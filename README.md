# 计价项目

一个用 Rust 实现的计价系统。

## 项目结构

该项目是一个 Rust 工作空间，包含以下 crate：

- `core`：核心功能

- `extension-base-schema`：基础模式定义

- `extension-bzhs`：BZHS 标准换算

- `extension-cost-collect`：费用计取

- `extension-djgc`：DJGC 单价构成

- `extension-fbfx-csxm`：FBFX-CSXM 分部分项措施项目

- `extension-other-project`：其他项目

- `extension-rcj`：RCJ 人材机

- `extension-rcj-collect`：RCJ 人材机费用汇总

- `shared`：共享工具和通用功能

## 开发环境设置

1. 安装 Rust（如果尚未安装）：

   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   ```

2. 构建项目：

   ```bash
   cargo build
   ```

3. 运行测试：

   ```bash
   cargo test
   ```

## 项目结构

```
price/
├── packages/
│   ├── core/                 # 核心功能
│   ├── extension-base-schema/ # 基础模式定义
│   ├── extension-bzhs/       # BZHS 标准换算扩展
│   ├── extension-cost-collect/ # 费用计取
│   ├── extension-djgc/       # DJGC 单价构成
│   ├── extension-fbfx-csxm/  # FBFX-CSXM 分部分项措施项目
│   ├── extension-other-project/ # 其他项目
│   ├── extension-rcj/        # RCJ 人材机
│   ├── extension-rcj-collect/ # RCJ 人材机费用汇总
│   └── shared/              # 共享工具
├── work/
│   ├── ys/       # 预算
└── Cargo.toml               # 工作空间清单
```

## 未完成

- [ ] nodejs绑定

- [ ] 完整项目案例

- [x] 查询引擎(查询缓存)

- [ ] bug fix，添加测试用例