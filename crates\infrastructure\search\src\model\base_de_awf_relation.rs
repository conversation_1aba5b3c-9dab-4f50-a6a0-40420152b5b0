/*
CREATE TABLE "base_de_awf_relation" (
  "sequence_nbr" text(19) NOT NULL,
  "de_code" text(255) NOT NULL,
  "de_name" text(255) NOT NULL,
  "library_code" text(255),
  "fee_file_id" text(19),
  "qf_code" text(255),
  "qf_name" text(255),
  "base_rate" real(16,6),
  "add_rate" real(16,6),
  "remark" text(1024),
  "status" integer(4),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeAwfRelation {
    pub sequence_nbr: String,
    pub de_code: String,
    pub de_name: String,
    pub library_code: Option<String>,
    pub fee_file_id: Option<String>,
    pub qf_code: Option<String>,
    pub qf_name: Option<String>,
    pub base_rate: Option<f64>,
    pub add_rate: Option<f64>,
    pub remark: Option<String>,
    pub status: Option<i32>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseDeAwfRelation {}, "base_de_awf_relation");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "de_code".to_string(),
            "de_name".to_string(),
            "library_code".to_string(),
            "fee_file_id".to_string(),
            "qf_code".to_string(),
            "qf_name".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "de_code".to_string(),
            "de_name".to_string(),
            "library_code".to_string(),
            "fee_file_id".to_string(),
            "qf_code".to_string(),
            "qf_name".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "de_code".to_string(),
            "de_name".to_string(),
            "library_code".to_string(),
            "fee_file_id".to_string(),
            "qf_code".to_string(),
            "qf_name".to_string(),
            "base_rate".to_string(),
            "add_rate".to_string(),
            "remark".to_string(),
            "status".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeAwfRelation>("base_de_awf_relation", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeAwfRelation>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_awf_relation").await?;
    let base_de_awf_relation_index = IndexClient::<BaseDeAwfRelation> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_awf_relation_index);
    Ok(search_service)
}
