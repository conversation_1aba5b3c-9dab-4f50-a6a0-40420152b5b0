/*
CREATE TABLE "base_de_library_2022" (
  "sequence_nbr" text(32) NOT NULL,
  "library_code" text(255) NOT NULL,
  "library_name" text(255) NOT NULL,
  "library_major" text(50),
  "sort_no" integer(11),
  "sort" integer(11),
  "release_date" text,
  "ss_province" text(20),
  "ss_city" text(20),
  "remark" text(1024),
  "de_standard_id" text(20),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeLibrary2022 {
    pub sequence_nbr: String,
    pub library_code: String,
    pub library_name: String,
    pub library_major: Option<String>,
    pub sort_no: Option<i32>,
    pub sort: Option<i32>,
    pub release_date: Option<String>,
    pub ss_province: Option<String>,
    pub ss_city: Option<String>,
    pub remark: Option<String>,
    pub de_standard_id: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseDeLibrary2022 {}, "base_de_library_2022");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "library_major".to_string(),
            "release_date".to_string(),
            "ss_province".to_string(),
            "ss_city".to_string(),
            "remark".to_string(),
            "de_standard_id".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "library_major".to_string(),
            "sort_no".to_string(),
            "sort".to_string(),
            "release_date".to_string(),
            "ss_province".to_string(),
            "ss_city".to_string(),
            "remark".to_string(),
            "de_standard_id".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "library_major".to_string(),
            "sort_no".to_string(),
            "sort".to_string(),
            "release_date".to_string(),
            "ss_province".to_string(),
            "ss_city".to_string(),
            "remark".to_string(),
            "de_standard_id".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeLibrary2022>("base_de_library_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeLibrary2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_library_2022").await?;
    let base_de_library_2022_index = IndexClient::<BaseDeLibrary2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_library_2022_index);
    Ok(search_service)
}
