/*
CREATE TABLE "base_de" (
  "sequence_nbr" text(19) NOT NULL,
  "library_code" text(255) NOT NULL,
  "library_name" text(255) NOT NULL,
  "cslb_code" text(255),
  "qf_code" text(255),
  "project_type" text(50),
  "de_name" text(255) NOT NULL,
  "de_code" text(255) NOT NULL,
  "de_identify" text(255),
  "is_zj" text(4),
  "unit" text(50),
  "price" real(16,6),
  "total" real(16,6),
  "rgf" real(16,2),
  "clf" real(16,2),
  "jxf" real(16,2),
  "glf" real(16,2),
  "quantity" text(255),
  "key_words1" text(255),
  "key_words2" text(255),
  "key_words3" text(255),
  "max_value" real(16,6),
  "min_value" real(16,6),
  "num_unit" text(50),
  "max_value2" real(16,6),
  "min_value2" real(16,6),
  "num_unit2" text(50),
  "max_value3" real(16,6),
  "min_value3" real(16,6),
  "num_unit3" text(50),
  "classify_level4" text(255),
  "classify_level3" text(255),
  "classify_level2" text(255),
  "classify_level1" text(255),
  "remark" text(1024),
  "status" text(4),
  "rate_name" text(255),
  "sort_no" integer(11),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  "zjcs_class_code" text(255),
  "zjcs_class_name" text(255),
  "sgzzcslb" text(255) NOT NULL,
  "value" integer(11),
  "charge_name" text(255),
  "classlevel_split_concat" text(255),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDe {
    pub sequence_nbr: String,
    pub library_code: String,
    pub library_name: String,
    pub cslb_code: Option<String>,
    pub qf_code: Option<String>,
    pub project_type: Option<String>,
    pub de_name: String,
    pub de_code: String,
    pub de_identify: Option<String>,
    pub is_zj: Option<String>,
    pub unit: Option<String>,
    pub price: Option<f64>,
    pub total: Option<f64>,
    pub rgf: Option<f64>,
    pub clf: Option<f64>,
    pub jxf: Option<f64>,
    pub glf: Option<f64>,
    pub quantity: Option<String>,
    pub key_words1: Option<String>,
    pub key_words2: Option<String>,
    pub key_words3: Option<String>,
    pub max_value: Option<f64>,
    pub min_value: Option<f64>,
    pub num_unit: Option<String>,
    pub max_value2: Option<f64>,
    pub min_value2: Option<f64>,
    pub num_unit2: Option<String>,
    pub max_value3: Option<f64>,
    pub min_value3: Option<f64>,
    pub num_unit3: Option<String>,
    pub classify_level4: Option<String>,
    pub classify_level3: Option<String>,
    pub classify_level2: Option<String>,
    pub classify_level1: Option<String>,
    pub remark: Option<String>,
    pub status: Option<String>,
    pub rate_name: Option<String>,
    pub sort_no: Option<i32>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
    pub zjcs_class_code: Option<String>,
    pub zjcs_class_name: Option<String>,
    pub sgzzcslb: String,
    pub value: Option<i32>,
    pub charge_name: Option<String>,
    pub classlevel_split_concat: Option<String>,
}
//crud!(BaseDe {}, "base_de");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "cslb_code".to_string(),
            "qf_code".to_string(),
            "project_type".to_string(),
            "de_name".to_string(),
            "de_code".to_string(),
            "de_identify".to_string(),
            "is_zj".to_string(),
            "unit".to_string(),
            "quantity".to_string(),
            "key_words1".to_string(),
            "key_words2".to_string(),
            "key_words3".to_string(),
            "num_unit".to_string(),
            "num_unit2".to_string(),
            "num_unit3".to_string(),
            "classify_level4".to_string(),
            "classify_level3".to_string(),
            "classify_level2".to_string(),
            "classify_level1".to_string(),
            "remark".to_string(),
            "status".to_string(),
            "rate_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "zjcs_class_code".to_string(),
            "zjcs_class_name".to_string(),
            "sgzzcslb".to_string(),
            "charge_name".to_string(),
            "classlevel_split_concat".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "cslb_code".to_string(),
            "qf_code".to_string(),
            "project_type".to_string(),
            "de_name".to_string(),
            "de_code".to_string(),
            "de_identify".to_string(),
            "is_zj".to_string(),
            "unit".to_string(),
            "quantity".to_string(),
            "key_words1".to_string(),
            "key_words2".to_string(),
            "key_words3".to_string(),
            "num_unit".to_string(),
            "num_unit2".to_string(),
            "num_unit3".to_string(),
            "classify_level4".to_string(),
            "classify_level3".to_string(),
            "classify_level2".to_string(),
            "classify_level1".to_string(),
            "remark".to_string(),
            "status".to_string(),
            "rate_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "zjcs_class_code".to_string(),
            "zjcs_class_name".to_string(),
            "sgzzcslb".to_string(),
            "charge_name".to_string(),
            "classlevel_split_concat".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "cslb_code".to_string(),
            "qf_code".to_string(),
            "project_type".to_string(),
            "de_name".to_string(),
            "de_code".to_string(),
            "de_identify".to_string(),
            "is_zj".to_string(),
            "unit".to_string(),
            "price".to_string(),
            "total".to_string(),
            "rgf".to_string(),
            "clf".to_string(),
            "jxf".to_string(),
            "glf".to_string(),
            "quantity".to_string(),
            "key_words1".to_string(),
            "key_words2".to_string(),
            "key_words3".to_string(),
            "max_value".to_string(),
            "min_value".to_string(),
            "num_unit".to_string(),
            "max_value2".to_string(),
            "min_value2".to_string(),
            "num_unit2".to_string(),
            "max_value3".to_string(),
            "min_value3".to_string(),
            "num_unit3".to_string(),
            "classify_level4".to_string(),
            "classify_level3".to_string(),
            "classify_level2".to_string(),
            "classify_level1".to_string(),
            "remark".to_string(),
            "status".to_string(),
            "rate_name".to_string(),
            "sort_no".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "zjcs_class_code".to_string(),
            "zjcs_class_name".to_string(),
            "sgzzcslb".to_string(),
            "value".to_string(),
            "charge_name".to_string(),
            "classlevel_split_concat".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDe>("base_de", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDe>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de").await?;
    let base_de_index = IndexClient::<BaseDe> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_index);
    Ok(search_service)
}
