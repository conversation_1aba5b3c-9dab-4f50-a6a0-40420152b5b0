use meili::init_search_client;
use meili::model::SearchConfig;
use search_service::PriceSearchService;
use shared::ContextHelper;

#[macro_use]
extern crate lazy_static;
pub mod meili;
pub mod model;
pub mod search_service;
pub mod sqlite;

pub async fn build_search_service(config: Option<SearchConfig>) {
    init_search_client(config);
    let search_service = PriceSearchService::create().await.unwrap();
    ContextHelper::set(search_service);
}
