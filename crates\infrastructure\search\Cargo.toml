[package]
name = "price-search"
version = { workspace = true }
edition = { workspace = true }
authors = { workspace = true }
description = "Meilisearch搜索引擎封装库"
license = { workspace = true }

[dependencies]
meilisearch-sdk = {version = "0.28.0", features = ["reqwest"]}
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
async-trait = { workspace = true }
lazy_static = { workspace = true }
uuid = { workspace = true } 
state={workspace = true}
shared={path = "../shared"}

rbs = {workspace = true}
rbatis = {workspace = true}
rbdc-sqlite = { workspace = true }  