use crossbeam::{
    channel::{Receiver, Sender, TryRecvError, bounded, unbounded},
    deque::{Inject<PERSON>, <PERSON><PERSON>, Stealer, Worker},
};
use std::{
    sync::Arc,
    thread::{self, <PERSON><PERSON><PERSON><PERSON><PERSON>},
    time::Duration,
};
use tokio::runtime::Runtime;

/// 工作任务类型
#[derive(Debug)]
pub enum WorkTask<T, R> {
    /// 处理任务
    Process {
        /// 任务数据
        data: T,
        /// 结果发送通道
        result_tx: Option<Sender<R>>,
    },
    /// 终止信号
    Terminate,
}

/// 工作窃取队列
pub struct WorkStealingQueue<T, R> {
    injector: Arc<Injector<WorkTask<T, R>>>,
    stealers: Arc<Vec<Stealer<WorkTask<T, R>>>>,
}

impl<T, R> Default for WorkStealingQueue<T, R>
where
    T: Send + 'static,
    R: Send + 'static,
{
    fn default() -> Self {
        Self::new()
    }
}

impl<T, R> WorkStealingQueue<T, R>
where
    T: Send + 'static,
    R: Send + 'static,
{
    pub fn new() -> Self {
        let injector = Arc::new(Injector::new());
        let stealers = Arc::new(Vec::new());
        Self { injector, stealers }
    }

    pub fn push(
        &self,
        task: WorkTask<T, R>,
    ) {
        self.injector.push(task);
    }

    pub fn add_worker(&mut self) -> Worker<WorkTask<T, R>> {
        let worker = Worker::new_fifo();
        let stealer = worker.stealer();
        Arc::get_mut(&mut self.stealers).unwrap().push(stealer);
        worker
    }
}

impl<T, R> Clone for WorkStealingQueue<T, R> {
    fn clone(&self) -> Self {
        Self { injector: self.injector.clone(), stealers: self.stealers.clone() }
    }
}

/// 工作窃取执行器配置
#[derive(Clone)]
pub struct WorkStealingExecutorConfig {
    /// 工作线程数量
    pub num_threads: usize,
    /// 任务处理超时时间
    pub task_timeout: Duration,
    /// 是否启用工作窃取
    pub enable_work_stealing: bool,
}

impl Default for WorkStealingExecutorConfig {
    fn default() -> Self {
        Self {
            num_threads: std::thread::available_parallelism().map(|p| p.get()).unwrap_or(num_cpus::get()),
            task_timeout: Duration::from_secs(30),
            enable_work_stealing: true,
        }
    }
}

/// 工作窃取执行器
pub struct WorkStealingExecutor<T, R, F>
where
    T: Send + 'static,
    R: Send + 'static,
    F: Fn(T) -> R + Send + Sync + 'static,
{
    config: WorkStealingExecutorConfig,
    work_queue: WorkStealingQueue<T, R>,
    #[allow(dead_code)]
    processor: Arc<F>,
    worker_handles: Vec<JoinHandle<()>>,
    result_rx: Receiver<R>,
}

impl<T, R, F> WorkStealingExecutor<T, R, F>
where
    T: Send + 'static,
    R: Send + 'static,
    F: Fn(T) -> R + Send + Sync + 'static,
{
    pub fn new(
        config: WorkStealingExecutorConfig,
        processor: F,
    ) -> Self {
        let work_queue = WorkStealingQueue::new();
        let (result_tx, result_rx) = unbounded();
        let processor = Arc::new(processor);
        let mut worker_handles = Vec::with_capacity(config.num_threads);

        for _ in 0..config.num_threads {
            let mut work_queue = work_queue.clone();
            let result_tx = result_tx.clone();
            let processor = processor.clone();
            let config = config.clone();

            let handle = thread::spawn(move || {
                let rt = Runtime::new().expect("创建tokio runtime失败");
                let worker = work_queue.add_worker();

                rt.block_on(async move {
                    loop {
                        // 首先尝试从本地队列获取任务
                        let task = worker.pop().or_else(|| {
                            if config.enable_work_stealing {
                                // 尝试从其他线程窃取任务
                                work_queue.stealers.iter().find_map(|stealer| match stealer.steal() {
                                    Steal::Success(task) => Some(task),
                                    _ => None,
                                })
                            } else {
                                None
                            }
                        });

                        match task {
                            Some(WorkTask::Process { data, result_tx: task_tx }) => {
                                let result = processor(data);
                                if let Some(tx) = task_tx {
                                    let _ = tx.send(result);
                                } else {
                                    let _ = result_tx.send(result);
                                }
                            },
                            Some(WorkTask::Terminate) => break,
                            None => {
                                // 没有任务时短暂休眠，避免CPU空转
                                tokio::time::sleep(Duration::from_millis(10)).await;
                            },
                        }
                    }
                });
            });

            worker_handles.push(handle);
        }

        Self { config, work_queue, processor, worker_handles, result_rx }
    }

    pub fn submit(
        &self,
        data: T,
    ) -> Option<Receiver<R>> {
        let (tx, rx) = bounded(1);
        self.work_queue.push(WorkTask::Process { data, result_tx: Some(tx) });
        Some(rx)
    }

    pub fn submit_batch(
        &self,
        data: Vec<T>,
    ) -> Vec<Receiver<R>> {
        data.into_iter().filter_map(|item| self.submit(item)).collect()
    }

    pub fn try_recv_result(&self) -> Result<R, TryRecvError> {
        self.result_rx.try_recv()
    }

    pub fn shutdown(&mut self) {
        for _ in 0..self.config.num_threads {
            self.work_queue.push(WorkTask::Terminate);
        }

        for handle in self.worker_handles.drain(..) {
            let _ = handle.join();
        }
    }
}

impl<T, R, F> Drop for WorkStealingExecutor<T, R, F>
where
    T: Send + 'static,
    R: Send + 'static,
    F: Fn(T) -> R + Send + Sync + 'static,
{
    fn drop(&mut self) {
        self.shutdown();
    }
}
