use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseAnwenRate2022 {
    pub sequence_nbr: String,
    pub project_location: Option<String>,
    pub road_surface_num: Option<String>,
    pub floor_space: Option<String>,
    pub municipal_construction_cost: Option<String>,
    pub anwen_rate_ybjs: Option<f64>,
    pub anwen_rate_jyjs: Option<f64>,
    pub library_code: Option<String>,
    pub del_flag: Option<String>,
    pub remark: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}

//crud!(BaseAnwenRate2022 {}, "base_anwen_rate_2022");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "project_location".to_string(),
            "road_surface_num".to_string(),
            "floor_space".to_string(),
            "municipal_construction_cost".to_string(),
            "anwen_rate".to_string(),
            "library_code".to_string(),
            "del_flag".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "project_location".to_string(),
            "road_surface_num".to_string(),
            "floor_space".to_string(),
            "municipal_construction_cost".to_string(),
            "anwen_rate".to_string(),
            "library_code".to_string(),
            "del_flag".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "project_location".to_string(),
            "road_surface_num".to_string(),
            "floor_space".to_string(),
            "municipal_construction_cost".to_string(),
            "anwen_rate".to_string(),
            "library_code".to_string(),
            "del_flag".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseAnwenRate2022>("base_anwen_rate_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseAnwenRate2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_anwen_rate_2022").await?;
    let base_anwen_rate_2022_index = IndexClient::<BaseAnwenRate2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_anwen_rate_2022_index);
    Ok(search_service)
}
