/*
CREATE TABLE "base_de_rcj_bs" (
  "sequence_nbr" text(19) NOT NULL,
  "library_code" text(255) NOT NULL,
  "rcj_id" text(19) NOT NULL,
  "material_code" text(255) NOT NULL,
  "material_name" text(255) NOT NULL,
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64)
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeRcjBs {
    pub sequence_nbr: String,
    pub library_code: String,
    pub rcj_id: String,
    pub material_code: String,
    pub material_name: String,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseDeRcjBs {}, "base_de_rcj_bs");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "rcj_id".to_string(),
            "material_code".to_string(),
            "material_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "rcj_id".to_string(),
            "material_code".to_string(),
            "material_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "rcj_id".to_string(),
            "material_code".to_string(),
            "material_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeRcjBs>("base_de_rcj_bs", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeRcjBs>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_rcj_bs").await?;
    let base_de_rcj_bs_index = IndexClient::<BaseDeRcjBs> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_rcj_bs_index);
    Ok(search_service)
}
