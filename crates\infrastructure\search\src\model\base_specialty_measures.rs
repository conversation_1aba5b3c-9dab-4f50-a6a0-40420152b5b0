/*
CREATE TABLE "base_specialty_measures" (
  "sequence_nbr" varchar NOT NULL,
  "template_name" varchar NOT NULL,
  "quota_standard" varchar,
  "specialty_code" varchar,
  "cslb_name" varchar,
  "measures_type" varchar,
  "bd_code" varchar,
  "rec_user_code" varchar,
  "rec_date" varchar,
  "rec_status" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "description" varchar,
  "data_order" integer,
  PRIMARY KEY ("sequence_nbr", "template_name")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseSpecialtyMeasures {
    pub sequence_nbr: String,
    pub template_name: String,
    pub quota_standard: Option<String>,
    pub specialty_code: Option<String>,
    pub cslb_name: Option<String>,
    pub measures_type: Option<String>,
    pub bd_code: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub data_order: Option<i32>,
}
//crud!(BaseSpecialtyMeasures {}, "base_specialty_measures");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "template_name".to_string(),
            "quota_standard".to_string(),
            "specialty_code".to_string(),
            "cslb_name".to_string(),
            "measures_type".to_string(),
            "bd_code".to_string(),
            "data_order".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "template_name".to_string(),
            "quota_standard".to_string(),
            "specialty_code".to_string(),
            "cslb_name".to_string(),
            "measures_type".to_string(),
            "bd_code".to_string(),
            "data_order".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "template_name".to_string(),
            "quota_standard".to_string(),
            "specialty_code".to_string(),
            "cslb_name".to_string(),
            "measures_type".to_string(),
            "bd_code".to_string(),
            "data_order".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseSpecialtyMeasures>("base_specialty_measures", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseSpecialtyMeasures>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_specialty_measures").await?;
    let base_list_index = IndexClient::<BaseSpecialtyMeasures> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
