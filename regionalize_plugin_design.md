# 地区化插件策略模式设计文档（公共接口库架构版）

## 1. 概述

基于ModuForge-RS工程造价系统的插件架构，设计一套**基于公共接口库**的地区化插件系统。通过创建独立的`price-interfaces`公共接口库，实现插件和核心模块之间的依赖解耦，每个插件都有自己独立的策略接口和实现，通过统一的策略工厂进行注册和管理。

## 1.1 设计原则

### 1.1.1 依赖反转原则
- **公共接口库**：所有接口定义放在独立的`price-interfaces`库中
- **插件和Core解耦**：插件和核心模块都依赖公共接口库，避免循环依赖
- **接口统一管理**：策略接口、错误类型、配置类型统一维护

### 1.1.2 插件策略独立性
- **每个插件独立策略**：CSXM、FBFX、DJGC、RCJ等插件各自定义策略接口
- **单一职责原则**：每个策略只负责一个插件的地区化逻辑
- **接口隔离原则**：插件只依赖自己需要的策略接口

### 1.1.3 统一管理机制
- **策略工厂注册**：统一的工厂管理所有插件策略的创建
- **地区配置驱动**：通过配置文件驱动不同地区的策略实现
- **运行时注入**：插件创建时注入对应的地区策略

## 2. 架构设计

### 2.1 公共接口库（price-interfaces）

```
price-interfaces/
├── src/
│   ├── lib.rs              # 主入口，重导出所有公共接口
│   ├── strategies.rs       # 策略接口定义
│   ├── contexts.rs         # 上下文类型定义
│   ├── results.rs          # 计算结果类型定义
│   ├── errors.rs           # 错误类型定义
│   ├── config.rs           # 配置类型定义
│   └── factories.rs        # 策略工厂接口定义
└── Cargo.toml
```

### 2.2 依赖关系图

```
┌─────────────────┐
│  price-budget-  │
│      core       │
│                 │
└─────────┬───────┘
          │ 依赖
          ▼
┌─────────────────┐
│ price-interfaces│
│  (公共接口库)    │
│                 │
└─────────┬───────┘
          ▲ 依赖
          │
┌─────────┴───────┐
│   插件模块       │
│ extension-*     │
│                 │
└─────────────────┘
```

## 3. 独立插件策略接口设计

### 3.1 CSXM插件策略接口

```rust
// 在 price-interfaces/src/strategies.rs 中定义
use std::sync::Arc;
use async_trait::async_trait;
use mf_state::{State, Transaction};
use crate::{contexts::*, results::*, errors::*};

/// CSXM（措施项目）地区化策略接口
#[async_trait]
pub trait CsxmRegionalStrategy: Send + Sync + std::fmt::Debug {
    /// 计算措施项目成本
    async fn calculate_cost(&self, ctx: &CsxmCalculationContext) -> Result<CsxmCalculationResult, CsxmError>;
    
    /// 验证计算结果
    async fn validate_result(&self, ctx: &CsxmValidationContext) -> Result<bool, CsxmError>;
    
    /// 过滤是否需要处理此事务
    fn should_process(&self, ctx: &CsxmFilterContext) -> bool;
    
    /// 获取策略名称（用于日志和调试）
    fn strategy_name(&self) -> &str;
    
    /// 获取支持的地区代码列表
    fn supported_regions(&self) -> &[String];
}
```

### 3.2 FBFX插件策略接口

```rust
/// FBFX（分部分项）地区化策略接口
#[async_trait]
pub trait FbfxRegionalStrategy: Send + Sync + std::fmt::Debug {
    /// 计算分部分项成本
    async fn calculate_cost(&self, ctx: &FbfxCalculationContext) -> Result<FbfxCalculationResult, FbfxError>;
    
    /// 验证计算结果
    async fn validate_result(&self, ctx: &FbfxValidationContext) -> Result<bool, FbfxError>;
    
    /// 过滤是否需要处理此事务
    fn should_process(&self, ctx: &FbfxFilterContext) -> bool;
    
    /// 获取策略名称
    fn strategy_name(&self) -> &str;
    
    /// 获取支持的地区代码列表
    fn supported_regions(&self) -> &[String];
}
```

### 3.3 DJGC插件策略接口

```rust
/// DJGC（单价构成）地区化策略接口
#[async_trait]
pub trait DjgcRegionalStrategy: Send + Sync + std::fmt::Debug {
    /// 计算单价构成
    async fn calculate_composition(&self, ctx: &DjgcCalculationContext) -> Result<DjgcCalculationResult, DjgcError>;
    
    /// 验证计算结果
    async fn validate_result(&self, ctx: &DjgcValidationContext) -> Result<bool, DjgcError>;
    
    /// 过滤是否需要处理此事务
    fn should_process(&self, ctx: &DjgcFilterContext) -> bool;
    
    /// 获取策略名称
    fn strategy_name(&self) -> &str;
    
    /// 获取支持的地区代码列表
    fn supported_regions(&self) -> &[String];
}
```

### 3.4 RCJ插件策略接口

```rust
/// RCJ（人材机）地区化策略接口
#[async_trait]
pub trait RcjRegionalStrategy: Send + Sync + std::fmt::Debug {
    /// 计算人材机价格
    async fn calculate_prices(&self, ctx: &RcjCalculationContext) -> Result<RcjCalculationResult, RcjError>;
    
    /// 验证计算结果
    async fn validate_result(&self, ctx: &RcjValidationContext) -> Result<bool, RcjError>;
    
    /// 过滤是否需要处理此事务
    fn should_process(&self, ctx: &RcjFilterContext) -> bool;
    
    /// 获取策略名称
    fn strategy_name(&self) -> &str;
    
    /// 获取支持的地区代码列表
    fn supported_regions(&self) -> &[String];
}
```

## 4. 统一策略工厂设计

### 4.1 地区策略工厂接口

```rust
// 在 price-interfaces/src/factories.rs 中定义

/// 地区策略工厂统一接口
#[async_trait]
pub trait RegionalStrategyFactory: Send + Sync + std::fmt::Debug {
    /// 创建CSXM策略实例
    async fn create_csxm_strategy(&self, region_code: &str, config: &RegionConfig) -> Result<Arc<dyn CsxmRegionalStrategy>, StrategyError>;
    
    /// 创建FBFX策略实例
    async fn create_fbfx_strategy(&self, region_code: &str, config: &RegionConfig) -> Result<Arc<dyn FbfxRegionalStrategy>, StrategyError>;
    
    /// 创建DJGC策略实例
    async fn create_djgc_strategy(&self, region_code: &str, config: &RegionConfig) -> Result<Arc<dyn DjgcRegionalStrategy>, StrategyError>;
    
    /// 创建RCJ策略实例
    async fn create_rcj_strategy(&self, region_code: &str, config: &RegionConfig) -> Result<Arc<dyn RcjRegionalStrategy>, StrategyError>;
    
    /// 创建完整的地区策略集合
    async fn create_region_strategies(&self, region_code: &str, config: &RegionConfig) -> Result<RegionStrategies, StrategyError>;
    
    /// 获取支持的地区代码列表
    fn get_supported_regions(&self) -> Vec<String>;
    
    /// 获取工厂名称（用于注册和日志）
    fn get_factory_name(&self) -> &str;
    
    /// 验证地区配置是否有效
    fn validate_config(&self, region_code: &str, config: &RegionConfig) -> Result<(), StrategyError>;
    
    /// 获取工厂优先级（数字越小优先级越高）
    fn get_priority(&self) -> u32 { 100 }
}
```

### 4.2 策略工厂注册表

```rust
/// 策略工厂注册表
#[derive(Debug)]
pub struct StrategyFactoryRegistry {
    /// 按工厂名称索引的工厂实例
    factories: HashMap<String, Arc<dyn RegionalStrategyFactory>>,
    /// 按地区代码索引的工厂实例（用于快速查找）
    region_factory_map: HashMap<String, Arc<dyn RegionalStrategyFactory>>,
}

impl StrategyFactoryRegistry {
    pub fn new() -> Self { /* ... */ }
    
    /// 注册策略工厂
    pub fn register_factory(&mut self, factory: Arc<dyn RegionalStrategyFactory>) -> Result<(), StrategyError> { /* ... */ }
    
    /// 获取支持指定地区的工厂
    pub fn get_factory_for_region(&self, region_code: &str) -> Option<Arc<dyn RegionalStrategyFactory>> { /* ... */ }
    
    /// 创建指定地区的所有策略
    pub async fn create_strategies_for_region(&self, region_code: &str, config: &RegionConfig) -> Result<RegionStrategies, StrategyError> { /* ... */ }
}
```

## 5. 地区配置系统

### 5.1 地区配置结构

```rust
// 在 price-interfaces/src/config.rs 中定义

/// 地区配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegionConfig {
    /// 地区代码（如：110000为北京）
    pub region_code: String,
    /// 地区名称
    pub region_name: String,
    /// 配置版本
    pub config_version: String,
    /// 是否启用该地区
    pub enabled: bool,
    /// CSXM配置
    pub csxm_config: CsxmRegionConfig,
    /// FBFX配置
    pub fbfx_config: FbfxRegionConfig,
    /// DJGC配置
    pub djgc_config: DjgcRegionConfig,
    /// RCJ配置
    pub rcj_config: RcjRegionConfig,
    /// 扩展参数
    pub extensions: HashMap<String, serde_json::Value>,
}
```

### 5.2 插件特定配置

每个插件都有自己独立的配置结构，例如：

```rust
/// CSXM地区配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CsxmRegionConfig {
    /// 安全措施费费率
    pub safety_fee_rate: f64,
    /// 环境保护费费率
    pub environmental_fee_rate: f64,
    /// 文明施工费费率
    pub civilized_construction_rate: f64,
    /// 项目类型费率调整系数
    pub project_type_adjustments: HashMap<String, f64>,
    /// 地区位置调整系数
    pub location_adjustments: HashMap<String, f64>,
}
```

## 6. 具体实现示例

### 6.1 北京CSXM策略实现

```rust
// 在具体的插件中实现
use price_interfaces::*;

#[derive(Debug)]
pub struct BeijingCsxmStrategy {
    region_codes: Vec<String>,
}

#[async_trait]
impl CsxmRegionalStrategy for BeijingCsxmStrategy {
    async fn calculate_cost(&self, ctx: &CsxmCalculationContext) -> Result<CsxmCalculationResult, CsxmError> {
        let mut result = CsxmCalculationResult::new();
        
        // 北京特有的计算逻辑
        result.safety_measures_cost = ctx.base_cost * 0.015; // 1.5%
        result.environmental_cost = ctx.base_cost * 0.01;    // 1%
        result.civilized_construction_cost = ctx.base_cost * 0.01; // 1%
        
        // 根据项目类型调整
        if ctx.project_type == "地铁工程" {
            result.safety_measures_cost *= 1.2; // 提高20%
        }
        
        // 根据建筑高度调整
        if let Some(height) = ctx.building_height {
            if height > 100.0 {
                result.safety_measures_cost *= 1.1; // 超高层建筑增加10%
            }
        }
        
        result.total_cost = result.safety_measures_cost + 
                           result.environmental_cost + 
                           result.civilized_construction_cost;
        
        result.add_remark("适用北京地区标准".to_string());
        Ok(result)
    }
    
    async fn validate_result(&self, ctx: &CsxmValidationContext) -> Result<bool, CsxmError> {
        // 北京特有的验证逻辑
        Ok(ctx.calculated_cost > 0.0)
    }
    
    fn should_process(&self, ctx: &CsxmFilterContext) -> bool {
        // 检查是否为北京地区的事务
        true
    }
    
    fn strategy_name(&self) -> &str {
        "北京CSXM策略"
    }
    
    fn supported_regions(&self) -> &[String] {
        &self.region_codes
    }
}
```

### 6.2 地区策略工厂实现

```rust
#[derive(Debug)]
pub struct BeijingStrategyFactory;

#[async_trait]
impl RegionalStrategyFactory for BeijingStrategyFactory {
    async fn create_csxm_strategy(&self, region_code: &str, config: &RegionConfig) -> Result<Arc<dyn CsxmRegionalStrategy>, StrategyError> {
        if region_code != "110000" {
            return Err(StrategyError::UnsupportedRegion(region_code.to_string()));
        }
        
        Ok(Arc::new(BeijingCsxmStrategy {
            region_codes: vec!["110000".to_string()],
        }))
    }
    
    async fn create_fbfx_strategy(&self, region_code: &str, config: &RegionConfig) -> Result<Arc<dyn FbfxRegionalStrategy>, StrategyError> {
        // 创建北京FBFX策略
        Ok(Arc::new(BeijingFbfxStrategy::new(region_code, config)?))
    }
    
    // ... 其他策略创建方法
    
    fn get_supported_regions(&self) -> Vec<String> {
        vec!["110000".to_string()] // 北京
    }
    
    fn get_factory_name(&self) -> &str {
        "北京地区策略工厂"
    }
    
    fn validate_config(&self, region_code: &str, config: &RegionConfig) -> Result<(), StrategyError> {
        if region_code != "110000" {
            return Err(StrategyError::UnsupportedRegion(region_code.to_string()));
        }
        config.validate().map_err(|e| StrategyError::ValidationFailed(e.to_string()))?;
        Ok(())
    }
    
    fn get_priority(&self) -> u32 {
        10 // 高优先级
    }
}
```

## 7. 系统初始化和使用

### 7.1 策略工厂注册

```rust
// 在系统初始化时注册所有地区策略工厂
pub async fn initialize_regional_strategies() -> Result<Arc<StrategyFactoryRegistry>, StrategyError> {
    let mut registry = StrategyFactoryRegistry::new();
    
    // 注册北京策略工厂
    registry.register_factory(Arc::new(BeijingStrategyFactory))?;
    
    // 注册上海策略工厂
    registry.register_factory(Arc::new(ShanghaiStrategyFactory))?;
    
    // 注册广东策略工厂
    registry.register_factory(Arc::new(GuangdongStrategyFactory))?;
    
    Ok(Arc::new(registry))
}
```

### 7.2 插件中使用策略

```rust
// 在插件的实现中
use price_interfaces::*;

impl PluginTrait for CsxmPlugin {
    async fn generate_transaction(&self, state: &State, transaction: &Transaction) -> ForgeResult<Vec<Command>> {
        let region_code = extract_region_code(transaction)?;
        let config = load_region_config(&region_code)?;
        
        // 获取当前地区的CSXM策略
        let strategy = self.strategy_registry
            .create_strategies_for_region(&region_code, &config)
            .await?
            .csxm_strategy;
        
        // 构建计算上下文
        let ctx = CsxmCalculationContext {
            state: Arc::new(state.clone()),
            transaction: Arc::new(transaction.clone()),
            base_cost: extract_base_cost(transaction)?,
            project_type: extract_project_type(transaction)?,
            project_location: extract_location(transaction)?,
            building_height: extract_building_height(transaction)?,
            parameters: HashMap::new(),
        };
        
        // 执行策略计算
        let result = strategy.calculate_cost(&ctx).await?;
        
        // 转换为Commands
        let commands = convert_result_to_commands(result)?;
        
        Ok(commands)
    }
}
```

## 8. 优势与特点

### 8.1 架构优势

1. **依赖解耦**：通过公共接口库彻底解决插件和核心模块间的循环依赖
2. **接口统一**：所有策略接口、错误类型、配置类型集中管理
3. **版本控制**：接口库独立版本管理，便于API演进
4. **类型安全**：编译时保证接口的一致性和正确性

### 8.2 插件设计优势

1. **策略独立**：每个插件的策略接口独立，互不干扰
2. **单一职责**：每个策略只负责一个插件的地区化逻辑
3. **高内聚低耦合**：插件内部逻辑高度聚合，对外依赖最小化
4. **易于扩展**：新增地区或插件不影响现有实现

### 8.3 管理优势

1. **统一注册**：所有策略通过统一工厂注册和管理
2. **优先级控制**：支持多个工厂对同一地区的覆盖，按优先级选择
3. **配置驱动**：地区差异通过配置文件控制，无需修改代码
4. **运行时切换**：支持运行时动态切换地区策略

## 9. 实现总结

基于公共接口库的地区化插件策略设计实现了：

1. **完全的依赖解耦**：`price-interfaces`公共库解决了所有依赖问题
2. **插件策略独立**：每个插件都有独立的策略接口和实现
3. **统一工厂管理**：通过`RegionalStrategyFactory`和注册表统一管理所有策略
4. **灵活的配置系统**：支持复杂的地区配置和插件特定参数
5. **完整的类型安全**：从策略接口到错误处理的完整类型体系

这种设计既保持了ModuForge-RS框架的插件化优势，又实现了高度灵活的地区化支持，为后续扩展更多地区和插件提供了坚实的架构基础。