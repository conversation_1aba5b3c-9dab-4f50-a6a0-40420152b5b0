/*
CREATE TABLE "base_cost_summary" (
  "sequence_nbr" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
  "mark_code" text(32),
  "code" text(32) NOT NULL,
  "sort_num" text(16),
  "name" text(255) NOT NULL,
  "calculate_formula" text(128),
  "instructions" text(512),
  "type" text(64),
  "tax_calculation_method" integer(4),
  "remark" text(255),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" integer(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64)
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};
#[derive(Debug, Serialize, Deserialize)]
pub struct BaseCostSummary {
    pub sequence_nbr: i32,
    pub mark_code: Option<String>,
    pub code: String,
    pub sort_num: Option<String>,
    pub name: String,
    pub calculate_formula: Option<String>,
    pub instructions: Option<String>,
    pub r#type: Option<String>,
    pub tax_calculation_method: Option<i32>,
    pub remark: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<i32>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}

//crud!(BaseCostSummary {}, "base_cost_summary");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "type".to_string(),
            "mark_code".to_string(),
            "code".to_string(),
            "name".to_string(),
            "sort_num".to_string(),
            "calculate_formula".to_string(),
            "instructions".to_string(),
            "tax_calculation_method".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "type".to_string(),
            "mark_code".to_string(),
            "code".to_string(),
            "name".to_string(),
            "sort_num".to_string(),
            "calculate_formula".to_string(),
            "instructions".to_string(),
            "tax_calculation_method".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "type".to_string(),
            "mark_code".to_string(),
            "code".to_string(),
            "name".to_string(),
            "sort_num".to_string(),
            "calculate_formula".to_string(),
            "instructions".to_string(),
            "tax_calculation_method".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseCostSummary>("base_cost_summary", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseCostSummary>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_cost_summary").await?;
    let base_cost_summary_index = IndexClient::<BaseCostSummary> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_cost_summary_index);
    Ok(search_service)
}
