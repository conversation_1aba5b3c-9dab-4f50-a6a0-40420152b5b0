use async_trait::async_trait;

use meilisearch_sdk::{
    client::Client,
    indexes::{Index, IndexesQuery},
    settings::Settings,
    task_info::TaskInfo,
};
use serde::{Serialize, de::DeserializeOwned};
use serde_json::Value;
use std::marker::PhantomData;

use crate::meili::{
    error::SearchResult,
    model::{IndexSettings, SearchConfig, SearchOptions},
};

/// Meilisearch 客户端封装
pub struct SearchClient {
    client: Client,
}

impl SearchClient {
    /// 创建新的搜索客户端
    pub fn new(config: SearchConfig) -> Self {
        let client = Client::new(config.host, config.api_key).unwrap();
        Self { client }
    }

    /// 获取或创建索引
    pub async fn get_or_create_index(
        &self,
        uid: &str,
        primary_key: Option<&str>,
    ) -> SearchResult<Index> {
        // 先尝试获取索引是否存在
        if (self.client.get_index(uid).await).is_err() {
            // 不存在则创建
            let _ = self.client.create_index(uid, primary_key).await?;
        }

        // 返回索引对象
        Ok(self.client.index(uid))
    }
    /// 获取索引
    pub async fn get_index(
        &self,
        uid: &str,
    ) -> SearchResult<Index> {
        // 验证索引是否存在
        let _ = self.client.get_index(uid).await?;
        // 获取索引实例
        Ok(self.client.index(uid))
    }

    /// 删除索引
    pub async fn delete_index(
        &self,
        uid: &str,
    ) -> SearchResult<TaskInfo> {
        let task = self.client.delete_index(uid).await?;
        Ok(task)
    }

    /// 更新索引设置
    pub async fn update_index_settings(
        &self,
        uid: &str,
        settings: &IndexSettings,
    ) -> SearchResult<TaskInfo> {
        let index = self.get_index(uid).await?;

        // 将自定义设置转换为Meilisearch设置
        let mut mei_settings = Settings::new();

        if let Some(attrs) = &settings.searchable_attributes {
            let string_slice: Vec<&str> = attrs.iter().map(|s| s.as_str()).collect();
            mei_settings = mei_settings.with_searchable_attributes(string_slice);
        }

        if let Some(attrs) = &settings.filterable_attributes {
            let string_slice: Vec<&str> = attrs.iter().map(|s| s.as_str()).collect();
            mei_settings = mei_settings.with_filterable_attributes(string_slice);
        }

        if let Some(attrs) = &settings.sortable_attributes {
            let string_slice: Vec<&str> = attrs.iter().map(|s| s.as_str()).collect();
            mei_settings = mei_settings.with_sortable_attributes(string_slice);
        }

        if let Some(attrs) = &settings.displayed_attributes {
            let string_slice: Vec<&str> = attrs.iter().map(|s| s.as_str()).collect();
            mei_settings = mei_settings.with_displayed_attributes(string_slice);
        }

        let task = index.set_settings(&mei_settings).await?;
        Ok(task)
    }

    /// 创建索引并执行索引器
    pub async fn create_index<T>(
        &self,
        uid: &str,
        settings: Option<IndexSettings>,
    ) -> SearchResult<IndexClient<T>>
    where
        T: Serialize + DeserializeOwned + Sync + Send + 'static,
    {
        let primary_key = settings.as_ref().and_then(|s| s.primary_key.as_deref());
        let index = self.get_or_create_index(uid, primary_key).await?;

        // 设置索引配置
        if let Some(settings) = settings {
            let _ = self.update_index_settings(uid, &settings).await?;
        }

        Ok(IndexClient { index, _phantom: PhantomData })
    }

    /// 获取所有索引
    /// 默认最多返回1000个索引
    pub async fn get_all_index(&self) -> SearchResult<Vec<Index>> {
        let mut query = IndexesQuery::new(&self.client);
        query.limit = Some(1000);
        let all_index = self.client.get_indexes_with(&query).await.unwrap();
        let index_arr = all_index.results;
        Ok(index_arr)
    }
}

/// 索引客户端封装
pub struct IndexClient<T> {
    pub index: Index,
    pub _phantom: PhantomData<T>,
}

impl<T> IndexClient<T>
where
    T: Serialize + DeserializeOwned + Sync + Send + 'static,
{
    /// 添加文档
    pub async fn add_documents(
        &self,
        documents: Vec<T>,
        primary_key: Option<&str>,
    ) -> SearchResult<TaskInfo> {
        let task = self.index.add_documents(&documents, primary_key).await?;
        Ok(task)
    }

    /// 添加或替换文档
    pub async fn add_or_replace_documents(
        &self,
        documents: Vec<T>,
        primary_key: Option<&str>,
    ) -> SearchResult<TaskInfo> {
        // 使用 upsert 接口替代
        let task = self.index.add_documents(&documents, primary_key).await?;
        Ok(task)
    }

    /// 添加或更新文档
    pub async fn add_or_update_documents(
        &self,
        documents: Vec<T>,
        primary_key: Option<&str>,
    ) -> SearchResult<TaskInfo> {
        // 使用 upsert 接口替代
        let task = self.index.add_documents(&documents, primary_key).await?;
        Ok(task)
    }

    /// 通过 ID 获取文档
    pub async fn get_document(
        &self,
        id: &str,
    ) -> SearchResult<T> {
        let document = self.index.get_document::<T>(id).await?;
        Ok(document)
    }

    /// 通过 ID 删除文档
    pub async fn delete_document(
        &self,
        id: &str,
    ) -> SearchResult<TaskInfo> {
        let task = self.index.delete_document(id).await?;
        Ok(task)
    }

    /// 批量删除文档
    pub async fn delete_documents(
        &self,
        ids: &[&str],
    ) -> SearchResult<TaskInfo> {
        let task = self.index.delete_documents(ids).await?;
        Ok(task)
    }

    /// 删除所有文档
    pub async fn delete_all_documents(&self) -> SearchResult<TaskInfo> {
        let task = self.index.delete_all_documents().await?;
        Ok(task)
    }

    /// 搜索文档
    pub async fn search(
        &self,
        options: &SearchOptions,
    ) -> SearchResult<crate::meili::model::SearchResult<T>> {
        // 创建基本查询构建器
        let mut query = self.index.search();

        // 设置查询字符串
        let query_str = options.query.clone();
        let limit = options.limit.unwrap_or(20);
        let offset = options.offset.unwrap_or(0);
        let filter_value = options.filter.clone().unwrap_or_default();

        let query = query.with_query(&query_str).with_limit(limit).with_offset(offset).with_filter(&filter_value);

        // 执行搜索，尝试处理响应解析错误
        let search_results = match query.execute::<Value>().await {
            Ok(results) => {
                // 创建新的结果集合，修复类型转换
                let hits: Vec<T> = results.hits.into_iter().map(|hit| serde_json::from_value::<T>(hit.result).unwrap()).collect();

                // 转换为自定义结果格式
                crate::meili::model::SearchResult {
                    hits,
                    total_hits: results.estimated_total_hits.unwrap_or(0),
                    offset: results.offset.unwrap_or(0),
                    limit: results.limit.unwrap_or(20),
                    processing_time_ms: results.processing_time_ms,
                    query: query_str.clone(),
                }
            },
            Err(e) => {
                // 如果是解析错误，尝试获取原始响应
                if e.to_string().contains("failed to parse response") {
                    // 返回空结果
                    return Ok(crate::meili::model::SearchResult {
                        hits: Vec::new(),
                        total_hits: 0,
                        offset: options.offset.unwrap_or(0),
                        limit: options.limit.unwrap_or(20),
                        processing_time_ms: 0,
                        query: query_str.clone(),
                    });
                } else {
                    return Err(e.into());
                }
            },
        };

        Ok(search_results)
    }

    /// 获取索引状态
    pub async fn get_stats(&self) -> SearchResult<Value> {
        let stats = self.index.get_stats().await?;
        // 手动构建JSON值
        let json_value = serde_json::json!({
            "numberOfDocuments": stats.number_of_documents,
            "isIndexing": stats.is_indexing,
            "fieldDistribution": stats.field_distribution
        });
        Ok(json_value)
    }
}

/// 搜索服务接口
#[async_trait]
pub trait SearchService<T: Serialize + DeserializeOwned + Sync + Send + 'static> {
    /// 添加数据到索引
    async fn add_to_index(
        &self,
        documents: Vec<T>,
    ) -> SearchResult<()>;

    /// 执行搜索
    async fn search(
        &self,
        options: &SearchOptions,
    ) -> SearchResult<crate::meili::model::SearchResult<T>>;

    /// 通过 ID 获取数据
    async fn get_by_id(
        &self,
        id: &str,
    ) -> SearchResult<T>;

    /// 通过 ID 删除数据
    async fn delete_by_id(
        &self,
        id: &str,
    ) -> SearchResult<()>;
}

/// 基本搜索服务实现
pub struct BasicSearchService<T> {
    index_client: IndexClient<T>,
}

impl<T> BasicSearchService<T>
where
    T: Serialize + DeserializeOwned + Sync + Send + 'static,
{
    /// 创建新的搜索服务
    pub fn new(index_client: IndexClient<T>) -> Self {
        // 从索引中提取信

        Self { index_client }
    }
}

#[async_trait]
impl<T> SearchService<T> for BasicSearchService<T>
where
    T: Serialize + DeserializeOwned + Sync + Send + 'static,
{
    async fn add_to_index(
        &self,
        documents: Vec<T>,
    ) -> SearchResult<()> {
        let _task = self.index_client.add_or_replace_documents(documents, None).await?;
        // 任务成功完成
        Ok(())
    }

    async fn search(
        &self,
        options: &SearchOptions,
    ) -> SearchResult<crate::meili::model::SearchResult<T>> {
        self.index_client.search(options).await
    }

    async fn get_by_id(
        &self,
        id: &str,
    ) -> SearchResult<T> {
        self.index_client.get_document(id).await
    }

    async fn delete_by_id(
        &self,
        id: &str,
    ) -> SearchResult<()> {
        let _task = self.index_client.delete_document(id).await?;
        // 任务成功完成
        Ok(())
    }
}
