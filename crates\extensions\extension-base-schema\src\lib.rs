
pub mod command;
pub mod nodes;
pub mod project_structure_plugin;
pub mod router;

// 重新导出主要的结构体和工厂
pub use nodes::project_structure::{
    ProjectNode, SingleProjectNode, UnitProjectNode, ProjectStructureFactory
};

// 重新导出路由器相关
pub use router::BaseSchemaRouter;

#[cfg(test)]
mod tests {
    use super::project_structure_plugin::project_structure;
    use mf_state::plugin::PluginTrait;

    #[test]
    fn test_plugin_name_injection() {
        let spec = project_structure::spec();
        let metadata = spec.tr.metadata();
        
        // 验证名称是否正确注入
        assert_eq!(metadata.name, "project_structure", "插件名称应该自动注入为 'project_structure'");
        assert_eq!(metadata.version, "1.0.0");
        assert_eq!(metadata.description, "项目结构插件");
        assert_eq!(metadata.author, "moduforge");
        
        println!("✅ 插件元数据：");
        println!("   名称: {}", metadata.name);
        println!("   版本: {}", metadata.version);
        println!("   描述: {}", metadata.description);
        println!("   作者: {}", metadata.author);
    }
}

