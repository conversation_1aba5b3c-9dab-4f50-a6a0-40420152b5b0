/*
CREATE TABLE "base_rcj" (
  "sequence_nbr" text(20) NOT NULL,
  "level1" text(50),
  "level2" text(50),
  "level3" text(50),
  "level4" text(50),
  "level5" text(50),
  "level6" text(50),
  "level7" text(50),
  "material_code" text(50),
  "kind" integer(10),
  "material_name" text(100),
  "specification" text(500),
  "unit" text(10),
  "de_price" real(16,6),
  "market_price" real(16,6),
  "tax_removal" real(16,6),
  "library_code" text(50),
  "level_mark" text(8),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  "sort_no" integer(20),
  "is_fyrcj" integer(11),
  "kind_sc" text(255),
  "transfer_factor" text(255),
  PRIMARY KEY ("sequence_nbr")
);

CREATE INDEX "base_rcj_library_code_ind"
ON "base_rcj" (
  "library_code" COLLATE BINARY ASC
);

CREATE INDEX "base_rcj_material_code_ind"
ON "base_rcj" (
  "material_code" ASC
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseRcj {
    pub sequence_nbr: String,
    pub level1: Option<String>,
    pub level2: Option<String>,
    pub level3: Option<String>,
    pub level4: Option<String>,
    pub level5: Option<String>,
    pub level6: Option<String>,
    pub level7: Option<String>,
    pub material_code: Option<String>,
    pub kind: Option<i32>,
    pub material_name: Option<String>,
    pub specification: Option<String>,
    pub unit: Option<String>,
    pub de_price: Option<f64>,
    pub market_price: Option<f64>,
    pub tax_removal: Option<f64>,
    pub library_code: Option<String>,
    pub level_mark: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
    pub sort_no: Option<i32>,
    pub is_fyrcj: Option<i32>,
    pub kind_sc: Option<String>,
    pub transfer_factor: Option<String>,
}

//crud!(BaseRcj {}, "base_rcj");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "level1".to_string(),
            "level2".to_string(),
            "level3".to_string(),
            "level4".to_string(),
            "level5".to_string(),
            "level6".to_string(),
            "level7".to_string(),
            "material_code".to_string(),
            "kind".to_string(),
            "material_name".to_string(),
            "specification".to_string(),
            "unit".to_string(),
            "de_price".to_string(),
            "market_price".to_string(),
            "tax_removal".to_string(),
            "library_code".to_string(),
            "level_mark".to_string(),
            "sort_no".to_string(),
            "is_fyrcj".to_string(),
            "kind_sc".to_string(),
            "transfer_factor".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "level1".to_string(),
            "level2".to_string(),
            "level3".to_string(),
            "level4".to_string(),
            "level5".to_string(),
            "level6".to_string(),
            "level7".to_string(),
            "material_code".to_string(),
            "kind".to_string(),
            "material_name".to_string(),
            "specification".to_string(),
            "unit".to_string(),
            "de_price".to_string(),
            "market_price".to_string(),
            "tax_removal".to_string(),
            "library_code".to_string(),
            "level_mark".to_string(),
            "sort_no".to_string(),
            "is_fyrcj".to_string(),
            "kind_sc".to_string(),
            "transfer_factor".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "level1".to_string(),
            "level2".to_string(),
            "level3".to_string(),
            "level4".to_string(),
            "level5".to_string(),
            "level6".to_string(),
            "level7".to_string(),
            "material_code".to_string(),
            "kind".to_string(),
            "material_name".to_string(),
            "specification".to_string(),
            "unit".to_string(),
            "de_price".to_string(),
            "market_price".to_string(),
            "tax_removal".to_string(),
            "library_code".to_string(),
            "level_mark".to_string(),
            "sort_no".to_string(),
            "is_fyrcj".to_string(),
            "kind_sc".to_string(),
            "transfer_factor".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseRcj>("base_rcj", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseRcj>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_rcj").await?;
    let base_list_index = IndexClient::<BaseRcj> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
