use mf_core::node::Node;

use super::node_definitions::{FbfxCsxmFactory, FbNode, QdNode, DeNode, DeRcjNode, FbfxNode, CsxmNode};

pub const FB_STR: &str = "fb";
pub const QD_STR: &str = "qd";
pub const DE_STR: &str = "de";
pub const DE_RCJ_STR: &str = "dercj";
pub const FBFX_STR: &str = "fbfx";
pub const CSXM_STR: &str = "csxm";
///         ├── projectCode (项目编码)
///         ├── unit (单位)
///         ├── projectName (项目名称)
///         ├── typeName (类型)
///         ├── projectAttr (项目特征)
///         ├── quantity (工程量)
///         ├── quantityExpression (工程量表达式)
///         ├── sbfPrice (设备费单价)
///         ├── sbfTotal (设备费合价)
///         ├── zgfPrice (暂估单价)
///         ├── zgfTotal (暂估合价)
///         ├── zjfPrice (直接费单价)
///         ├── zjfTotal (直接费合价)
///         ├── rfeePrice (人工费单价)
///         ├── rfeeTotal (人工费合价)
///         ├── cfeePrice (材料费单价)
///         ├── cfeeTotal (材料费合价)
///         ├── jfeePrice (机械费单价)
///         ├── jfeeTotal (机械费合价)
///         ├── totalRfee (人工费合价)
///         ├── totalCfee (材料费合价)
///         ├── totalJfee (机械费合价)
///         ├── totalProfitFee (利润费合价)
///         ├── totalManagerFee (管理费合价)
///         ├── totalZcfee (主材费合价)
///         ├── price (单价)
///         ├── total (工程造价合价)
///         ├── gfPrice (规费单价)
///         ├── gfTotal (规费合价)
///         ├── scgjsyfPrice (生产工具使用费单价)
///         ├── scgjsyfTotal (生产工具使用费合价)
///         ├── fhddglzjfPrice (繁华地段管理增加费单价)
///         ├── fhddglzjfTotal (繁华地段管理增加费合价)
///         ├── gjfhfPrice (冬季防寒费单价)
///         ├── gjfhfTotal (冬季防寒费合价)
///         ├── sdghzjfPrice (山地管护增加费单价)
///         ├── sdghzjfTotal (山地管护增加费合价)
///         ├── lssgaqfhcsfPrice (绿色施工安全防护措施费单价)
///         ├── lssgaqfhcsfTotal (绿色施工安全防护措施费合价)
///         ├── jxsePrice (进项税额单价)
///         ├── jxseTotal (进项税额合价)
///         ├── xxsePrice (销项税额单价)
///         ├── xxseTotal (销项税额合价)
///         ├── zzsynsePrice (增值税应纳税额单价)
///         ├── zzsynseTotal (增值税应纳税额合价)
///         ├── fjsePrice (附加税费单价)
///         ├── fjseTotal (附加税费合价)
///         ├── sqgczjPrice (税前工程造价单价)
///         ├── sqgczjTotal (税前工程造价合价)
///         ├── fxfyPrice (风险费用单价)
///         ├── fxfyTotal (风险费用合价)
///         ├── sjPrice (税金单价)
///         └── sjTotal (税金合价)
pub fn init_fbfx_csxm_fields() -> Vec<Node> {
    FbfxCsxmFactory::create_complete_structure()
}
