# 📚 文档索引

## 📋 当前文档结构

### 主要文档
- **README.md** - 项目主文档，包含完整的使用指南和技术说明

### 开发文档（独立保留）
- **AGENT_ARCHITECTURE_GUIDE.md** - 开发架构指南和规范
- **AGENT_DEVELOPMENT_CONSTRAINTS.md** - 开发约束和限制说明

### 辅助文档
- **DOCS_INDEX.md** - 本文档，说明文档整合情况和使用方法

## ✅ 已整合到 README.md 的内容

以下文档的内容已经整合到主 README.md 文件中，原文件已删除：

### 1. 微前端架构 (`README_MICROFRONTEND.md`)
- **整合位置**: README.md "核心功能 > 微前端架构" 部分
- **内容**: 架构设计、技术栈、模块管理

### 2. 打包构建指南 (`PACKAGING_GUIDE.md`)
- **整合位置**: README.md "开发指南 > 添加新模块" 部分
- **内容**: 构建流程、配置说明、部署方法

### 3. 窗口管理指南 (`WINDOW_MANAGEMENT_GUIDE.md`)
- **整合位置**: README.md "核心功能 > Tauri 多窗口系统" 部分
- **内容**: 窗口创建、管理、父子关系

### 4. Tauri 窗口指南 (`TAURI_WINDOW_GUIDE.md`)
- **整合位置**: README.md "核心功能 > 操作窗口系统" 部分
- **内容**: 操作窗口、窗口通信、配置选项

### 5. 子窗口修复 (`CHILD_WINDOW_FIX.md`)
- **整合位置**: README.md "开发指南 > 窗口控制" 部分
- **内容**: 窗口控制按钮、事件处理、样式修复

### 6. 自定义表单指南 (`CUSTOM_FORM_GUIDE.md`)
- **整合位置**: README.md "开发指南 > 使用共享组件" 部分
- **内容**: 表单组件、自定义实现、最佳实践

## 🎯 文档整合的优势

### 1. 简化文档结构
- 从 8 个文档减少到 3 个主要文档
- 避免文档分散和重复
- 提高文档的可维护性

### 2. 改善用户体验
- 一个主文档包含所有使用信息
- 减少文档间的跳转
- 更清晰的信息层次结构

### 3. 保持开发规范
- 开发架构指南独立保留
- 开发约束文档独立保留
- 确保开发规范的权威性

## 📖 如何使用文档

### 对于新用户
1. **阅读 README.md** - 了解项目概况、快速开始、核心功能
2. **查看具体章节** - 根据需要查看相应的功能说明
3. **参考示例代码** - 使用文档中的代码示例

### 对于开发者
1. **阅读 README.md** - 了解技术栈和开发指南
2. **阅读 AGENT_ARCHITECTURE_GUIDE.md** - 了解开发架构和规范
3. **阅读 AGENT_DEVELOPMENT_CONSTRAINTS.md** - 了解开发约束和限制

### 对于维护者
- 主要更新 README.md 即可
- 开发规范变更时更新对应的架构文档
- 保持文档与代码的同步

## 🔄 文档维护建议

1. **统一更新**: 功能变更时同步更新 README.md
2. **版本控制**: 重大变更时记录版本信息
3. **示例同步**: 确保代码示例与实际代码一致
4. **定期检查**: 定期检查文档的准确性和完整性

这种文档结构既保持了信息的完整性，又提高了文档的可用性和维护性。

## 🔧 最近更新

### 窗口控制功能修复 (2025-09-15)

#### 修复的问题
1. **主应用窗口控制失效** - Dashboard.vue中的最小化、最大化、关闭按钮无法正常工作
2. **概算模块窗口控制失效** - 概算模块中的窗口控制函数未正确导入
3. **子模块关闭冲突** - SimpleHeader组件中的关闭逻辑与外部处理冲突

#### 解决方案
1. **添加Tauri后端命令**:
   - `maximize_window_with_children` - 最大化窗口及子窗口
   - `unmaximize_window_with_children` - 取消最大化窗口及子窗口

2. **修复主应用窗口控制**:
   - 使用正确的Tauri命令 (`minimize_window_with_children`, `maximize_window_with_children`, `close_window_with_children`)
   - 修复最大化/取消最大化的逻辑

3. **修复概算模块**:
   - 正确导入 `useWindowControls` composable
   - 使用统一的窗口控制方法

4. **简化SimpleHeader关闭逻辑**:
   - 只发出关闭事件，由外部组件处理具体逻辑
   - 避免重复的关闭操作和冲突

#### 技术细节
- **后端**: 在 `src-tauri/src/main.rs` 中添加了新的窗口控制命令
- **前端**: 统一使用 `useWindowControls` composable 进行窗口控制
- **组件**: 简化了 SimpleHeader 的关闭逻辑，避免事件冲突

现在所有窗口控制功能都应该正常工作，包括主应用和子模块的最小化、最大化、关闭操作。

### 父子窗口联动修复 (2025-09-15)

#### 修复的问题
- **模块窗口联动失效** - 概算等模块窗口与主窗口没有联动效果
- **父窗口操作不影响子窗口** - 主窗口最小化、最大化、关闭时，子窗口没有同步操作

#### 根本原因
模块窗口在创建时被错误地注册为独立的主窗口，而不是主窗口的子窗口：
```rust
// 错误的注册方式（之前）
register_window(&window_label, None);  // 注册为独立窗口

// 正确的注册方式（修复后）
register_window(&window_label, Some("main".to_string()));  // 注册为主窗口的子窗口
```

#### 解决方案
1. **修复模块窗口注册**:
   - 在 `create_module_window` 命令中，将模块窗口注册为主窗口的子窗口
   - 确保父子窗口关系正确建立

2. **验证联动逻辑**:
   - 确认 `minimize_window_with_children` 命令正确处理子窗口
   - 确认 `maximize_window_with_children` 命令正确处理子窗口
   - 确认 `close_window_with_children` 命令正确处理子窗口

#### 现在的联动效果
- **主窗口最小化** → 所有子窗口（包括概算模块）同时最小化
- **主窗口最大化** → 所有子窗口同时最大化
- **主窗口关闭** → 所有子窗口同时关闭
- **弹窗联动** → 通过操作窗口管理器创建的弹窗也有正确的父子关系

#### 技术细节
- **后端**: 修改了 `src-tauri/src/main.rs` 中的模块窗口注册逻辑
- **窗口管理**: 使用 `WINDOW_MANAGER` 全局状态管理父子窗口关系
- **联动命令**: 所有 `*_with_children` 命令都会递归处理子窗口

现在父子窗口联动功能已完全恢复，主窗口的所有操作都会正确地影响到子窗口。

### 窗口联动逻辑优化 (2025-09-15)

#### 重新定义的联动逻辑

**主应用（控制台）**:
- **关闭行为**: 关闭时同时关闭所有子应用及其弹窗，使用 Ant Design 确认对话框
- **其他操作**: 最小化、最大化只影响主应用自身，不影响子应用

**子应用（概算、预算等模块）**:
- **模态窗联动**:
  - 最小化 → 模态窗同步最小化
  - 关闭 → 模态窗同步关闭，使用 Ant Design 确认对话框
- **模态窗独立**: 最大化/窗口化不影响模态窗

#### 新增的 Tauri 命令

```rust
// 单个窗口操作（不影响子窗口）
minimize_window(window_id)           // 只最小化指定窗口
maximize_window(window_id)           // 只最大化指定窗口
unmaximize_window(window_id)         // 只取消最大化指定窗口

// 模态窗联动操作（子应用专用）
minimize_window_with_modals(window_id)  // 最小化窗口及其模态窗
close_window_with_modals(window_id)     // 关闭窗口及其模态窗

// 全量联动操作（主应用专用）
close_window_with_children(window_id)   // 关闭窗口及所有子窗口
```

#### 前端实现

**主应用关闭确认**:
```javascript
Modal.confirm({
  title: '确认关闭',
  content: '确定要关闭应用吗？这将同时关闭所有子应用和弹窗。',
  onOk: async () => {
    await invoke('close_window_with_children', { windowId: 'main' })
  }
})
```

**子应用关闭确认**:
```javascript
Modal.confirm({
  title: '确认关闭',
  content: '确定要关闭概算模块吗？这将同时关闭所有相关的弹窗。',
  onOk: async () => {
    await invoke('close_window_with_modals', { windowId: 'rough-estimate-main' })
  }
})
```

#### 联动效果总结

1. **主应用操作**:
   - 最小化/最大化 → 只影响主应用
   - 关闭 → 影响所有子应用和弹窗

2. **子应用操作**:
   - 最小化 → 影响子应用及其模态窗
   - 最大化 → 只影响子应用本身
   - 关闭 → 影响子应用及其模态窗

3. **确认对话框**:
   - 主应用和子应用关闭都有确认提示
   - 使用 Ant Design Modal.confirm 组件

现在窗口联动逻辑更加精确和用户友好，符合实际使用需求。

### 子应用模态窗联动修复 (2025-09-15)

#### 修复的问题
- **模态窗不跟随子应用联动** - 概算模块最小化/关闭时，其打开的模态窗没有同步操作
- **窗口ID不匹配** - 代码中使用的窗口ID与实际创建的窗口ID不一致

#### 根本原因
1. **窗口ID错误**:
   - 代码中使用 `'rough-estimate-main'`
   - 实际窗口ID是 `'module-rough-estimate'`

2. **窗口信息不准确**:
   - `windowInfo.label` 硬编码为错误的值
   - 导致模态窗的父子关系建立错误

#### 解决方案

**修复窗口ID**:
```javascript
// 修复前：错误的窗口ID
windowId: 'rough-estimate-main'

// 修复后：正确的窗口ID
windowId: currentWindow.value?.label || 'module-rough-estimate'
```

**修复窗口信息**:
```javascript
// 修复前：硬编码的错误标签
const windowInfo = computed(() => ({
  label: 'rough-estimate-main',
  // ...
}))

// 修复后：动态获取正确标签
const windowInfo = computed(() => ({
  label: currentWindow.value?.label || 'module-rough-estimate',
  // ...
}))
```

**添加调试日志**:
- 在 `minimize_window_with_modals` 命令中添加子窗口列表日志
- 在 `close_window_with_modals` 命令中添加关闭过程日志
- 便于排查父子窗口关系问题

#### 修复的文件
1. **EstimateMain.vue**:
   - 修复窗口控制命令中的 `windowId` 参数
   - 修复 `windowInfo.label` 的获取逻辑

2. **main.rs**:
   - 添加调试日志以便排查问题
   - 增强错误处理和状态输出

#### 现在的联动效果
1. **概算模块最小化** → 所有相关模态窗同步最小化
2. **概算模块关闭** → 所有相关模态窗同步关闭
3. **概算模块最大化** → 模态窗保持独立，不受影响

#### 技术细节
- **窗口标签规则**: 模块窗口使用 `module-{moduleKey}` 格式
- **父子关系**: 模态窗正确注册为对应模块窗口的子窗口
- **联动命令**: 使用专门的 `*_with_modals` 命令处理模态窗联动

现在子应用的模态窗联动功能已完全修复，所有模态窗都会正确跟随父窗口的最小化和关闭操作。
