[package]
authors = {workspace = true}
description = "计价软件基础扩展定义"
edition = {workspace = true}
name = "extension-base-schema"
version = {workspace = true}
[dependencies]
anyhow = {workspace = true}
serde = {workspace = true}
serde_json = {workspace = true}
shared = {workspace = true}
thiserror = {workspace = true}
tokio = {workspace = true}
imbl = {workspace = true}



uuid = {workspace = true}
chrono = {workspace = true}

price-rules = {workspace = true}
price-storage = {workspace = true}
async-trait = { workspace = true }
price-common = { workspace = true }

# 使用标准化的 web 响应和错误处理
price-web = { workspace = true }

moduforge-core = {workspace = true}
moduforge-macros = {workspace = true}
moduforge-macros-derive = {workspace = true}
moduforge-model = {workspace = true}
moduforge-state = {workspace = true}
moduforge-transform = {workspace = true}
