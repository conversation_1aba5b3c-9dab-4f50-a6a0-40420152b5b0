#!/bin/bash

# Price-RS Extension Generator Demo Script
# This script demonstrates the various features of the extension generator

set -e

echo "🚀 Price-RS Extension Generator Demo"
echo "===================================="
echo

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if the generator is built
if [ ! -f "../target/release/price-ext-gen" ] && [ ! -f "../target/debug/price-ext-gen" ]; then
    echo -e "${YELLOW}Building extension generator...${NC}"
    cd ..
    cargo build --release
    cd scripts
    echo -e "${GREEN}✅ Build completed${NC}"
    echo
fi

# Set the binary path
if [ -f "../target/release/price-ext-gen" ]; then
    GENERATOR="../target/release/price-ext-gen"
else
    GENERATOR="../target/debug/price-ext-gen"
fi

echo -e "${BLUE}Using generator:${NC} $GENERATOR"
echo

# Demo 1: List available templates
echo -e "${YELLOW}📋 Demo 1: List Available Templates${NC}"
echo "Command: $GENERATOR list --detailed"
echo
$GENERATOR list --detailed
echo

# Demo 2: Generate a standard extension
echo -e "${YELLOW}🏗️ Demo 2: Generate Standard Extension${NC}"
echo "Command: $GENERATOR new demo-standard --description \"Demo standard extension\" --output ./demo-output/demo-standard --force"
echo
$GENERATOR new demo-standard \
    --description "Demo standard extension with all four layers" \
    --author "Demo Team" \
    --output ./demo-output/demo-standard \
    --force

echo -e "${GREEN}✅ Standard extension generated${NC}"
echo

# Demo 3: Generate an API extension
echo -e "${YELLOW}🌐 Demo 3: Generate API Extension${NC}"
echo "Command: $GENERATOR new demo-api --extension-type api --description \"Demo API extension\" --output ./demo-output/demo-api --force"
echo
$GENERATOR new demo-api \
    --extension-type api \
    --description "Demo API extension for HTTP services" \
    --author "API Team" \
    --output ./demo-output/demo-api \
    --force

echo -e "${GREEN}✅ API extension generated${NC}"
echo

# Demo 4: Generate a lightweight extension
echo -e "${YELLOW}📦 Demo 4: Generate Lightweight Extension${NC}"
echo "Command: $GENERATOR new demo-light --extension-type lightweight --description \"Demo lightweight extension\" --output ./demo-output/demo-light --force"
echo
$GENERATOR new demo-light \
    --extension-type lightweight \
    --description "Demo lightweight extension for simple data processing" \
    --author "Light Team" \
    --output ./demo-output/demo-light \
    --force

echo -e "${GREEN}✅ Lightweight extension generated${NC}"
echo

# Demo 5: Generate a custom extension
echo -e "${YELLOW}🔧 Demo 5: Generate Custom Extension${NC}"
echo "Command: $GENERATOR new demo-custom --extension-type custom --layers nodes,plugins,commands --description \"Demo custom extension\" --output ./demo-output/demo-custom --force"
echo
$GENERATOR new demo-custom \
    --extension-type custom \
    --layers nodes,plugins,commands \
    --description "Demo custom extension with specific layers" \
    --author "Custom Team" \
    --output ./demo-output/demo-custom \
    --force

echo -e "${GREEN}✅ Custom extension generated${NC}"
echo

# Demo 6: Show generated project structure
echo -e "${YELLOW}📁 Demo 6: Generated Project Structure${NC}"
echo "Let's examine the generated projects:"
echo

for project in demo-standard demo-api demo-light demo-custom; do
    echo -e "${BLUE}📂 $project/${NC}"
    if [ -d "./demo-output/$project" ]; then
        tree "./demo-output/$project" -L 3 2>/dev/null || find "./demo-output/$project" -type f | head -10
    fi
    echo
done

# Demo 7: Validate generated projects
echo -e "${YELLOW}✅ Demo 7: Validate Generated Projects${NC}"
echo

for project in demo-standard demo-api demo-light demo-custom; do
    if [ -d "./demo-output/$project" ]; then
        echo "Validating $project..."
        $GENERATOR validate "./demo-output/$project"
        echo -e "${GREEN}✅ $project validation completed${NC}"
        echo
    fi
done

# Demo 8: Show configuration
echo -e "${YELLOW}⚙️ Demo 8: Configuration Management${NC}"
echo "Command: $GENERATOR config show"
echo
$GENERATOR config show
echo

# Demo 9: Test compilation
echo -e "${YELLOW}🔨 Demo 9: Test Compilation${NC}"
echo "Testing if generated projects compile..."
echo

for project in demo-standard demo-api demo-light demo-custom; do
    if [ -d "./demo-output/$project" ]; then
        echo "Compiling $project..."
        cd "./demo-output/$project"
        if cargo check --quiet; then
            echo -e "${GREEN}✅ $project compiles successfully${NC}"
        else
            echo -e "${RED}❌ $project compilation failed${NC}"
        fi
        cd - > /dev/null
        echo
    fi
done

# Summary
echo -e "${GREEN}🎉 Demo Completed!${NC}"
echo "===================================="
echo
echo "Generated projects:"
echo "  📂 demo-output/demo-standard  - Standard extension with all layers"
echo "  📂 demo-output/demo-api       - API-focused extension" 
echo "  📂 demo-output/demo-light     - Lightweight extension"
echo "  📂 demo-output/demo-custom    - Custom extension with specific layers"
echo
echo "Next steps:"
echo "  1. Explore the generated code in demo-output/"
echo "  2. Modify the TODO sections with your business logic"
echo "  3. Run tests: cargo test (in each project directory)"
echo "  4. Add projects to your workspace Cargo.toml"
echo
echo "For more information, see:"
echo "  - README.md for detailed usage instructions"
echo "  - examples/basic_usage.rs for programmatic API examples"
echo
echo -e "${BLUE}Happy coding! 🚀${NC}"