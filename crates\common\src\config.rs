/// 计价系统通用配置类型
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

// ================================
// 基础地区信息
// ================================

/// 基础地区信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BaseRegionInfo {
    /// 地区代码（如：110000为北京）
    pub region_code: String,
    /// 地区名称
    pub region_name: String,
    /// 配置版本
    pub config_version: String,
    /// 是否启用该地区
    pub enabled: bool,
}

impl BaseRegionInfo {
    pub fn new(region_code: &str, region_name: &str) -> Self {
        Self {
            region_code: region_code.to_string(),
            region_name: region_name.to_string(),
            config_version: "1.0.0".to_string(),
            enabled: true,
        }
    }
    
    pub fn validate(&self) -> Result<(), crate::CommonConfigError> {
        if self.region_code.is_empty() {
            return Err(crate::CommonConfigError::MissingRequired("region_code".to_string()));
        }
        
        if self.region_code.len() != 6 {
            return Err(crate::CommonConfigError::InvalidValue {
                key: "region_code".to_string(),
                value: self.region_code.clone(),
            });
        }
        
        if self.region_name.is_empty() {
            return Err(crate::CommonConfigError::MissingRequired("region_name".to_string()));
        }
        
        Ok(())
    }
}
