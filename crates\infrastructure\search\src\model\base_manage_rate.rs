/*
CREATE TABLE "base_manage_rate" (
  "sequence_nbr" text(20) NOT NULL,
  "project_type" text(50),
  "library_code" text(255) NOT NULL,
  "qf_code" text(255),
  "sort_no" integer(11),
  "precast_rate" text(255) NOT NULL,
  "management_fee1" real(16,6),
  "management_fee2" real(16,6),
  "management_fee3" real(16,6),
  "profit1" real(16,6),
  "profit2" real(16,6),
  "profit3" real(16,6),
  "remark" text(1024),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};
#[derive(Debug, Serialize, Deserialize)]
pub struct BaseManageRate {
    pub sequence_nbr: String,
    pub project_type: Option<String>,
    pub library_code: Option<String>,
    pub qf_code: Option<String>,
    pub sort_no: Option<i32>,
    pub precast_rate: Option<String>,
    pub management_fee1: Option<f64>,
    pub management_fee2: Option<f64>,
    pub management_fee3: Option<f64>,
    pub profit1: Option<f64>,
    pub profit2: Option<f64>,
    pub profit3: Option<f64>,
    pub remark: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseManageRate {}, "base_manage_rate");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "project_type".to_string(),
            "library_code".to_string(),
            "qf_code".to_string(),
            "sort_no".to_string(),
            "precast_rate".to_string(),
            "management_fee1".to_string(),
            "management_fee2".to_string(),
            "management_fee3".to_string(),
            "profit1".to_string(),
            "profit2".to_string(),
            "profit3".to_string(),
            "remark".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "project_type".to_string(),
            "library_code".to_string(),
            "qf_code".to_string(),
            "sort_no".to_string(),
            "precast_rate".to_string(),
            "management_fee1".to_string(),
            "management_fee2".to_string(),
            "management_fee3".to_string(),
            "profit1".to_string(),
            "profit2".to_string(),
            "profit3".to_string(),
            "remark".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "project_type".to_string(),
            "library_code".to_string(),
            "qf_code".to_string(),
            "sort_no".to_string(),
            "precast_rate".to_string(),
            "management_fee1".to_string(),
            "management_fee2".to_string(),
            "management_fee3".to_string(),
            "profit1".to_string(),
            "profit2".to_string(),
            "profit3".to_string(),
            "remark".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseManageRate>("base_manage_rate", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseManageRate>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_manage_rate").await?;
    let base_list_index = IndexClient::<BaseManageRate> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
