{"name": "price-nodejs", "version": "0.0.0", "main": "index.js", "types": "index.d.ts", "napi": {"name": "price", "triples": {"additional": ["aarch64-apple-darwin", "aarch64-linux-android", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "aarch64-pc-windows-msvc", "armv7-unknown-linux-gnueabihf", "armv7-unknown-linux-musleabihf", "x86_64-unknown-linux-musl", "x86_64-unknown-freebsd", "i686-pc-windows-msvc", "armv7-linux-androideabi", "universal-apple-darwin", "riscv64gc-unknown-linux-gnu"]}}, "license": "MIT", "devDependencies": {"@napi-rs/cli": "^2.18.4", "ava": "^6.0.1"}, "ava": {"timeout": "3m"}, "engines": {"node": ">= 10"}, "scripts": {"artifacts": "napi artifacts", "build": "napi build --platform --release", "build:debug": "napi build --platform", "prepublishOnly": "napi prepublish -t npm", "test": "ava", "universal": "napi universal", "version": "napi version"}}