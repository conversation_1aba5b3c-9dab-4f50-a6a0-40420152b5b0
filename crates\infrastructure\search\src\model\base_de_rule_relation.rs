/*
CREATE TABLE "base_de_rule_relation" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "de_code" varchar,
  "de_name" varchar,
  "library_code" varchar,
  "relation_group_code" varchar,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ('A'),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "description" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeRuleRelation {
    pub sequence_nbr: String,
    pub de_code: Option<String>,
    pub de_name: Option<String>,
    pub library_code: Option<String>,
    pub relation_group_code: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(BaseDeRuleRelation {}, "base_de_rule_relation");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "de_code".to_string(),
            "de_name".to_string(),
            "library_code".to_string(),
            "relation_group_code".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "de_code".to_string(),
            "de_name".to_string(),
            "library_code".to_string(),
            "relation_group_code".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "de_code".to_string(),
            "de_name".to_string(),
            "library_code".to_string(),
            "relation_group_code".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeRuleRelation>("base_de_rule_relation", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeRuleRelation>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_rule_relation").await?;
    let base_de_rule_relation_index = IndexClient::<BaseDeRuleRelation> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_rule_relation_index);
    Ok(search_service)
}
