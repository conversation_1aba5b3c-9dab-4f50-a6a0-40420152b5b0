{% if has_commands -%}
pub mod command;
{%- endif %}
{% if has_nodes -%}
pub mod nodes;
{%- endif %}
{% if has_plugins -%}
pub mod plugins;
{%- endif %}
{% if has_router -%}
pub mod router;
{%- endif %}

{% if has_nodes -%}
// 重新导出主要的结构体和工厂
pub use nodes::{{ names.snake }}_definitions::{
    {{ node_prefix }}ContainerNode, {{ node_prefix }}RowNode, {{ node_prefix }}Factory
};
{%- endif %}

{% if has_plugins -%}
// 重新导出插件
pub use plugins::{{ plugin_struct_name }};
{%- endif %}

{% if has_router -%}
// 重新导出路由器相关
pub use router::{{ node_prefix }}Router;
{%- endif %}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_{{ names.snake }}_module() {
        // 基础测试，验证模块正确加载
        assert_eq!("{{ names.kebab }}", "{{ names.kebab }}");
    }
    
    {% if has_plugins -%}
    #[tokio::test]
    async fn test_{{ names.snake }}_plugin() {
        use mf_state::plugin::PluginTrait;
        
        let plugin = {{ plugin_struct_name }};
        let metadata = plugin.metadata();
        
        // 验证插件元数据
        assert_eq!(metadata.name, "{{ names.kebab }}");
        assert_eq!(metadata.description, "{{ description }}");
        
        println!("✅ 插件元数据：");
        println!("   名称: {}", metadata.name);
        println!("   版本: {}", metadata.version);
        println!("   描述: {}", metadata.description);
        println!("   作者: {}", metadata.author);
    }
    {%- endif %}
}