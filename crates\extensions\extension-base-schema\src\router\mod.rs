use price_web::axum::{
    routing::{get, post},
    Router,
};

pub mod handlers;
pub mod dto;
use handlers::*;

/// 基础扩展 Router 构建器
pub struct BaseSchemaRouter;

impl BaseSchemaRouter {
    /// 创建基础扩展的标准路由
    pub fn create_router<S>() -> Router<S> 
    where
        S: Clone + Send + Sync + 'static,
    {
        Router::new()
            // 项目结构管理路由
            .nest("/projects", Self::project_routes())
            // 单项工程管理路由
            .nest("/single-projects", Self::single_project_routes())
            // 单位工程管理路由
            .nest("/unit-projects", Self::unit_project_routes())
            // 健康检查和信息路由
            .route("/health", get(health_check))
            .route("/info", get(get_extension_info))
    }

    /// 项目结构相关路由
    fn project_routes<S>() -> Router<S> 
    where
        S: Clone + Send + Sync + 'static,
    {
        Router::new()
            .route("/", get(list_projects).post(create_project))
            .route("/:id", get(get_project).put(update_project).delete(delete_project))
            .route("/:id/structure", get(get_project_structure))
            .route("/:id/export", post(export_project))
    }

    /// 单项工程相关路由
    fn single_project_routes<S>() -> Router<S> 
    where
        S: Clone + Send + Sync + 'static,
    {
        Router::new()
            .route("/", get(list_single_projects).post(create_single_project))
            .route("/:id", get(get_single_project).put(update_single_project).delete(delete_single_project))
            .route("/:id/units", get(list_project_units))
    }

    /// 单位工程相关路由
    fn unit_project_routes<S>() -> Router<S> 
    where
        S: Clone + Send + Sync + 'static,
    {
        Router::new()
            .route("/", get(list_unit_projects).post(create_unit_project))
            .route("/:id", get(get_unit_project).put(update_unit_project).delete(delete_unit_project))
            .route("/:id/calculate", post(calculate_unit_project))
    }
}

// 重新导出标准化的响应类型
pub use price_web::ResponseResult;

pub type ApiResult<T> = ResponseResult<T>;