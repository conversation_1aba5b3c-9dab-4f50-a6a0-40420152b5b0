/*
CREATE TABLE "base_rate" (
  "sequence_nbr" text(20) NOT NULL,
  "kind_num" integer(10),
  "type_name" text(50),
  "rate_code" text(200),
  "project_type" text(50),
  "library_code" text(50),
  "management_fee1" real(16,6),
  "management_fee2" real(16,6),
  "management_fee3" real(16,6),
  "profit1" real(16,6),
  "profit2" real(16,6),
  "profit3" real(16,6),
  "gfee_rate" real(16,6),
  "awf_rate_max" real(16,6),
  "awf_rate_min" real(16,6),
  "tax_rates" real(16,6),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" integer(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseRate {
    pub sequence_nbr: String,
    pub kind_num: Option<i32>,
    pub type_name: Option<String>,
    pub rate_code: Option<String>,
    pub project_type: Option<String>,
    pub library_code: Option<String>,
    pub management_fee1: Option<f64>,
    pub management_fee2: Option<f64>,
    pub management_fee3: Option<f64>,
    pub profit1: Option<f64>,
    pub profit2: Option<f64>,
    pub profit3: Option<f64>,
    pub gfee_rate: Option<f64>,
    pub awf_rate_max: Option<f64>,
    pub awf_rate_min: Option<f64>,
    pub tax_rates: Option<f64>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseRate {}, "base_rate");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "kind_num".to_string(),
            "type_name".to_string(),
            "rate_code".to_string(),
            "project_type".to_string(),
            "library_code".to_string(),
            "management_fee1".to_string(),
            "management_fee2".to_string(),
            "management_fee3".to_string(),
            "profit1".to_string(),
            "profit2".to_string(),
            "profit3".to_string(),
            "gfee_rate".to_string(),
            "awf_rate_max".to_string(),
            "awf_rate_min".to_string(),
            "tax_rates".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "kind_num".to_string(),
            "type_name".to_string(),
            "rate_code".to_string(),
            "project_type".to_string(),
            "library_code".to_string(),
            "management_fee1".to_string(),
            "management_fee2".to_string(),
            "management_fee3".to_string(),
            "profit1".to_string(),
            "profit2".to_string(),
            "profit3".to_string(),
            "gfee_rate".to_string(),
            "awf_rate_max".to_string(),
            "awf_rate_min".to_string(),
            "tax_rates".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "kind_num".to_string(),
            "type_name".to_string(),
            "rate_code".to_string(),
            "project_type".to_string(),
            "library_code".to_string(),
            "management_fee1".to_string(),
            "management_fee2".to_string(),
            "management_fee3".to_string(),
            "profit1".to_string(),
            "profit2".to_string(),
            "profit3".to_string(),
            "gfee_rate".to_string(),
            "awf_rate_max".to_string(),
            "awf_rate_min".to_string(),
            "tax_rates".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseRate>("base_rate", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseRate>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_rate").await?;
    let base_list_index = IndexClient::<BaseRate> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
