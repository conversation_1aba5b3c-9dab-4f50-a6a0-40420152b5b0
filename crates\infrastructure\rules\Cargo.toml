[package]
name = "price-rules"
version = {workspace=true}
edition = {workspace=true}
description = "规则引擎"

[lib]
path="./src/lib.rs"

[dependencies]
serde = {workspace=true}
serde_json = {workspace=true}
thiserror= {workspace=true}
async-trait= {workspace=true}
anyhow= {workspace=true}
tokio= {workspace=true}
uuid = { workspace = true, features = ["v4", "serde"] }
chrono = { workspace = true, features = ["serde"] }

rbs = {workspace = true}
rbatis = {workspace = true}
rbdc-sqlite = { workspace = true }


zen-engine = { workspace = true }
zen-expression = { workspace = true }
zen-tmpl = { workspace = true }

moduforge-model = { workspace = true }
moduforge-state = { workspace = true }
moduforge-transform = { workspace = true }
moduforge-core = { workspace = true }