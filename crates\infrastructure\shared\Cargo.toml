[package]
name = "shared"
version = "0.1.0"
edition = "2021"

[dependencies]
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true } 
state = { workspace = true}
async-channel = { workspace = true }
async-trait = { workspace = true }
futures = { workspace = true }
dashmap = { workspace = true }
moduforge-core = { workspace = true }