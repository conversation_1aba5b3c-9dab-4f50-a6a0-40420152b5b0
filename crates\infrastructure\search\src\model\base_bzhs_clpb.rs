/*

CREATE TABLE "base_bzhs_clpb" (
  "sequence_nbr" text(20) NOT NULL,
  "library_code" text(255) NOT NULL,
  "group_name" text(255),
  "details_code" text(255),
  "type" text(512),
  "details" text(512),
  "rec_user_code" text(32),
  "rec_date" text(20),
  "description" text(255),
  "standard_id" text(19),
  PRIMARY KEY ("sequence_nbr")
);*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseBzhsClpb {
    pub sequence_nbr: String,
    pub library_code: String,
    pub group_name: Option<String>,
    pub details_code: Option<String>,
    pub t_type: Option<String>,
    pub details: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_date: Option<String>,
    pub description: Option<String>,
    pub standard_id: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseBzhsClpb {}, "base_bzhs_clpb");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "group_name".to_string(),
            "details_code".to_string(),
            "type".to_string(),
            "details".to_string(),
            "rec_user_code".to_string(),
            "rec_date".to_string(),
            "description".to_string(),
            "standard_id".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "group_name".to_string(),
            "details_code".to_string(),
            "type".to_string(),
            "details".to_string(),
            "rec_user_code".to_string(),
            "rec_date".to_string(),
            "description".to_string(),
            "standard_id".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "group_name".to_string(),
            "details_code".to_string(),
            "type".to_string(),
            "details".to_string(),
            "rec_user_code".to_string(),
            "rec_date".to_string(),
            "description".to_string(),
            "standard_id".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseBzhsClpb>("base_bzhs_clpb", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseBzhsClpb>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_bzhs_clpb").await?;
    let base_bzhs_clpb_index = IndexClient::<BaseBzhsClpb> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_bzhs_clpb_index);
    Ok(search_service)
}
