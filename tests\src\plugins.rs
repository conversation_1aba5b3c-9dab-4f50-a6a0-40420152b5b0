use async_trait::async_trait;
use mf_macro::{impl_plugin, impl_state_field, mf_plugin, mf_meta};
use mf_derive::PState;
use mf_model::node_type::NodeEnum;
use mf_state::error::StateResult;
use mf_state::{
    debug,
    plugin::{PluginTrait, StateField},
    resource::Resource,
    state::{State, StateConfig},
    transaction::Transaction,
};
use std::{collections::HashMap, sync::Arc};
async fn p1_append(
    _: &[Transaction],
    _: &State,
    new_state: &State,
) -> anyhow::Result<Option<Transaction>> {
    let mut tr = new_state.tr();
    let root = tr.doc().root();
    let _ = tr.add_node(root.id.clone(), vec![NodeEnum::from(tr.schema.nodes.get("DW").unwrap().create(None, None, vec![], None), vec![])]);
    Ok(Some(tr))
}

mf_plugin!(p1_plugin,
    metadata = mf_meta!(
        version = "1.0.0",
        description = "测试插件",
        author = "ModuForge Team",
        tags = ["validation", "security"]
    ),
   append_transaction = p1_append
);

#[derive(Debug, PState)]
pub struct P1State1 {
    pub map: HashMap<String, String>,
}

async fn p1_init(
    _config: &StateConfig,
    _instance: &State,
) -> Arc<dyn Resource> {
    let map: HashMap<String, String> = HashMap::from([("k".to_string(), "v".to_string())]);
    Arc::new(P1State1 { map })
}

async fn p1_apply(
    tr: &Transaction,
    value: Arc<dyn Resource>,
    _old_state: &State,
    _new_state: &State,
) -> Arc<dyn Resource> {
    debug!("P1Plugin apply{}", tr.steps.len());
    value
}

impl_state_field!(P1State, p1_init, p1_apply);

async fn p2_append(
    trs: &[Transaction],
    _: &State,
    _: &State,
) -> StateResult<Option<Transaction>> {
    let mut tr = trs.last().unwrap().clone();
    let size = tr.doc().size();
    debug!("P2Plugin开始节点个数：{}", tr.doc().size());
    let root = tr.doc().root();
    if size < 10 {
        let _ = tr.add_node(root.id.clone(), vec![NodeEnum::from(tr.schema.nodes.get("DW").unwrap().create(None, None, vec![], None), vec![])]);
        debug!("P2Plugin节点个数：{}", tr.doc().size());
        return Ok(Some(tr));
    }
    Ok(None)
}
mf_plugin!(p2_plugin,
    metadata = mf_meta!(
        version = "1.0.0",
        description = "测试插件2",
        author = "ModuForge Team",
        tags = ["validation", "security"]
    ),
   append_transaction = p2_append,
   state_field = P1State,
);
