/*
CREATE TABLE "base_list_calc_rule_2022" (
  "sequence_nbr" text NOT NULL,
  "library_code" text(50),
  "list_code" text(255),
  "calc_rule" text(1024),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64)
);
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseListCalcRule2022 {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub list_code: Option<String>,
    pub calc_rule: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseListCalcRule2022 {}, "base_list_calc_rule_2022");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec!["library_code".to_string(), "list_code".to_string(), "calc_rule".to_string()]),
        filterable_attributes: Some(vec!["library_code".to_string(), "list_code".to_string(), "calc_rule".to_string()]),
        sortable_attributes: Some(vec!["library_code".to_string(), "list_code".to_string(), "calc_rule".to_string()]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseListCalcRule2022>("base_list_calc_rule_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseListCalcRule2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_list_calc_rule_2022").await?;
    let base_list_index = IndexClient::<BaseListCalcRule2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
