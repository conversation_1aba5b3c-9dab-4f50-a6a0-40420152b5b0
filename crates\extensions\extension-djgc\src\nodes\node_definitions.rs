use serde::{Deserialize, Serialize};
use mf_derive::Node;

/// 单价构成容器节点
/// 用于表示整个单价构成结构的顶层容器
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "djgc"]
#[desc = "单价构成"]
#[content = "djgcRowNode+"]
pub struct DjgcContainerNode {
    #[attr]
    pub value: String,
}

/// 单价构成行节点
/// 用于表示单价构成中的每一行数据
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "djgcRowNode"]
#[desc = "单价构成行节点"]
pub struct DjgcRowNode {
    /// 取费编码
    #[attr]
    pub qf_code: String,
    
    /// 标准
    #[attr]
    pub standard: String,
    
    /// 单价构成类型
    #[attr]
    pub type_name: String,
    
    /// 费用代号
    #[attr]
    pub code: String,
    
    /// 计算基数
    #[attr]
    pub caculate_base: String,
    
    /// 描述
    #[attr]
    pub desc: String,
    
    /// 费率
    #[attr]
    pub rate: String,
    
    /// 单价
    #[attr(default = 0.0)]
    pub price: f64,
}

/// 单价构成节点工厂
pub struct DjgcFactory;

impl DjgcFactory {
    /// 创建单价构成节点结构
    pub fn create_djgc_structure() -> Vec<mf_core::node::Node> {
        let container = DjgcContainerNode::node_definition();
        let row = DjgcRowNode::node_definition();
        
        vec![container, row]
    }
    
    /// 创建单价构成容器节点
    pub fn create_container_node() -> mf_core::node::Node {
        DjgcContainerNode::node_definition()
    }
    
    /// 创建单价构成行节点
    pub fn create_row_node() -> mf_core::node::Node {
        DjgcRowNode::node_definition()
    }
}