/* ModalWindowHeader 组件样式 - 全局样式文件 */
.modal-window-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  height: 48px;
  position: relative;
  z-index: 1000;
  -webkit-app-region: drag;
  user-select: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modal-window-header .header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 16px;
  position: relative;
}

.modal-window-header .window-title {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
}

.modal-window-header .window-title h3 {
  margin: 0;
  color: white;
  font-weight: 600;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.modal-window-header .header-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  -webkit-app-region: no-drag;
}

.modal-window-header .window-controls {
  display: flex;
  align-items: center;
  gap: 6px;
}

.modal-window-header .control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.modal-window-header .control-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0);
  transition: background 0.2s ease;
  z-index: -1;
}

.modal-window-header .control-btn:hover::before {
  background: rgba(255, 255, 255, 0.1);
}

.modal-window-header .control-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-0.5px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modal-window-header .control-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-window-header .control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.modal-window-header .control-btn:disabled::before {
  background: transparent;
}

.modal-window-header .close-btn:hover {
  background: rgba(239, 68, 68, 0.9);
  transform: translateY(-0.5px);
}

.modal-window-header .close-btn:hover::before {
  background: rgba(255, 255, 255, 0.1);
}

.modal-window-header .control-btn svg {
  width: 14px;
  height: 14px;
  pointer-events: none;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .modal-window-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .modal-window-header .window-title h3 {
    color: #ecf0f1;
  }
  
  .modal-window-header .control-btn {
    color: rgba(236, 240, 241, 0.8);
  }
  
  .modal-window-header .control-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ecf0f1;
  }
}

/* 响应式设计 */
@media (max-width: 600px) {
  .modal-window-header .header-content {
    padding: 0 12px;
  }
  
  .modal-window-header .window-title h3 {
    font-size: 13px;
  }
  
  .modal-window-header .control-btn {
    width: 26px;
    height: 26px;
  }
  
  .modal-window-header .control-btn svg {
    width: 12px;
    height: 12px;
  }
}