[package]
name = "extension-generator"
version = "0.1.0"
edition = "2021"
authors = ["Price-RS Team <<EMAIL>>"]
description = "CLI tool for generating Price-RS extension project templates"
license = "MIT"
repository = "https://github.com/your-org/price-rs"

[lib]
name = "extension_generator"
path = "src/lib.rs"

[[bin]]
name = "price-ext-gen"
path = "src/main.rs"

[dependencies]
# CLI框架
clap = { version = "4.4", features = ["derive", "env"] }
inquire = "0.7" # 交互式命令行

# 文件系统操作
tokio = { version = "1.0", features = ["full"] }
fs_extra = "1.3"
walkdir = "2.4"

# 模板引擎
tera = "1.19"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志和输出
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
colored = "2.0"
indicatif = "0.17"

# 工具函数
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
regex = "1.10"
dirs = "5.0"

# TOML parsing for validation tests
toml = "0.8"

[dev-dependencies]
tempfile = "3.8"
assert_fs = "1.0"
assert_cmd = "2.0"
predicates = "3.0"