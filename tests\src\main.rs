use std::sync::Arc;

use extension_djgc::nodes::fields::init_nodes as init_nodes_djgc;
use extension_fbfx_csxm::build_extension;
use mf_core::{
    extension::Extension,
    types::{EditorOptionsBuilder, Extensions},
};
use mf_macro::node;
use mf_state::{
    info, init_logging,
    plugin::{Plugin, PluginSpec},
};
use price_budget_core::{editor::PriceEditor, types::PriceEditorOptions};
use price_rules::loader::sys_loader::SystemLoader;
use price_storage::{StorageHandler, StorageOptions, sqlite};
use serde_json::json;
use tests::{
    plugins::{P1State},
    search::test_all_search,
};
use tests::plugins::{p1_plugin, p2_plugin};

#[tokio::main]
async fn main() {
    init_logging("info", Some("logs/moduforge.log")).unwrap();
    let editor = PriceEditor::create(build_op().await).await.unwrap();
    //dbg!(&editor.get_state().doc());
     let engine = editor.get_rules_engine().unwrap();
    let res = engine.evaluate("test",json!({"value":10}).into()).await.unwrap();
    info!("res: {:?}", res); 
    tokio::time::sleep(std::time::Duration::from_secs(10)).await;
    //init_search_client(None);
    //test_all_search().await;
    /* let mut graph = get_decision_graph();
    let res = graph.evaluate(json!({"value":10}).into()).await.unwrap();
    dbg!(res.result); */
}

#[allow(dead_code)]
async fn test_search() {
    test_all_search().await.unwrap();
}

#[allow(dead_code)]
async fn build_op() -> PriceEditorOptions {
    let storage = Arc::new(sqlite::SqliteStorage::new(StorageOptions::default()).await.unwrap());
    let storage_handler = Arc::new(StorageHandler::new(storage.clone()));
    let core_op = EditorOptionsBuilder::new().extensions(get_base_def()).add_event_handler(storage_handler).build();
    let loader = Arc::new(SystemLoader::new("./rules".to_owned()));
    PriceEditorOptions::new(core_op, storage, loader)
}
pub fn get_base_def() -> Vec<Extensions> {
    let mut extensions = vec![];

    let top_node = node!("doc", "顶级节点", "DW+", "name"=>"doc".into());

    extensions.push(Extensions::N(top_node));
    let dw = node!("DW", "页面", "fbfx csxm djgc ");
    for node in init_nodes_djgc() {
        extensions.push(Extensions::N(node));
    }
    
    extensions.push(Extensions::N(dw));
    extensions.push(Extensions::E(get_extension()));
    // 分部分项扩展
    extensions.extend(build_extension());
    extensions
}

pub fn get_extension() -> Extension {
    let mut extension = Extension::default();
    let plugin1 =p1_plugin::new();
    extension.add_plugin(Arc::new(plugin1));
    let plugin2 =p2_plugin::new();
    extension.add_plugin(Arc::new(plugin2));
    /* extension.add_op_fn(Arc::new(move |op_state| {
        let engine = SimpleEngine::new(get_decision_graph_content(), true, 1, 100);
        op_state.resource_table.add("rid".to_string(), engine);
        Ok(())
    })); */
    extension
}
