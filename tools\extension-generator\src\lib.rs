//! # Extension Generator
//! 
//! A CLI tool and library for generating Price-RS extension project templates.
//! 
//! This crate provides both a command-line interface and a programmatic API
//! for creating new extension projects with the four-layer architecture:
//! - Nodes (data structures)
//! - Plugins (business logic) 
//! - Commands (CRUD operations)
//! - Router (HTTP interfaces)

pub mod cli;
pub mod config;
pub mod generator;
pub mod templates;
pub mod utils;

// Re-export commonly used types for easier access
pub use cli::types::{ExtensionType, LayerType};
pub use generator::{GenerationContext, Generator};
pub use config::Config;