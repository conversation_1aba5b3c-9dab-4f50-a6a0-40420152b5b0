/*
CREATE TABLE "base_gsj_rate_2022" (
  "sequence_nbr" text(20) NOT NULL,
  "code" text(50),
  "name" text(255),
  "kind" text(10),
  "rate" real(16,6),
  "sort_no" integer(4),
  "remark" text(255),
  "tax_reform_documents_id" text(20),
  "tax_calculation_method" integer(4),
  "tax_paying_region" text(64),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseGsjRate2022 {
    pub sequence_nbr: String,
    pub code: Option<String>,
    pub name: Option<String>,
    pub kind: Option<String>,
    pub rate: Option<f64>,
    pub sort_no: Option<i32>,
    pub remark: Option<String>,
    pub tax_reform_documents_id: Option<String>,
    pub tax_calculation_method: Option<i32>,
    pub tax_paying_region: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseGsjRate2022 {}, "base_gsj_rate_2022");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "code".to_string(),
            "name".to_string(),
            "kind".to_string(),
            "rate".to_string(),
            "sort_no".to_string(),
            "remark".to_string(),
            "tax_reform_documents_id".to_string(),
            "tax_calculation_method".to_string(),
            "tax_paying_region".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "code".to_string(),
            "name".to_string(),
            "kind".to_string(),
            "rate".to_string(),
            "sort_no".to_string(),
            "remark".to_string(),
            "tax_reform_documents_id".to_string(),
            "tax_calculation_method".to_string(),
            "tax_paying_region".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "code".to_string(),
            "name".to_string(),
            "kind".to_string(),
            "rate".to_string(),
            "sort_no".to_string(),
            "remark".to_string(),
            "tax_reform_documents_id".to_string(),
            "tax_calculation_method".to_string(),
            "tax_paying_region".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseGsjRate2022>("base_gsj_rate_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseGsjRate2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_gsj_rate_2022").await?;
    let base_gsj_rate_2022_index = IndexClient::<BaseGsjRate2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_gsj_rate_2022_index);
    Ok(search_service)
}
