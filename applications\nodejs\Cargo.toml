[package]
edition = "2021"
name = "price-nodejs"
version = "0.0.0"

[lib]
crate-type = ["cdylib"]

[dependencies]
# Default enable napi4 feature, see https://nodejs.org/api/n-api.html#node-api-version-matrix
napi = { version = "2.16.17", default-features = false, features = ["napi4","serde-json", "error_anyhow", "tokio_rt"] }
napi-derive = "2.16.13"
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tokio = { workspace = true, features = ["full"] }
async-trait = { workspace = true }
axum  = { workspace = true }
dashmap = { workspace = true }
tower = { workspace = true }
anyhow = { workspace = true }
# Core dependencies
price-budget-core = { workspace = true }
price-rules = { workspace = true }
price-storage = { workspace = true }
moduforge-core = { workspace = true }
price-budget = { workspace = true }
price-web = { workspace = true }
shared = { workspace = true }
[build-dependencies]
napi-build = "2.1.6"

[profile.release]
lto = true
strip = "symbols"
