/*

CREATE TABLE "base_anzhuang_rate_2022" (
  "sequence_nbr" text NOT NULL,
  "fee_code" text(255),
  "fee_name" text(255),
  "library_code" text(255),
  "class_level1" text(255),
  "class_level2" text(255),
  "de_code" text(255),
  "de_name" text(255),
  "allocation_method" integer(11),
  "calculate_base" text(255),
  "rate" real(16,6),
  "r_rate" real(16,6),
  "c_rate" real(16,6),
  "j_rate" real(16,6),
  "relation_list" text(255),
  "relation_list_id" text(20),
  "creat_date" text(20),
  "layer_interval" text(255),
  "height_range" text(255),
  "is_default" integer(11),
  "sort_number" integer(11),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64)
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseAnzhuangRate2022 {
    pub sequence_nbr: String,
    pub fee_code: Option<String>,
    pub fee_name: Option<String>,
    pub library_code: Option<String>,
    pub class_level1: Option<String>,
    pub class_level2: Option<String>,
    pub de_code: Option<String>,
    pub de_name: Option<String>,
    pub allocation_method: Option<i32>,
    pub calculate_base: Option<String>,
    pub rate: Option<f64>,
    pub r_rate: Option<f64>,
    pub c_rate: Option<f64>,
    pub j_rate: Option<f64>,
    pub relation_list: Option<String>,
    pub relation_list_id: Option<String>,
    pub creat_date: Option<String>,
    pub layer_interval: Option<String>,
    pub height_range: Option<String>,
    pub is_default: Option<i32>,
    pub sort_number: Option<i32>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}

//crud!(BaseAnzhuangRate2022 {}, "base_anzhuang_rate_2022");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "fee_code".to_string(),
            "fee_name".to_string(),
            "library_code".to_string(),
            "class_level1".to_string(),
            "class_level2".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "allocation_method".to_string(),
            "calculate_base".to_string(),
            "rate".to_string(),
            "r_rate".to_string(),
            "c_rate".to_string(),
            "j_rate".to_string(),
            "relation_list".to_string(),
            "relation_list_id".to_string(),
            "creat_date".to_string(),
            "layer_interval".to_string(),
            "height_range".to_string(),
            "is_default".to_string(),
            "sort_number".to_string(),
            "rec_user_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "fee_code".to_string(),
            "fee_name".to_string(),
            "library_code".to_string(),
            "class_level1".to_string(),
            "class_level2".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "allocation_method".to_string(),
            "calculate_base".to_string(),
            "rate".to_string(),
            "r_rate".to_string(),
            "c_rate".to_string(),
            "j_rate".to_string(),
            "relation_list".to_string(),
            "relation_list_id".to_string(),
            "creat_date".to_string(),
            "layer_interval".to_string(),
            "height_range".to_string(),
            "is_default".to_string(),
            "sort_number".to_string(),
            "rec_user_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "fee_code".to_string(),
            "fee_name".to_string(),
            "library_code".to_string(),
            "class_level1".to_string(),
            "class_level2".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "allocation_method".to_string(),
            "calculate_base".to_string(),
            "rate".to_string(),
            "r_rate".to_string(),
            "c_rate".to_string(),
            "j_rate".to_string(),
            "relation_list".to_string(),
            "relation_list_id".to_string(),
            "creat_date".to_string(),
            "layer_interval".to_string(),
            "height_range".to_string(),
            "is_default".to_string(),
            "sort_number".to_string(),
            "rec_user_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseAnzhuangRate2022>("base_anzhuang_rate_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseAnzhuangRate2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_anzhuang_rate_2022").await?;
    let base_anzhuang_rate_2022_index = IndexClient::<BaseAnzhuangRate2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_anzhuang_rate_2022_index);
    Ok(search_service)
}
