use mf_state::transaction::{Command, Transaction};
use mf_state::error::StateResult;

///标准分部分项的插入
#[derive(Debug)]
pub struct InsertFbfxCommand;
#[async_trait::async_trait]
 impl Command for InsertFbfxCommand {
    fn name(&self) -> String {
        "insert_fbfx".to_string()
    }
    async fn execute(&self, _tr: &mut Transaction) -> StateResult<()> {
        // 索引插入 数据
        // 补充 数据
        Ok(())
    }
 }



///措施项目插入
#[derive(Debug)]
pub struct InsertCsxmCommand;
#[async_trait::async_trait]
impl Command for InsertCsxmCommand {
    fn name(&self) -> String {
        "insert_csxm".to_string()
    }
    async fn execute(&self, _tr: &mut Transaction) -> StateResult<()> {
        // 索引插入 数据
        // 补充 数据
        Ok(())
    }
}