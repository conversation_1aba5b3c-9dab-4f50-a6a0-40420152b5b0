/*
CREATE TABLE "base_de_relation_variable_describe" (
  "sequence_nbr" text(19) NOT NULL,
  "list_code" text(255),
  "job_content" text(255),
  "de_id" text(19),
  "library_code" text(255),
  "library_name" text(255),
  "de_code_f" text(255),
  "de_name_f" text(255),
  "groupid" text(11),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeRelationVariableDescribe {
    pub sequence_nbr: String,
    pub list_code: Option<String>,
    pub job_content: Option<String>,
    pub de_id: Option<String>,
    pub library_code: Option<String>,
    pub library_name: Option<String>,
    pub de_code_f: Option<String>,
    pub de_name_f: Option<String>,
    pub groupid: Option<String>,
}
//crud!(BaseDeRelationVariableDescribe {}, "base_de_relation_variable_describe");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "list_code".to_string(),
            "job_content".to_string(),
            "de_id".to_string(),
            "library_code".to_string(),
            "library_name".to_string(),
            "de_code_f".to_string(),
            "de_name_f".to_string(),
            "groupid".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "list_code".to_string(),
            "job_content".to_string(),
            "de_id".to_string(),
            "library_code".to_string(),
            "library_name".to_string(),
            "de_code_f".to_string(),
            "de_name_f".to_string(),
            "groupid".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "list_code".to_string(),
            "job_content".to_string(),
            "de_id".to_string(),
            "library_code".to_string(),
            "library_name".to_string(),
            "de_code_f".to_string(),
            "de_name_f".to_string(),
            "groupid".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeRelationVariableDescribe>("base_de_relation_variable_describe", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeRelationVariableDescribe>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_relation_variable_describe").await?;
    let base_de_relation_variable_describe_index = IndexClient::<BaseDeRelationVariableDescribe> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_relation_variable_describe_index);
    Ok(search_service)
}
