use thiserror::Error;

#[derive(Debug, Error)]
pub enum SearchError {
    #[error("搜索客户端错误: {0}")]
    ClientError(String),

    #[error("索引错误: {0}")]
    IndexError(String),

    #[error("文档错误: {0}")]
    DocumentError(String),

    #[error("序列化错误: {0}")]
    SerializationError(String),

    #[error("未知错误: {0}")]
    UnknownError(String),
}

pub type SearchResult<T> = Result<T, SearchError>;

impl From<meilisearch_sdk::errors::Error> for SearchError {
    fn from(error: meilisearch_sdk::errors::Error) -> Self {
        match error {
            meilisearch_sdk::errors::Error::Meilisearch(e) => SearchError::ClientError(format!("Meilisearch错误: {}", e)),
            meilisearch_sdk::errors::Error::MeilisearchCommunication(e) => SearchError::ClientError(format!("Meilisearch通信错误: {}", e)),
            meilisearch_sdk::errors::Error::ParseError(e) => SearchError::SerializationError(format!("解析错误: {}", e)),
            meilisearch_sdk::errors::Error::Timeout => SearchError::ClientError("搜索请求超时".to_string()),
            meilisearch_sdk::errors::Error::InvalidRequest => SearchError::ClientError("无效的搜索请求".to_string()),
            meilisearch_sdk::errors::Error::CantUseWithoutApiKey(msg) => SearchError::ClientError(format!("缺少API密钥: {}", msg)),
            meilisearch_sdk::errors::Error::TenantTokensInvalidApiKey => SearchError::ClientError("租户令牌API密钥无效".to_string()),
            meilisearch_sdk::errors::Error::TenantTokensExpiredSignature => SearchError::ClientError("租户令牌签名已过期".to_string()),
            meilisearch_sdk::errors::Error::InvalidTenantToken(e) => SearchError::ClientError(format!("无效的租户令牌: {}", e)),
            meilisearch_sdk::errors::Error::HttpError(e) => SearchError::ClientError(format!("HTTP错误: {}", e)),
            meilisearch_sdk::errors::Error::Yaup(e) => SearchError::SerializationError(format!("YAML解析错误: {}", e)),
            meilisearch_sdk::errors::Error::Uuid(e) => SearchError::ClientError(format!("UUID错误: {}", e)),
            meilisearch_sdk::errors::Error::InvalidUuid4Version => SearchError::ClientError("无效的UUID4版本".to_string()),
            meilisearch_sdk::errors::Error::Other(e) => SearchError::UnknownError(format!("其他错误: {}", e)),
            _ => SearchError::UnknownError(format!("未知错误: {:?}", error)),
        }
    }
}

impl From<serde_json::Error> for SearchError {
    fn from(error: serde_json::Error) -> Self {
        SearchError::SerializationError(error.to_string())
    }
}
