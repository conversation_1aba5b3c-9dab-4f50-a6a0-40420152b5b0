use price_web::axum::{
    routing::{get, post, put, delete},
    Router,
};

pub mod handlers;
pub mod dto;
use handlers::*;

/// {{ description }} Router 构建器
pub struct {{ node_prefix }}Router;

impl {{ node_prefix }}Router {
    /// 创建{{ description }}的标准路由
    pub fn create_router<S>() -> Router<S> 
    where
        S: Clone + Send + Sync + 'static,
    {
        Router::new()
            // {{ description }}管理路由
            .nest("/{{ names.kebab }}", Self::{{ names.snake }}_routes())
            // 计算相关路由
            .nest("/{{ names.kebab }}/calc", Self::calculation_routes())
            // 健康检查和信息路由
            .route("/{{ names.kebab }}/health", get(health_check))
            .route("/{{ names.kebab }}/info", get(get_extension_info))
    }

    /// {{ description }}相关路由
    fn {{ names.snake }}_routes<S>() -> Router<S> 
    where
        S: <PERSON><PERSON> + Send + Sync + 'static,
    {
        Router::new()
            .route("/", get(list_{{ names.snake }}).post(create_{{ names.snake }}))
            .route("/:id", 
                get(get_{{ names.snake }})
                .put(update_{{ names.snake }})
                .delete(delete_{{ names.snake }})
            )
            .route("/:id/rows", get(list_{{ names.snake }}_rows).post(add_{{ names.snake }}_row))
            .route("/:id/rows/:row_id", 
                get(get_{{ names.snake }}_row)
                .put(update_{{ names.snake }}_row)
                .delete(delete_{{ names.snake }}_row)
            )
            .route("/:id/validate", post(validate_{{ names.snake }}))
            .route("/:id/export", post(export_{{ names.snake }}))
    }

    /// 计算相关路由
    fn calculation_routes<S>() -> Router<S> 
    where
        S: Clone + Send + Sync + 'static,
    {
        Router::new()
            .route("/calculate", post(calculate_{{ names.snake }}))
            .route("/batch-calculate", post(batch_calculate_{{ names.snake }}))
            .route("/preview", post(preview_calculation))
            .route("/formulas", get(list_calculation_formulas))
            .route("/formulas/:formula_id", get(get_calculation_formula))
    }
}

// 重新导出标准化的响应类型
pub use price_web::ResponseResult;

pub type ApiResult<T> = ResponseResult<T>;