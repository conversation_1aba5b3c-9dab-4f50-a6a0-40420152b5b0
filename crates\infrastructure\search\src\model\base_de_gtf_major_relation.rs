/*
CREATE TABLE "base_de_gtf_major_relation" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "add_rate_height" varchar,
  "fgjzgc" varchar,
  "lhgc" varchar,
  "ylgc" varchar,
  "gjxsgc" varchar,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ('A'),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "description" varchar,
  "default_flag" decimal
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeGtfMajorRelation {
    pub sequence_nbr: String,
    pub add_rate_height: Option<String>,
    pub fgjzgc: Option<String>,
    pub lhgc: Option<String>,
    pub ylgc: Option<String>,
    pub gjxsgc: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub default_flag: Option<f64>,
}
//crud!(BaseDeGtfMajorRelation {}, "base_de_gtf_major_relation");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "add_rate_height".to_string(),
            "fgjzgc".to_string(),
            "lhgc".to_string(),
            "ylgc".to_string(),
            "gjxsgc".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "add_rate_height".to_string(),
            "fgjzgc".to_string(),
            "lhgc".to_string(),
            "ylgc".to_string(),
            "gjxsgc".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "default_flag".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "add_rate_height".to_string(),
            "fgjzgc".to_string(),
            "lhgc".to_string(),
            "ylgc".to_string(),
            "gjxsgc".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "default_flag".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeGtfMajorRelation>("base_de_gtf_major_relation", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeGtfMajorRelation>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_gtf_major_relation").await?;
    let base_de_gtf_major_relation_index = IndexClient::<BaseDeGtfMajorRelation> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_gtf_major_relation_index);
    Ok(search_service)
}
