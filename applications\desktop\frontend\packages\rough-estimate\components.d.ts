/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AResult: typeof import('ant-design-vue/es')['Result']
    ASkeleton: typeof import('ant-design-vue/es')['Skeleton']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATypographyText: typeof import('ant-design-vue/es')['TypographyText']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
