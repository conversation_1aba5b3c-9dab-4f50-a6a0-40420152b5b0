# 工程造价计价软件业务框架

## 1. 业务层级结构设计

### 1.1 业务层级调整说明
基于河北省工程造价政策调研和广联达等主流软件分析，对原业务结构进行如下调整：

**调整点一：清单标准选择优化**
- 原结构：仅区分"河北13国标"和"24国标"  
- 调整为：GB50500-2013、GB/T50500-2024两个版本并行支持
- 调整原因：根据河北省调研，24版标准2025年9月1日实施，需要过渡期并行支持

**调整点二：定额标准关联规则修正**  
- 原结构：24国标仅可选择22河北定额
- 调整为：24国标清单可选择22河北定额，13国标清单可选择12定额和22定额
- 调整原因：标准兼容性需要基于实际发布时间和适用范围确定

**调整点三：规费计算规则更新**
- 原结构：仅12定额存在规费，22定额取消规费
- 调整为：22定额标准规费并入综合单价，12定额规费单独计算
- 调整原因：符合河北省2022版消耗量标准的实际变化

### 1.2 完整业务层级结构
```
省份地区选择
    ↓
计价标准关联加载
├── 清单标准 (GB50500-2013 / GB/T50500-2024)
├── 定额标准 (河北12定额 / 河北22定额)  
├── 估价表 (省站规则 / 沧州 / 保定 / 秦皇岛)
└── 兼容性校验
    ↓
业务产品选择 (MVP阶段)
├── 概算 (预留入口)
├── 预算 (完整功能) ★核心
├── 结算 (预留入口)
└── 审核 (预留入口)
    ↓
预算文件类型选择
├── 招标文件创建 (清单计价模式)
├── 投标文件创建 (清单计价模式)
├── 单位工程文件创建 (清单计价模式)
└── 定额工料机法文件创建 (定额计价模式)
    ↓
费用计算模式配置
├── 全费用单价模式 (人+材+机+管+利+措施+安文费+规费+税金)
└── 清单计价模式 (人+材+机+管+利)
    ↓
项目工程结构管理
├── 工程项目层级 (根节点)
├── 单项工程层级 (可含子单项)
└── 单位工程层级 (数据叶子节点)
```

## 2. 业务模块架构

### 2.1 控制台模块

**模块定位**：项目创建和配置管理的统一入口
**核心业务**：
- **地区标准库管理**：维护不同地区的标准库映射关系
- **项目创建向导**：引导用户完成项目配置和创建流程
- **标准兼容性校验**：确保所选标准组合的业务可行性
- **项目模板管理**：提供常用项目类型的快速创建模板

### 2.2 工作台模块  
**模块定位**：项目编制和数据管理的主要工作界面
**核心业务**：
- **项目结构管理**：工程项目→单项工程→单位工程的层级管理
- **多标签编辑**：支持同时编辑多个单位工程进行对比
- **实时计算引擎**：数据变更自动触发相关计算更新
- **状态同步管理**：多用户协同时的数据状态同步

### 2.3 编制模块
**模块定位**：具体的造价数据编制和计算功能
**核心业务**：
- **分部分项管理**：清单项目和定额子目的组价编制
- **措施项目管理**：技术措施和通用措施的费用计算
- **其他项目管理**：零星工作、计日工、总承包服务费管理
- **费用汇总计算**：多层级费用汇总和税费计算

### 2.4 报表模块
**模块定位**：成果文件生成和格式化输出
**核心业务**：
- **标准报表生成**：招标文件、投标文件、结算文件等标准格式
- **自定义报表设计**：支持用户自定义报表模板和格式
- **多格式导出**：支持PDF、Excel、Word等多种格式导出
- **打印排版优化**：适配不同纸张规格的打印输出需求

### 2.5 电子标模块
**模块定位**：电子招投标数据交换和对接
**核心业务**：
- **标准XML导出**：支持不同招投标平台的数据格式要求
- **数据格式转换**：在不同厂商格式间进行数据转换
- **电子签章集成**：集成CA数字证书和电子签章功能
- **数据校验确认**：确保导出数据的完整性和准确性

## 3. 数据关联架构

### 3.1 标准库关联体系
```
地区维度关联：
河北省 → 石家庄市/保定市/沧州市... → 对应估价表规则

标准库版本关联：
GB50500-2013清单 ←→ 河北12定额、河北22定额  
GB/T50500-2024清单 ←→ 河北22定额

计价模式关联：
全费用单价模式 ←→ 特定单价构成模板
清单计价模式 ←→ 基础单价构成模板
```

### 3.2 项目数据关联体系  
```
工程项目 (1)
    ↓ 包含
单项工程 (N) 
    ↓ 包含  
单位工程 (N)
    ↓ 包含
分部工程 (N，最多4级)
    ↓ 包含
清单项目 (N)
    ↓ 关联
定额子目 (N)
    ↓ 关联  
人材机明细 (N)
```

### 3.3 费用计算关联体系
```
基础费用计算链：
定额人工费 + 定额材料费 + 定额机械费 = 定额直接费
    ↓
定额直接费 × (1+管理费率) × (1+利润率) = 不含税综合单价
    ↓
分部分项工程费 = Σ(清单工程量 × 不含税综合单价)
    ↓
措施费 = 分部分项工程费 × 措施费率
    ↓  
规费 = (分部分项工程费 + 措施费) × 规费费率 (仅12定额)
    ↓
税金 = 全部费用 × 增值税税率 (9%)
```

## 4. 业务流程设计

### 4.1 项目创建完整流程
```
启动控制台
    ↓
选择河北省 → 加载河北省标准库列表  
    ↓
选择业务类型 → 预算 (其他预留入口)
    ↓  
选择文件类型 → 招标/投标/单位工程/工料机法
    ↓
弹窗录入项目信息
├── 项目名称 (必填)
├── 项目编码 (选填，支持自动生成)
├── 建设单位 (必填)
├── 其他参建单位 (选填)
├── 清单标准选择 (13版/24版)
├── 定额标准选择 (12定额/22定额)
├── 估价表选择 (省站/沧州/保定/秦皇岛)
└── 费用计算模式 (全费用单价/清单计价)
    ↓
标准兼容性自动校验
    ↓
生成项目文件结构 → 跳转工作台界面
```

### 4.2 工作台界面切换流程
```
工作台启动
    ↓
窗体顶部：软件Logo | 保存 | 撤销 | 文件路径 | 用户信息 | 窗体控制
    ↓
功能栏识别：概预结审类型 + 招标投标单位工料机法类型
    ↓
主菜单切换：
├── "文件" → 项目文件级别操作
├── "编制" → 业务数据编制功能 ★主要工作区
├── "报表" → 报表模板和生成功能
└── "电子标" → 电子招投标数据导出
    ↓
左侧结构树 + 右侧编辑区
```

### 4.3 分部分项编制流程
```
选中单位工程 → 进入编制页签
    ↓
页签选择：工程概况 | 造价分析 | 取费表 | 分部分项 | 措施项目 | 人材机汇总 | 其他项目 | 费用汇总
    ↓
选择"分部分项"页签
    ↓
编辑区结构：
├── 上半部分：清单定额数据表格 (增删改查)
└── 下半部分：多页签明细区
    ├── 人材机明细
    ├── 单价构成  
    ├── 标准换算
    ├── 换算信息
    ├── 特征及内容
    └── 工程量明细
    ↓
数据层级：单位工程行 → 分部行(最多4级) → 清单行 → 定额行
    ↓
双击编码触发：清单库/定额库数据选择器
    ↓
自动关联：定额选择自动带出人材机明细数据
```

## 5. 关键业务规则

### 5.1 标准库业务规则
**标准选择约束规则**
- GB50500-2013清单标准 ←→ 河北12定额、河北22定额 (兼容)
- GB/T50500-2024清单标准 ←→ 河北22定额 (兼容)  
- 河北22定额标准 → 可选择地区估价表 (沧州/保定/秦皇岛)
- 河北12定额标准 → 使用省站统一规则，无地区估价表选项

**费用构成规则差异**
- **12定额+全费用单价**：人+材+机+管+利+措施+安文费+规费+税金
- **22定额+全费用单价**：人+材+机+管+利+措施+安文费+税金 
- **清单计价模式**：人+材+机+管+利 (措施、规费、安文费、税金在措施项目层、汇总层计算)

### 5.2 项目结构业务规则

**层级创建约束**

- 工程项目层级为根节点，不可删除
- 单项工程可创建子单项，支持多级嵌套
- 单位工程可挂在单项工程、工程项目下，作为最终叶子节点
- 单位工程存在时，同级不允许创建单项工程节点

**分部结构约束**  
- 分部工程最大支持四级分部嵌套
- 定额子目必须挂在清单项目下，不允许与清单同级
- 单位工程行作为根节点不可删除
- 页签切换时定位到上次选中行，默认为单位工程行

### 5.3 数据编辑业务规则
**清单定额关联规则**
- 双击清单编码：调用清单标准库选择器，插入标准清单项目  
- 双击定额编码：调用定额标准库选择器，插入定额子目
- 插入定额自动关联：带出对应的人材机明细数据到底部明细区
- 定额含量支持调整：影响单价计算，需要重新计算费用

**数据汇总规则**
- 工程项目层级：下属所有单项、单位数据的去重汇总
- 单项工程层级：下属所有单位工程数据的去重汇总  
- 单位工程层级：实际数据产生层级，包含完整页签功能
- 分部汇总：各级分部为下级分部和清单数据的汇总展示

## 6. 核心业务实体

### 6.1 项目管理实体

**工程项目 (Project)**
- 项目基础信息：项目名称、编码、建设单位、项目类型
- 标准配置信息：清单标准、定额标准、估价表、计价模式
- 参建单位信息：设计单位、监理单位、施工单位
- 时间进度信息：创建时间、计划完工时间、实际进度

**单项工程 (SubProject)**  
- 单项基础信息：单项名称、编码、工程类型、建设规模
- 层级关系：父单项ID、子单项列表、所属工程项目
- 汇总统计：包含单位工程数量、总投资、主要技术指标

**单位工程 (UnitProject)**
- 单位基础信息：单位名称、编码、专业类型、建筑面积
- 技术特征：结构类型、装修标准、设备等级
- 造价信息：总造价、单方造价、主要费用构成

### 6.2 计价数据实体  
**清单项目 (BillItem)**
- 项目编码：符合清单标准的12位编码
- 项目信息：项目名称、项目特征、规格型号
- 计量信息：计量单位、工程量表达式、实际工程量
- 价格信息：综合单价、合价

**定额子目 (QuotaSubItem)**
- 定额编码：定额标准中的唯一编码
- 定额信息：定额名称、计量单位、基础单价
- 消耗关系：消耗量(含量)、调整系数、小计金额
- 费用明细：人工费、材料费、机械费分项金额
- 价格信息：取费专业、单价构成文件、施工组织措施类别

**人材机明细 (LaborMaterialMachine)**  
- 资源编码：人工、材料、机械的标准编码
- 资源信息：名称、规格型号、计量单位
- 价格信息：基础价格、市场价格、调整价格
- 消耗信息：定额消耗量、实际消耗量、损耗率

### 6.3 费用汇总实体
**造价汇总 (CostSummary)**
- 分部分项工程费：各专业工程费用小计和合计
- 措施项目费：技术措施费和通用措施费
- 其他项目费：零星工作、计日工、总承包服务费
- 规费税金：社保费、住房公积金、增值税等

**取费标准 (FeeStandard)**  
- 费率配置：管理费率、利润率、规费费率、税率
- 专业分类：不同工程专业对应的费率差异
- 企业资质：不同资质等级对应的费率标准
- 地区调整：不同地区的费率调整系数

## 7. 关键业务约束

### 7.1 数据完整性约束
- **项目必须包含标准配置**：清单标准、定额标准为必选项
- **单位工程必须包含清单**：单位工程至少包含一个分部分项清单
- **清单必须关联定额**：清单项目必须配置至少一个定额子目
- **费用计算链路完整**：从人材机到综合单价的计算链路不可中断

### 7.2 业务逻辑约束  
- **层级关系约束**：严格按照工程项目→单项→单位→分部→清单→定额的层级
- **标准兼容性约束**：所选标准组合必须通过兼容性校验
- **编码唯一性约束**：同一层级内的编码不允许重复
- **计算模式一致性**：同一项目内必须使用统一的费用计算模式

### 7.3 状态流转约束
- **编制状态管控**：草稿→编制中→审核中→已确认→已归档
- **数据锁定机制**：确认状态数据不允许直接修改，需退回到编制状态
- **版本管理约束**：重要变更自动生成版本，支持版本间对比和回退
- **权限控制约束**：不同角色用户对应不同的数据操作权限

## 8. 业务扩展设计

### 8.1 地区扩展策略
**标准化扩展模式**
- **标准库配置化**：通过配置文件支持新地区标准库加载
- **业务规则参数化**：将地区差异业务规则参数化配置
- **界面元素动态化**：根据地区特色动态显示或隐藏特定功能
- **数据格式兼容化**：支持不同地区数据格式的导入导出

### 8.2 功能模块扩展策略

**概算模块扩展**：基于投资估算指标的快速概算功能
**结算模块扩展**：基于实际施工量的结算编制和对比分析功能  
**审核模块扩展**：造价文件审核、差异分析、审核意见管理功能
**协同模块扩展**：多人协同编制、版本管理、审批流程功能

---

**文档版本**：V1.0  
**编制日期**：2025年8月28日  
**适用范围**：工程造价计价软件MVP版本  
**下级文档**：详情业务说明.md、交互说明.md