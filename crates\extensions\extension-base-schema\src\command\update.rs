use async_trait::async_trait;
use mf_state::{Transaction, transaction::Command};
use mf_transform::TransformResult;
use serde::Deserialize;
use serde_json::Value;
use mf_model::imbl as im;
#[derive(Debug, Deserialize)]
pub struct UpdateCommand {
    pub point_id: Box<str>,
    pub values: im::HashMap<String, Value>,
}

impl UpdateCommand {
    pub fn new(
        point_id: Box<str>,
        values: im::HashMap<String, Value>,
    ) -> Self {
        Self { point_id, values }
    }
}

#[async_trait]
impl Command for UpdateCommand {
    async fn execute(
        &self,
        tr: &mut Transaction,
    ) -> TransformResult<()> {
        tr.set_node_attribute(self.point_id.clone(), self.values.clone())?;
        Ok(())
    }

    fn name(&self) -> String {
        "UpdateCommand".to_string()
    }
}
