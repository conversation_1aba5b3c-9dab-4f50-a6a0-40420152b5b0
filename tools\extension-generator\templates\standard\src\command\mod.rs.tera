use async_trait::async_trait;
use mf_macro::impl_command;
use mf_state::{Transaction, transaction::Command};
use mf_transform::TransformResult;

// 插入{{ description }}
impl_command!(Insert{{ node_prefix }}, (async |tr: &mut Transaction| {
    // TODO: 实现插入{{ description }}的逻辑
    // 例如：
    // 1. 验证输入数据
    // 2. 创建新的节点
    // 3. 更新状态
    
    println!("执行插入{{ description }}命令");
    Ok(())
}));

// 更新{{ description }}
impl_command!(Update{{ node_prefix }}, (async |tr: &mut Transaction| {
    // TODO: 实现更新{{ description }}的逻辑
    // 例如：
    // 1. 查找目标节点
    // 2. 验证更新数据
    // 3. 更新节点属性
    // 4. 重新计算相关数据
    
    println!("执行更新{{ description }}命令");
    Ok(())
}));

// 删除{{ description }}
impl_command!(Delete{{ node_prefix }}, (async |tr: &mut Transaction| {
    // TODO: 实现删除{{ description }}的逻辑
    // 例如：
    // 1. 查找目标节点
    // 2. 检查删除权限
    // 3. 处理关联数据
    // 4. 执行删除操作
    
    println!("执行删除{{ description }}命令");
    Ok(())
}));

// 计算{{ description }}
impl_command!(Calculate{{ node_prefix }}, (async |tr: &mut Transaction| {
    // TODO: 实现计算{{ description }}的逻辑
    // 例如：
    // 1. 获取相关数据
    // 2. 执行业务计算
    // 3. 更新计算结果
    // 4. 触发后续处理
    
    println!("执行计算{{ description }}命令");
    Ok(())
}));

/// 命令工厂
/// 用于创建和管理{{ description }}相关的命令
pub struct {{ node_prefix }}CommandFactory;

impl {{ node_prefix }}CommandFactory {
    /// 创建插入命令
    pub fn create_insert_command() -> Box<dyn Command> {
        Box::new(Insert{{ node_prefix }})
    }
    
    /// 创建更新命令
    pub fn create_update_command() -> Box<dyn Command> {
        Box::new(Update{{ node_prefix }})
    }
    
    /// 创建删除命令
    pub fn create_delete_command() -> Box<dyn Command> {
        Box::new(Delete{{ node_prefix }})
    }
    
    /// 创建计算命令
    pub fn create_calculate_command() -> Box<dyn Command> {
        Box::new(Calculate{{ node_prefix }})
    }
}

/// 命令类型枚举
#[derive(Debug, Clone, PartialEq)]
pub enum {{ node_prefix }}CommandType {
    Insert,
    Update,
    Delete,
    Calculate,
}

impl {{ node_prefix }}CommandType {
    /// 获取命令类型的字符串表示
    pub fn as_str(&self) -> &str {
        match self {
            {{ node_prefix }}CommandType::Insert => "insert",
            {{ node_prefix }}CommandType::Update => "update",
            {{ node_prefix }}CommandType::Delete => "delete",
            {{ node_prefix }}CommandType::Calculate => "calculate",
        }
    }
    
    /// 获取命令类型的描述
    pub fn description(&self) -> &str {
        match self {
            {{ node_prefix }}CommandType::Insert => "插入{{ description }}",
            {{ node_prefix }}CommandType::Update => "更新{{ description }}",
            {{ node_prefix }}CommandType::Delete => "删除{{ description }}",
            {{ node_prefix }}CommandType::Calculate => "计算{{ description }}",
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_command_factory() {
        let insert_cmd = {{ node_prefix }}CommandFactory::create_insert_command();
        let update_cmd = {{ node_prefix }}CommandFactory::create_update_command();
        let delete_cmd = {{ node_prefix }}CommandFactory::create_delete_command();
        let calc_cmd = {{ node_prefix }}CommandFactory::create_calculate_command();
        
        // 验证命令创建成功
        assert!(!insert_cmd.as_ref().type_id().is_null());
        assert!(!update_cmd.as_ref().type_id().is_null());
        assert!(!delete_cmd.as_ref().type_id().is_null());
        assert!(!calc_cmd.as_ref().type_id().is_null());
    }
    
    #[test]
    fn test_command_types() {
        assert_eq!({{ node_prefix }}CommandType::Insert.as_str(), "insert");
        assert_eq!({{ node_prefix }}CommandType::Update.as_str(), "update");
        assert_eq!({{ node_prefix }}CommandType::Delete.as_str(), "delete");
        assert_eq!({{ node_prefix }}CommandType::Calculate.as_str(), "calculate");
        
        assert_eq!({{ node_prefix }}CommandType::Insert.description(), "插入{{ description }}");
        assert_eq!({{ node_prefix }}CommandType::Update.description(), "更新{{ description }}");
        assert_eq!({{ node_prefix }}CommandType::Delete.description(), "删除{{ description }}");
        assert_eq!({{ node_prefix }}CommandType::Calculate.description(), "计算{{ description }}");
    }
}