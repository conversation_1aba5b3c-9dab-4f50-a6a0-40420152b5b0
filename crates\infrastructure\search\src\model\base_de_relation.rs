/*
CREATE TABLE "base_de_relation" (
  "sequence_nbr" text(19) NOT NULL,
  "list_code" text(255),
  "job_content" text(255),
  "groupid" text(11),
  "library_code" text(255),
  "library_name" text(255),
  "de_code_f" text(255),
  "de_name_f" text(255),
  "unit_f" text(255),
  "library_code_z" text(255),
  "library_code_relation" text(255),
  "relation_content" text(255),
  "de_code_z" text(255),
  "de_name_z" text(255),
  "unit_z" text(255),
  "quantity" text(255),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeRelation {
    pub sequence_nbr: String,
    pub list_code: Option<String>,
    pub job_content: Option<String>,
    pub groupid: Option<String>,
    pub library_code: Option<String>,
    pub library_name: Option<String>,
    pub de_code_f: Option<String>,
    pub de_name_f: Option<String>,
    pub unit_f: Option<String>,
    pub library_code_z: Option<String>,
    pub library_code_relation: Option<String>,
    pub relation_content: Option<String>,
    pub de_code_z: Option<String>,
    pub de_name_z: Option<String>,
    pub unit_z: Option<String>,
    pub quantity: Option<String>,
}
//crud!(BaseDeRelation {}, "base_de_relation");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "list_code".to_string(),
            "job_content".to_string(),
            "groupid".to_string(),
            "library_code".to_string(),
            "library_name".to_string(),
            "de_code_f".to_string(),
            "de_name_f".to_string(),
            "unit_f".to_string(),
            "library_code_z".to_string(),
            "library_code_relation".to_string(),
            "relation_content".to_string(),
            "de_code_z".to_string(),
            "de_name_z".to_string(),
            "unit_z".to_string(),
            "quantity".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "list_code".to_string(),
            "job_content".to_string(),
            "groupid".to_string(),
            "library_code".to_string(),
            "library_name".to_string(),
            "de_code_f".to_string(),
            "de_name_f".to_string(),
            "unit_f".to_string(),
            "library_code_z".to_string(),
            "library_code_relation".to_string(),
            "relation_content".to_string(),
            "de_code_z".to_string(),
            "de_name_z".to_string(),
            "unit_z".to_string(),
            "quantity".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "list_code".to_string(),
            "job_content".to_string(),
            "groupid".to_string(),
            "library_code".to_string(),
            "library_name".to_string(),
            "de_code_f".to_string(),
            "de_name_f".to_string(),
            "unit_f".to_string(),
            "library_code_z".to_string(),
            "library_code_relation".to_string(),
            "relation_content".to_string(),
            "de_code_z".to_string(),
            "de_name_z".to_string(),
            "unit_z".to_string(),
            "quantity".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeRelation>("base_de_relation", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeRelation>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_relation").await?;
    let base_de_relation_index = IndexClient::<BaseDeRelation> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_relation_index);
    Ok(search_service)
}
