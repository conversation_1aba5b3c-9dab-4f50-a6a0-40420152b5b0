use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

#[derive(Debug, PartialEq, serde::Serial<PERSON>, serde::Deserialize, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
#[getset(get_mut = "pub", get = "pub", set = "pub")]
pub struct LogConfig {
    log_dir: String,
    log_level: String,
}

///服务启动配置
#[derive(Debug, PartialEq, serde::Serialize, serde::Deserialize, Clone, Getters, Setters, MutGetters, Default)]
#[getset(get_mut = "pub", get = "pub", set = "pub")]
pub struct ApplicationConfig {
    ///default path "target/logs/"
    log: LogConfig,
}

impl ApplicationConfig {
    pub fn from_toml(toml_data: &str) -> Self {
        match toml::from_str(toml_data) {
            Ok(e) => e,
            Err(e) => panic!("{}", e),
        }
    }
}
