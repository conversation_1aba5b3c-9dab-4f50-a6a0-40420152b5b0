[package]
name = "extension-config"
version = { workspace = true }
edition = { workspace = true }
authors = { workspace = true }
description = "全局配置 extension"
[dependencies]
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
async-trait = { workspace = true }
# 使用标准化的 web 响应和错误处理
price-web = { workspace = true }
shared = { workspace = true }

moduforge-model = { workspace = true }
moduforge-state = { workspace = true }
moduforge-transform = { workspace = true }
moduforge-core = { workspace = true }
moduforge-macros = { workspace = true }
moduforge-macros-derive = { workspace = true }