/*
CREATE TABLE "base_list_feature_down_pull_menu_order" (
  "sequence_nbr" text(20) NOT NULL,
  "library_code" text(50),
  "list_code" text(50),
  "feature_name" text,
  "down_pull_menu" text(255),
  "rn" integer(11),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};
#[derive(Debug, Serialize, Deserialize)]
pub struct BaseListFeatureDownPullMenuOrder {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub list_code: Option<String>,
    pub feature_name: Option<String>,
    pub down_pull_menu: Option<String>,
    pub rn: Option<i32>,
}
//crud!(BaseListFeatureDownPullMenuOrder {}, "base_list_feature_down_pull_menu_order");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "list_code".to_string(),
            "feature_name".to_string(),
            "down_pull_menu".to_string(),
            "rn".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "list_code".to_string(),
            "feature_name".to_string(),
            "down_pull_menu".to_string(),
            "rn".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "list_code".to_string(),
            "feature_name".to_string(),
            "down_pull_menu".to_string(),
            "rn".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseListFeatureDownPullMenuOrder>("base_list_feature_down_pull_menu_order", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseListFeatureDownPullMenuOrder>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_list_feature_down_pull_menu_order").await?;
    let base_list_index = IndexClient::<BaseListFeatureDownPullMenuOrder> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
