/*
CREATE TABLE "base_de_job_content" (
  "library_code" varchar,
  "de_id" varchar,
  "de_code" varchar,
  "de_job_content" varchar,
  "de_name" varchar,
  "id" integer PRIMARY KEY AUTOINCREMENT NOT NULL
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeJobContent {
    pub id: i32,
    pub library_code: Option<String>,
    pub de_id: Option<String>,
    pub de_code: Option<String>,
    pub de_job_content: Option<String>,
    pub de_name: Option<String>,
}
//crud!(BaseDeJobContent {}, "base_de_job_content");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("id".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_job_content".to_string(),
            "de_name".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_job_content".to_string(),
            "de_name".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_job_content".to_string(),
            "de_name".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeJobContent>("base_de_job_content", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeJobContent>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_job_content").await?;
    let base_de_job_content_index = IndexClient::<BaseDeJobContent> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_job_content_index);
    Ok(search_service)
}
