use anyhow::Result;
use clap::{Parser, Subcommand};
use colored::*;
use std::path::PathBuf;

mod cli;
mod generator;
mod config;
mod templates;
mod utils;

use cli::*;

/// Price-RS Extension Generator
/// 
/// 一个专门用于生成 Price-RS 扩展项目模板的CLI工具
/// 支持生成符合四层架构（Nodes/Plugins/Commands/Router）的完整项目结构
#[derive(Parser)]
#[command(
    name = "price-ext-gen",
    about = "Price-RS Extension Project Generator",
    long_about = "Generate Price-RS extension projects with standard four-layer architecture (Nodes/Plugins/Commands/Router)",
    version = env!("CARGO_PKG_VERSION"),
    author = env!("CARGO_PKG_AUTHORS")
)]
struct Cli {
    #[command(subcommand)]
    command: Commands,

    /// 详细输出模式
    #[arg(short, long, global = true)]
    verbose: bool,

    /// 静默模式，仅输出必要信息
    #[arg(short, long, global = true)]
    quiet: bool,
}

#[derive(Subcommand)]
enum Commands {
    /// 生成新的扩展项目
    New {
        /// 扩展名称（如：djgc, rcj, fbfx-csxm）
        name: String,
        
        /// 扩展描述
        #[arg(short, long)]
        description: Option<String>,
        
        /// 输出目录
        #[arg(short, long)]
        output: Option<PathBuf>,
        
        /// 扩展类型
        #[arg(long, value_enum, default_value = "standard")]
        extension_type: ExtensionType,
        
        /// 启用的组件层
        #[arg(long, value_delimiter = ',')]
        layers: Option<Vec<LayerType>>,
        
        /// 作者信息
        #[arg(long)]
        author: Option<String>,
        
        /// 强制覆盖已存在的目录
        #[arg(short, long)]
        force: bool,

        /// 交互式模式
        #[arg(short, long)]
        interactive: bool,
    },
    
    /// 列出可用的模板
    List {
        /// 显示详细信息
        #[arg(short, long)]
        detailed: bool,
    },
    
    /// 验证扩展项目结构
    Validate {
        /// 要验证的项目路径
        path: PathBuf,
        
        /// 修复检测到的问题
        #[arg(short, long)]
        fix: bool,
    },
    
    /// 配置管理
    Config {
        #[command(subcommand)]
        action: ConfigAction,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();
    
    // 初始化日志
    utils::init_logging(cli.verbose, cli.quiet)?;
    
    // 显示欢迎信息
    if !cli.quiet {
        print_welcome();
    }
    
    // 执行命令
    match cli.command {
        Commands::New { 
            name, 
            description, 
            output, 
            extension_type, 
            layers, 
            author, 
            force,
            interactive 
        } => {
            let cmd = NewCommand::new(
                name, 
                description, 
                output, 
                extension_type, 
                layers, 
                author, 
                force, 
                interactive
            );
            cmd.execute().await?;
        }
        Commands::List { detailed } => {
            let cmd = ListCommand::new(detailed);
            cmd.execute().await?;
        }
        Commands::Validate { path, fix } => {
            let cmd = ValidateCommand::new(path, fix);
            cmd.execute().await?;
        }
        Commands::Config { action } => {
            let cmd = ConfigCommand::new(action);
            cmd.execute().await?;
        }
    }
    
    Ok(())
}

fn print_welcome() {
    println!("{}", "🚀 Price-RS Extension Generator".bold().cyan());
    println!("{}", "Generate extension projects with standard four-layer architecture".italic());
    println!();
}