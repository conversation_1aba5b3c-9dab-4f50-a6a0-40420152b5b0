/// 计价核心模块
///
/// 本模块是计价系统的核心，提供计价编辑器、规则引擎以及相关类型定义
/// 它整合了各个功能组件，为上层应用提供统一的接口
///
/// # 主要组件
/// * `editor` - 计价编辑器，用于管理和操作价格数据
/// * `rules` - 规则引擎，用于执行计价规则和决策逻辑
/// * `types` - 类型定义，为系统提供基础数据结构
/// * `error` - 错误处理，统一管理系统中可能出现的异常情况
pub mod editor;

/// 错误处理模块
///
/// 定义了系统中可能出现的各种错误类型和处理机制
/// 提供统一的错误类型和处理方式，简化错误传播链
pub mod error;

/// 事件处理模块
///
/// 负责系统内部事件的发布和订阅机制
/// 支持组件间的解耦通信和异步操作协调
pub mod event_handler;


/// 类型定义模块
///
/// 提供系统中使用的各种数据类型和结构体定义
/// 包括配置选项、资源引用和类型别名等
pub mod types;

pub mod helpers;


