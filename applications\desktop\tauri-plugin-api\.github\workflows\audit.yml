name: Audit

on:
  schedule:
    - cron: '0 0 * * *'
  push:
    branches:
      - main
    paths:
      - ".github/workflows/audit.yml"
      - "**/Cargo.lock"
      - "**/Cargo.toml"
  pull_request:
    branches:
      - main
    paths:
      - ".github/workflows/audit.yml"
      - "**/Cargo.lock"
      - "**/Cargo.toml"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  audit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: rustsec/audit-check@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
