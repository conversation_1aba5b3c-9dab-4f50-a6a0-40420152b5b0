use mf_core::{extension::Extension, types::Extensions};

use crate::nodes::common_fields::init_fbfx_csxm_fields;

pub mod command;
pub mod nodes;
pub mod plugins;

// 重新导出主要的结构体和工厂
pub use nodes::node_definitions::{
    FbNode, QdNode, DeNode, DeRcjNode, FbfxNode, CsxmNode, FbfxCsxmFactory
};

/// 构建分部分项扩展
pub fn build_extension() -> Vec<Extensions> {
    let mut extension = Extension::new();

    //构建插件扩展
    let mut extensions = vec![Extensions::E(extension)];
    //构建公共字段扩展
    let common_fields = init_fbfx_csxm_fields();
    for field in common_fields {
        extensions.push(Extensions::N(field));
    }
    extensions
}
