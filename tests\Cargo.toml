[package]
name = "tests"
version = "0.1.0"
edition = "2024"
description="综合测试"
[dependencies]
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true } 

moduforge-model = { workspace = true }
moduforge-state = { workspace = true }
moduforge-transform = { workspace = true }
moduforge-core = { workspace = true }
moduforge-macros = { workspace = true }
moduforge-macros-derive = { workspace = true }
salsa = { version = "0.19.0"}
async-trait = { workspace = true }

price-budget-core = { workspace = true }
price-rules = { workspace = true }
price-storage = { workspace = true }
price-search = { workspace = true }
# 引入pagkages 下面的所有库
extension-base-schema = { workspace = true }
extension-djgc = { workspace = true }
extension-bzhs = { workspace = true }
extension-cost-collect = { workspace = true }
extension-fbfx-csxm = { workspace = true }
extension-rcj = { workspace = true }
extension-rcj-collect = { workspace = true }

