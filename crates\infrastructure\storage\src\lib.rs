use std::{
    env::current_dir,
    path::{Path, PathBuf},
    sync::Arc,
};
use uuid::Uuid;

use async_trait::async_trait;
use mf_core::{
    error_utils::storage_error,
    event::{Event, EventHandler},
    ForgeResult,
};
use mf_state::{state::State, Configuration, Transaction};
use std::fmt::Debug;

pub mod sqlite;
pub mod step_factory;
pub mod crypto;

pub type StorageResult<T> = ForgeResult<T>;

#[derive(Debug, Clone)]
pub struct StorageOptions {
    /// 存储根目录
    pub root_dir: PathBuf,
    /// 实例ID
    pub id: String,
    /// 加密密钥（可选）
    pub encryption_key: Option<Vec<u8>>,
}

impl Default for StorageOptions {
    fn default() -> Self {
        let path = current_dir().unwrap().join("./data");
        Self { root_dir: path, id: Uuid::new_v4().to_string(), encryption_key: None }
    }
}

impl StorageOptions {
    pub fn new(root_dir: PathBuf, id: String) -> Self {
        Self {
            root_dir,
            id,
            encryption_key: None,
        }
    }

    pub fn with_encryption(mut self, key: Vec<u8>) -> Self {
        self.encryption_key = Some(key);
        self
    }

    /// 获取存储实例的根目录
    pub fn get_root_dir(&self) -> PathBuf {
        self.root_dir.join(&self.id)
    }

    /// 获取数据库文件路径
    pub fn get_db_path(&self) -> PathBuf {
        self.get_root_dir().join("db.sqlite")
    }

    /// 获取资源目录路径
    pub fn get_resource_dir(&self) -> PathBuf {
        self.get_root_dir().join("resources")
    }
}

#[async_trait]
pub trait StorageTrait: Send + Sync + Debug {
    async fn save_state(
        &self,
        state: &Arc<State>,
    ) -> StorageResult<()>;

    async fn save_transaction(
        &self,
        old_id: u64,
        transaction: &Arc<Vec<Transaction>>,
        state: &Arc<State>,
    ) -> StorageResult<()>;
    async fn load_state(
        &self,
        configuration: &Configuration,
    ) -> StorageResult<Arc<State>>;

    async fn export(
        &self,
        path: Option<String>,
    ) -> StorageResult<()>;

    async fn import(
        &self,
        zip_path: &Path,
    ) -> StorageResult<()>;
    async fn save_resource(
        &self,
        path: &Path,
    ) -> StorageResult<String>;

    async fn get_resource(
        &self,
        id: String,
    ) -> StorageResult<String>;
}

#[derive(Debug, Clone)]
pub struct StorageHandler {
    storage: Arc<dyn StorageTrait>,
}
impl StorageHandler {
    pub fn new(storage: Arc<dyn StorageTrait>) -> Self {
        Self { storage }
    }
}
#[async_trait]
impl EventHandler<Event> for StorageHandler {
    async fn handle(
        &self,
        event: &Event,
    ) -> ForgeResult<()> {
        match event {
            Event::Create(state) => {
                self.storage.save_state(state).await.map_err(|e| storage_error(e.to_string()))?;
            },
            Event::TrApply(old_id, transaction, state) => {
                self.storage.save_transaction(*old_id, transaction, state).await.map_err(|e| storage_error(e.to_string()))?;
            },
            Event::Destroy => {
                self.storage.export(None).await.map_err(|e| storage_error(e.to_string()))?;
            },
            _ => {},
        }
        Ok(())
    }
}
