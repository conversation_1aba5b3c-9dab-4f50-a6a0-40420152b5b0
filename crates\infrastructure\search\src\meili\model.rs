use serde::{Deserialize, Serialize};

/// 搜索配置选项
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SearchConfig {
    /// Meilisearch 主机地址
    pub host: String,
    /// API 密钥（可选）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub api_key: Option<String>,
}

impl Default for SearchConfig {
    fn default() -> Self {
        Self { host: "http://127.0.0.1:7700".to_string(), api_key: Some("xjlidong123".to_string()) }
    }
}

/// 搜索查询选项
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SearchOptions {
    /// 搜索关键词
    pub query: String,
    /// 要搜索的字段
    #[serde(skip_serializing_if = "Option::is_none")]
    pub search_attributes: Option<Vec<String>>,
    /// 要返回的字段
    #[serde(skip_serializing_if = "Option::is_none")]
    pub return_attributes: Option<Vec<String>>,
    /// 分页偏移量
    #[serde(skip_serializing_if = "Option::is_none")]
    pub offset: Option<usize>,
    /// 分页大小
    #[serde(skip_serializing_if = "Option::is_none")]
    pub limit: Option<usize>,
    /// 过滤条件
    #[serde(skip_serializing_if = "Option::is_none")]
    pub filter: Option<String>,
    /// 排序条件
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sort: Option<Vec<String>>,
}

impl Default for SearchOptions {
    fn default() -> Self {
        Self { query: String::new(), search_attributes: None, return_attributes: None, offset: Some(0), limit: Some(20), filter: None, sort: None }
    }
}

/// 索引设置
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct IndexSettings {
    /// 可搜索字段
    #[serde(skip_serializing_if = "Option::is_none")]
    pub searchable_attributes: Option<Vec<String>>,
    /// 可过滤字段
    #[serde(skip_serializing_if = "Option::is_none")]
    pub filterable_attributes: Option<Vec<String>>,
    /// 可排序字段
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sortable_attributes: Option<Vec<String>>,
    /// 可展示字段
    #[serde(skip_serializing_if = "Option::is_none")]
    pub displayed_attributes: Option<Vec<String>>,
    /// 主键
    #[serde(skip_serializing_if = "Option::is_none")]
    pub primary_key: Option<String>,
}

/// 搜索结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult<T> {
    /// 匹配到的文档
    pub hits: Vec<T>,
    /// 匹配总数
    pub total_hits: usize,
    /// 分页偏移量
    pub offset: usize,
    /// 分页大小
    pub limit: usize,
    /// 处理时间(毫秒)
    pub processing_time_ms: usize,
    /// 查询
    pub query: String,
}
