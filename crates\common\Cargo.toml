[package]
name = "price-common"
version = { workspace = true }
edition = { workspace = true }
authors = { workspace = true }
description = "Price system common types and utilities"


[dependencies]
# 基础依赖
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
chrono = { workspace = true, features = ["serde"] }

# 日志依赖
tracing = "0.1"

# ModuForge核心依赖
moduforge-model = { workspace = true }
moduforge-state = { workspace = true }
