pub mod model;
use std::sync::Arc;

use model::SqliteConfig;
use rbatis::RBatis;
use rbdc_sqlite::Driver;
use state::InitCell;

pub static SQLITE_SEARCH_CLIENT: InitCell<Arc<RBatis>> = InitCell::new();
pub async fn init_sqlite_search_client(config: Option<SqliteConfig>) {
    let config = config.unwrap_or_default();
    let rb = RBatis::new();
    rb.link(Driver {}, &format!("sqlite://{}", config.path)).await.unwrap();
    SQLITE_SEARCH_CLIENT.set(Arc::new(rb));
}
