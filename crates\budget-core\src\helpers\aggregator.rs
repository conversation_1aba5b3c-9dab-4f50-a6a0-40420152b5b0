use async_trait::async_trait;
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::{
        Arc,
        atomic::{AtomicUsize, Ordering},
    },
    time::Duration,
};
use thiserror::Error;
use tokio::runtime::Runtime;

use crate::helpers::work_stealing::{WorkStealingExecutor, WorkStealingExecutorConfig};
use mf_model::{node::Node, node_pool::NodePool, types::NodeId};
use mf_state::State;

/// 汇总器错误类型
///
/// # 错误分类
/// 该枚举定义了在节点汇总过程中可能出现的各类错误，
/// 主要包括IO错误、序列化错误和节点池错误。
///
/// # 错误处理策略
/// - IO错误: 通常由文件操作引起，如创建文件、读写数据等
/// - 序列化错误: 在数据序列化或反序列化过程中发生的错误
/// - 池错误: 来自底层节点池的错误
///
/// 使用thiserror提供标准错误信息格式和转换机制
#[derive(Error, Debug, Clone)]
pub enum AggregatorError {
    /// IO错误
    ///
    /// 文件系统操作失败，如创建文件、读写文件等
    #[error("IO error: {0}")]
    Io(String),

    /// 序列化错误
    ///
    /// 数据编码/解码失败，通常是格式不兼容或数据损坏导致
    #[error("Serialization error: {0}")]
    Serialization(String),

    /// 节点池错误
    ///
    /// 来自底层节点池的错误，如节点不存在、节点类型错误等
    #[error("Pool error: {0}")]
    Pool(String),

    /// 压缩错误
    ///
    /// 数据压缩或解压缩过程中发生的错误
    #[error("Compression error: {0}")]
    Compression(String),
}

/// 汇总选项
///
/// # 功能说明
/// 配置节点汇总工具的各项参数，控制汇总过程的行为、性能和资源使用。
///
/// # 配置项用途
/// - `batch_size`: 控制批处理大小，影响内存使用和处理效率
/// - `cleanup_temp_files`: 完成后是否清理临时文件
/// - `memory_limit`: 内存使用上限，超过后将数据写入磁盘
/// - `show_progress`: 是否显示进度信息
/// - `num_threads`: 并行处理线程数
/// - `enable_disk_cache`: 是否启用磁盘缓存功能
/// - `compression_level`: 压缩级别，影响数据存储效率
/// - `use_memory_mapping`: 是否使用内存映射加速文件访问
#[derive(Clone)]
pub struct AggregateOptions {
    /// 批处理大小
    ///
    /// 控制每批处理的节点数量，较大的值可以提高性能，但会增加内存使用
    pub batch_size: usize,

    /// 是否清理临时文件
    ///
    /// 如果为true，在处理完成后会删除临时文件；否则保留这些文件
    pub cleanup_temp_files: bool,

    /// 内存使用限制（字节）
    ///
    /// 当内存使用超过此限制时，会将部分数据写入磁盘
    /// 为None时不限制内存使用
    pub memory_limit: Option<usize>,

    /// 是否显示进度条
    ///
    /// 控制是否在处理过程中输出进度信息
    pub show_progress: bool,

    /// 并行处理线程数
    ///
    /// 指定用于并行处理的线程数，为None时使用系统默认值
    pub num_threads: Option<usize>,

    /// 是否启用磁盘缓存
    ///
    /// 控制是否使用磁盘缓存来减轻内存压力
    pub enable_disk_cache: bool,

    /// 压缩级别
    ///
    /// zstd压缩级别(1-22)，越大压缩率越高但速度越慢
    /// 默认值为3，提供良好的压缩率和速度平衡
    pub compression_level: i32,

    /// 是否使用内存映射
    ///
    /// 是否使用内存映射文件来进行磁盘缓存访问
    /// 对于大型文件能提供更好的性能
    pub use_memory_mapping: bool,
}

impl Default for AggregateOptions {
    /// 创建默认汇总选项
    ///
    /// # 默认配置
    /// - batch_size: 10000 - 适合大多数场景的批处理大小
    /// - cleanup_temp_files: true - 自动清理临时文件
    /// - memory_limit: None - 不限制内存使用
    /// - show_progress: true - 显示进度信息
    /// - num_threads: None - 使用系统默认线程数
    /// - enable_disk_cache: true - 启用磁盘缓存
    /// - compression_level: 3 - 平衡压缩率和速度
    /// - use_memory_mapping: true - 使用内存映射加速读写
    fn default() -> Self {
        Self {
            batch_size: 10000,
            cleanup_temp_files: true,
            memory_limit: None,
            show_progress: true,
            num_threads: None,
            enable_disk_cache: true,
            compression_level: 3,
            use_memory_mapping: true,
        }
    }
}

/// 节点聚合器特征
///
/// 定义节点聚合的核心功能接口
#[async_trait]
pub trait NodeAggregatorTrait<T>: Send + Sync + Clone + 'static {
    /// 聚合函数：根据节点和子节点结果计算当前节点的聚合值
    async fn aggregate(
        &self,
        state: &Arc<State>,
        node: &Node,
        child_results: &[T],
    ) -> T;
}

/// 层级策略特征
///
/// 定义如何将节点分组到不同的层级
pub trait LevelStrategy: Send + Sync {
    /// 获取节点的层级
    fn get_level(
        &self,
        node_id: &NodeId,
        pool: &Arc<NodePool>,
    ) -> usize;
}

/// 默认层级策略：从叶子节点开始，每向上一层加1
pub struct DefaultLevelStrategy;

impl LevelStrategy for DefaultLevelStrategy {
    fn get_level(
        &self,
        node_id: &NodeId,
        pool: &Arc<NodePool>,
    ) -> usize {
        let mut level = 0;
        let mut current_id = node_id.clone();
        while let Some(parent_id) = pool.parent_id(&current_id) {
            level += 1;
            current_id = parent_id.clone();
        }
        level
    }
}

pub struct ConcurrentCounter {
    count: AtomicUsize,
    processed: AtomicUsize,
}

impl Default for ConcurrentCounter {
    fn default() -> Self {
        Self::new()
    }
}

impl ConcurrentCounter {
    pub fn new() -> Self {
        Self { count: AtomicUsize::new(0), processed: AtomicUsize::new(0) }
    }

    pub fn increment(&self) {
        self.count.fetch_add(1, Ordering::SeqCst);
    }

    pub fn get_count(&self) -> usize {
        self.count.load(Ordering::SeqCst)
    }

    pub fn add_processed(
        &self,
        value: usize,
    ) {
        self.processed.fetch_add(value, Ordering::SeqCst);
    }

    pub fn get_processed(&self) -> usize {
        self.processed.load(Ordering::SeqCst)
    }
}

pub struct ConcurrentCache<T> {
    cache: DashMap<NodeId, T>,
}

impl<T> ConcurrentCache<T>
where
    T: Clone + Send + Sync + 'static,
{
    pub fn new() -> Self {
        Self { cache: DashMap::new() }
    }

    pub fn insert(
        &self,
        key: NodeId,
        value: T,
    ) {
        self.cache.insert(key, value);
    }

    pub fn get(
        &self,
        key: &NodeId,
    ) -> Option<T> {
        self.cache.get(key).map(|v| v.value().clone())
    }

    pub fn len(&self) -> usize {
        self.cache.len()
    }

    pub fn iter(&self) -> impl Iterator<Item = (NodeId, T)> + '_ {
        self.cache.iter().map(|e| (e.key().clone(), e.value().clone()))
    }

    pub fn remove(
        &self,
        key: &NodeId,
    ) -> Option<T> {
        self.cache.remove(key).map(|(_, v)| v)
    }
}

impl<T> Clone for ConcurrentCache<T>
where
    T: Clone + Send + Sync + 'static,
{
    fn clone(&self) -> Self {
        Self { cache: self.cache.clone() }
    }
}
// 首先定义一个类型别名来表示函数类型
type NodeProcessor<T> = Box<dyn Fn(NodeId) -> (NodeId, T) + Send + Sync>;

// 然后使用这个类型别名来简化 WorkStealingExecutor 的类型
type NodeExecutor<T> = WorkStealingExecutor<NodeId, (NodeId, T), NodeProcessor<T>>;
/// 节点汇总工具
///
/// # 功能概述
/// 该工具用于从节点池中收集节点数据，并按特定规则进行汇总计算。
/// 它支持从多个起始节点向上汇总数据，可以指定终止节点。
///
/// # 核心功能
/// - 多级缓存: 内存缓存和磁盘缓存，减少重复计算
/// - 并行处理: 利用多线程处理大量节点
/// - 内存控制: 动态管理内存使用，防止内存溢出
/// - 批量处理: 分批处理节点，控制资源使用
pub struct NodeAggregator<T, F, L = DefaultLevelStrategy>
where
    T: Clone + Send + Sync + 'static + Serialize + for<'de> Deserialize<'de>,
    F: NodeAggregatorTrait<T>,
    L: LevelStrategy,
{
    state: Arc<State>,          // 节点池引用
    options: AggregateOptions,  // 汇总选项
    cache: ConcurrentCache<T>,  // 并发内存缓存
    counter: ConcurrentCounter, // 并发计数器
    #[allow(dead_code)]
    aggregator: F,              // 汇总函数
    #[allow(dead_code)]
    initial: T,                 // 初始值
    executor: NodeExecutor<T>,
    level_strategy: L,
}

impl<T, F, L> NodeAggregator<T, F, L>
where
    T: Clone + Send + Sync + 'static + Serialize + for<'de> Deserialize<'de>,
    F: NodeAggregatorTrait<T>,
    L: LevelStrategy,
{
    /// 创建新的节点汇总工具
    ///
    /// # 参数
    /// - `state`: 状态对象，包含文档和节点池
    /// - `options`: 汇总选项
    /// - `aggregator`: 汇总函数，接收状态、当前节点和子节点结果列表，返回汇总结果
    /// - `initial`: 初始值，用于没有子节点的叶子节点
    /// - `level_strategy`: 层级策略
    ///
    /// # 返回
    /// 返回一个新的节点汇总工具实例
    pub fn new(
        state: Arc<State>,
        options: AggregateOptions,
        aggregator: F,
        initial: T,
        level_strategy: L,
    ) -> Self {
        let config = WorkStealingExecutorConfig {
            num_threads: options.num_threads.unwrap_or_else(|| std::thread::available_parallelism().map(|p| p.get()).unwrap_or(num_cpus::get())),
            task_timeout: Duration::from_secs(30),
            enable_work_stealing: true,
        };

        let state_clone = Arc::clone(&state);
        let aggregator_clone = aggregator.clone();
        let initial_clone = initial.clone();
        let cache = ConcurrentCache::new();

        let processor = Box::new(move |id: NodeId| {
            let state = Arc::clone(&state_clone);
            let aggregator = aggregator_clone.clone();
            let initial = initial_clone.clone();
            let cache = cache.clone();

            let rt = Runtime::new().expect("创建tokio runtime失败");
            rt.block_on(async move {
                // 检查缓存
                if let Some(cached_result) = cache.get(&id) {
                    return (id, cached_result);
                }

                if let Some(node) = state.doc().get_node(&id) {
                    let child_results = if let Some(children) = state.doc().children(&id) {
                        children
                            .iter()
                            .map(|child_id| {
                                // 检查子节点的缓存
                                cache.get(child_id).unwrap_or_else(|| initial.clone())
                            })
                            .collect()
                    } else {
                        Vec::new()
                    };
                    // 聚合子节点结果
                    let result = aggregator.aggregate(&state, &node, &child_results).await;
                    // 更新缓存
                    cache.insert(id.clone(), result.clone());
                    (id, result)
                } else {
                    (id, initial)
                }
            })
        }) as Box<dyn Fn(NodeId) -> (NodeId, T) + Send + Sync>;

        let executor = WorkStealingExecutor::new(config, processor);

        Self { state, options, cache: ConcurrentCache::new(), counter: ConcurrentCounter::new(), aggregator, initial, executor, level_strategy }
    }
    /// 从多个起始节点向上汇总数据
    ///
    /// # 执行流程
    /// 1. 节点收集与缓存检查:
    ///    - 从每个起始节点开始向上遍历
    ///    - 对每个节点检查内存缓存
    ///    - 如果节点在缓存中，直接使用缓存结果
    ///    - 如果遇到停止节点，终止该路径的遍历
    ///
    /// 2. 层级分组:
    ///    - 使用层级策略计算每个节点的层级
    ///    - 将节点按层级分组存储
    ///    - 确保节点按层级顺序处理
    ///
    /// 3. 并行处理:
    ///    - 按层级顺序处理节点
    ///    - 每个层级内的节点按批次处理
    ///    - 使用工作窃取执行器进行并行计算
    ///    - 每个节点的处理包括:
    ///      * 获取节点信息
    ///      * 收集子节点结果
    ///      * 执行聚合计算
    ///      * 更新缓存
    ///
    /// # 性能优化
    /// - 缓存优先: 优先使用缓存结果，避免重复计算
    /// - 层级处理: 确保依赖关系正确，避免循环依赖
    /// - 批量并行: 通过批处理提高并行效率
    /// - 工作窃取: 动态平衡线程负载
    ///
    /// # 参数
    /// - `start_nodes`: 起始节点列表，从这些节点开始向上汇总
    /// - `stop_at`: 可选的停止节点，到达该节点时停止汇总
    ///
    /// # 返回
    /// 返回一个HashMap，包含所有处理过的节点ID及其对应的汇总结果
    ///
    /// # 错误处理
    /// 返回AggregatorError类型，可能包含IO错误、序列化错误或节点池错误
    pub fn aggregate_up(
        &self,
        start_nodes: &[NodeId],
        stop_at: Option<&NodeId>,
    ) -> Result<HashMap<NodeId, T>, AggregatorError> {
        let pool = self.state.doc();
        let mut results = HashMap::new();
        let mut level_groups: HashMap<usize, Vec<NodeId>> = HashMap::new();

        // 第一阶段：收集需要处理的节点
        for start_id in start_nodes {
            let mut current_id = start_id;
            while pool.get_node(current_id).is_some() {
                // 检查缓存
                if let Some(cached_result) = self.cache.get(current_id) {
                    results.insert(current_id.clone(), cached_result);
                    if stop_at == Some(current_id) {
                        break;
                    }
                    if let Some(parent_id) = pool.parent_id(current_id) {
                        current_id = parent_id;
                        continue;
                    }
                    break;
                }

                // 记录节点层级
                let level = self.level_strategy.get_level(current_id, &pool);
                level_groups.entry(level).or_default().push(current_id.clone());

                if stop_at == Some(current_id) {
                    break;
                }
                if let Some(parent_id) = pool.parent_id(current_id) {
                    current_id = parent_id;
                } else {
                    break;
                }
            }
        }

        self.counter.count.store(level_groups.values().map(|v| v.len()).sum(), Ordering::SeqCst);

        // 第二阶段：按层级处理节点
        let mut levels: Vec<_> = level_groups.keys().collect();
        levels.sort();

        for level in levels {
            if let Some(nodes) = level_groups.get(level) {
                let batch_size = self.options.batch_size;
                for chunk in nodes.chunks(batch_size) {
                    let result_rxs = self.executor.submit_batch(chunk.to_vec());

                    for rx in result_rxs {
                        if let Ok((id, result)) = rx.recv() {
                            results.insert(id.clone(), result.clone());
                            self.counter.add_processed(1);
                        }
                    }
                }
            }
        }

        Ok(results)
    }
}

impl<T, F, L> Drop for NodeAggregator<T, F, L>
where
    T: Clone + Send + Sync + 'static + Serialize + for<'de> Deserialize<'de>,
    F: NodeAggregatorTrait<T>,
    L: LevelStrategy,
{
    fn drop(&mut self) {
        self.executor.shutdown();
    }
}

// 实现From特征用于转换错误类型
impl From<std::io::Error> for AggregatorError {
    fn from(err: std::io::Error) -> Self {
        AggregatorError::Io(err.to_string())
    }
}

impl From<anyhow::Error> for AggregatorError {
    fn from(err: anyhow::Error) -> Self {
        AggregatorError::Pool(err.to_string())
    }
}

// 为默认层级策略提供便捷构造函数
impl<T, F> NodeAggregator<T, F, DefaultLevelStrategy>
where
    T: Clone + Send + Sync + 'static + Serialize + for<'de> Deserialize<'de>,
    F: NodeAggregatorTrait<T>,
{
    pub fn with_default_level_strategy(
        state: Arc<State>,
        options: AggregateOptions,
        aggregator: F,
        initial: T,
    ) -> Self {
        Self::new(state, options, aggregator, initial, DefaultLevelStrategy)
    }
}
