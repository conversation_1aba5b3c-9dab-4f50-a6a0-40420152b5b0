/*
CREATE TABLE "base_rcj_class_level" (
  "sequence_nbr" integer(20) NOT NULL,
  "level1_code" integer(10),
  "level2_code" integer(10),
  "level3_code" integer(10),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" integer(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64)
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseRcjClassLevel {
    pub sequence_nbr: String,
    pub level1_code: Option<i32>,
    pub level2_code: Option<i32>,
    pub level3_code: Option<i32>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseRcjClassLevel {}, "base_rcj_class_level");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec!["level1_code".to_string(), "level2_code".to_string(), "level3_code".to_string()]),
        filterable_attributes: Some(vec!["level1_code".to_string(), "level2_code".to_string(), "level3_code".to_string()]),
        sortable_attributes: Some(vec!["level1_code".to_string(), "level2_code".to_string(), "level3_code".to_string()]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseRcjClassLevel>("base_rcj_class_level", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseRcjClassLevel>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_rcj_class_level").await?;
    let base_list_index = IndexClient::<BaseRcjClassLevel> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
