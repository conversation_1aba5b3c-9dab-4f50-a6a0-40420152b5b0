use std::sync::Arc;

use async_trait::async_trait;
use mf_state::{
    error::StateResult,
    plugin::{Plugin, PluginMetadata, PluginSpec, PluginTrait},
    state::State,
    transaction::Transaction,
};

#[derive(Debug)]
pub struct {{ plugin_struct_name }};

#[async_trait]
impl PluginTrait for {{ plugin_struct_name }} {
    fn metadata(&self) -> PluginMetadata {
        PluginMetadata {
            name: "{{ names.kebab }}".to_string(),
            version: "1.0.0".to_string(),
            description: "{{ description }}".to_string(),
            author: "{{ author }}".to_string(),
            dependencies: vec![],
            conflicts: vec![],
            state_fields: vec![],
            tags: vec!["{{ names.kebab }}".to_string()],
        }
    }
    
    async fn append_transaction(
        &self,
        trs: &[Transaction],
        old_state: &State,
        new_state: &State,
    ) -> StateResult<Option<Transaction>> {
        // TODO: 实现事务追加逻辑
        // 这里可以添加额外的业务逻辑，比如：
        // 1. 验证事务的合法性
        // 2. 计算衍生数据
        // 3. 触发其他系统事件
        
        Ok(None)
    }
    
    async fn filter_transaction(
        &self,
        tr: &Transaction,
        state: &State,
    ) -> bool {
        // TODO: 实现事务过滤逻辑
        // 这里决定该插件是否需要处理这个事务
        // 例如，检查事务是否包含相关的节点类型
        
        // 检查事务中是否包含{{ names.kebab }}相关的节点
        tr.get_commands().iter().any(|cmd| {
            // 这里可以根据命令类型或节点类型进行过滤
            true // 暂时返回true，处理所有事务
        })
    }
    
    async fn generate_transaction(
        &self,
        state: &State,
        transaction: &Transaction,
    ) -> StateResult<Vec<mf_state::transaction::Command>> {
        // TODO: 实现命令生成逻辑
        // 根据当前状态和输入事务生成新的命令
        
        let mut commands = Vec::new();
        
        // 例如：根据输入数据生成计算命令
        // commands.push(create_calculation_command()?);
        
        Ok(commands)
    }
}

impl {{ plugin_struct_name }} {
    /// 创建新的插件实例
    pub fn new() -> Self {
        Self
    }
    
    /// 获取插件规格
    pub fn spec() -> PluginSpec<Self> {
        PluginSpec::new(Self::new())
    }
    
    /// 验证输入数据
    pub async fn validate_input(&self, data: &serde_json::Value) -> StateResult<()> {
        // TODO: 实现输入验证逻辑
        // 验证输入数据的格式和内容是否符合要求
        
        Ok(())
    }
    
    /// 执行业务计算
    pub async fn calculate(&self, input: &serde_json::Value) -> StateResult<serde_json::Value> {
        // TODO: 实现具体的业务计算逻辑
        // 例如：{{ description }}相关的计算
        
        let result = serde_json::json!({
            "status": "success",
            "message": "{{ description }}计算完成",
            "data": input
        });
        
        Ok(result)
    }
}

impl Default for {{ plugin_struct_name }} {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[tokio::test]
    async fn test_plugin_metadata() {
        let plugin = {{ plugin_struct_name }}::new();
        let metadata = plugin.metadata();
        
        assert_eq!(metadata.name, "{{ names.kebab }}");
        assert_eq!(metadata.description, "{{ description }}");
        assert_eq!(metadata.author, "{{ author }}");
        assert_eq!(metadata.version, "1.0.0");
    }
    
    #[tokio::test]
    async fn test_plugin_validation() {
        let plugin = {{ plugin_struct_name }}::new();
        let test_data = json!({
            "code": "001",
            "name": "测试项目",
            "quantity": 1.0,
            "unit_price": 100.0
        });
        
        let result = plugin.validate_input(&test_data).await;
        assert!(result.is_ok());
    }
    
    #[tokio::test]
    async fn test_plugin_calculation() {
        let plugin = {{ plugin_struct_name }}::new();
        let input = json!({
            "code": "001",
            "name": "测试项目",
            "quantity": 2.0,
            "unit_price": 100.0
        });
        
        let result = plugin.calculate(&input).await;
        assert!(result.is_ok());
        
        let output = result.unwrap();
        assert_eq!(output["status"], "success");
    }
}