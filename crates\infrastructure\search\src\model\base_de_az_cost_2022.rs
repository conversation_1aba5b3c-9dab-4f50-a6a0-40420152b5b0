/*
CREATE TABLE "base_de_az_cost_2022" (
  "sequence_nbr" text NOT NULL,
  "de_code" text(255) NOT NULL,
  "cg_name" text(255) NOT NULL,
  "major" text(255),
  "storey" text(19),
  "meter" text(255),
  "is_default" integer(1),
  "remark" text(1024),
  "status" integer(4),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64)
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeAzCost2022 {
    pub sequence_nbr: String,
    pub de_code: String,
    pub cg_name: String,
    pub major: Option<String>,
    pub storey: Option<String>,
    pub meter: Option<String>,
    pub is_default: Option<i32>,
    pub remark: Option<String>,
    pub status: Option<i32>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}

//crud!(BaseDeAzCost2022 {}, "base_de_az_cost_2022");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "de_code".to_string(),
            "cg_name".to_string(),
            "major".to_string(),
            "storey".to_string(),
            "meter".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "de_code".to_string(),
            "cg_name".to_string(),
            "major".to_string(),
            "storey".to_string(),
            "meter".to_string(),
            "is_default".to_string(),
            "remark".to_string(),
            "status".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "de_code".to_string(),
            "cg_name".to_string(),
            "major".to_string(),
            "storey".to_string(),
            "meter".to_string(),
            "is_default".to_string(),
            "remark".to_string(),
            "status".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeAzCost2022>("base_de_az_cost_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeAzCost2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_az_cost_2022").await?;
    let base_de_az_cost_2022_index = IndexClient::<BaseDeAzCost2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_az_cost_2022_index);
    Ok(search_service)
}
