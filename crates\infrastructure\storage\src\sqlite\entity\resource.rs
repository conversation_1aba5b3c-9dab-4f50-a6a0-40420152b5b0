use rbatis::{crud, executor::Executor, impl_select, table_sync::SqliteTableMapper, RB<PERSON><PERSON>};
use serde::{Deserialize, Serialize};

/* #[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "resources")] */
#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct Resource {
    pub id: i64,
    pub name: String,
    pub path: String,
    pub mime_type: String,
    pub size: i64,
    pub hash: String,
}

crud!(Resource {}, "resources");
impl_select!(Resource{select_by_id(id:i64) -> Option => "`where id = #{id} limit 1`"});
pub async fn do_sync_table_resource(conn: &dyn Executor) {
    let table = Resource { id: 0, name: "".to_string(), path: "".to_string(), mime_type: "".to_string(), size: 0, hash: "".to_string() };
    let _ = RBatis::sync(conn, &SqliteTableMapper {}, &table, "resources").await;
}
