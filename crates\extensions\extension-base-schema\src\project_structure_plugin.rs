use mf_macro::{mf_plugin, mf_meta};
use mf_state::{State, Transaction};

mf_plugin!(
    project_structure,
    metadata = mf_meta!(
        version = "1.0.0",
        description = "项目结构插件",
        author = "moduforge",
        tags = ["project_structure"]
    ),
    append_transaction = async |_trs: &[Transaction], _old_state: &State, _new_state: &State| {
        Ok(None)
    },
    docs = "项目结构插件"
);