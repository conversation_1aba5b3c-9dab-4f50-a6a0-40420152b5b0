use std::sync::Arc;

use mf_core::types::RuntimeOptions;
use price_rules::loader::RulesLoaderExt;
use price_storage::StorageTrait;

use price_rules::rules_engine::RulesEngine;

/// 规则引擎的Arc类型别名，方便在代码中使用
///
/// 封装了规则引擎的共享引用，简化了类型声明，
/// 特别是在需要在多个组件间共享规则引擎实例时。
///
/// 使用 Arc 包装确保规则引擎可以安全地在多线程环境中共享，
/// 同时保证引用计数正确管理生命周期。
pub type ArcRulesEngine = Arc<RulesEngine>;

/// 计价编辑器配置选项
///
/// 该结构体封装了创建和配置计价编辑器所需的各种选项
///
/// # 主要组件
/// - 基础编辑器选项：控制编辑器的基本行为和特性
/// - 存储接口：负责数据的持久化和检索
/// - 规则加载器：负责规则定义的加载和解析
///
/// # 设计意图
/// PriceEditorOptions 采用组合模式，将多个核心组件通过依赖注入的方式
/// 组合在一起，既提高了灵活性，也方便了测试和替换各个组件。
pub struct PriceEditorOptions {
    /// 基础编辑器配置选项，控制编辑器的基本行为
    ///
    /// 包含编辑器的通用配置，如撤销/重做栈大小、
    /// 扩展设置、并发模型等基础特性配置
    pub editor_options: RuntimeOptions,

    pub storage: Arc<dyn StorageTrait>,

    /// 规则加载器的Arc引用，负责加载和管理计价规则
    ///
    /// 通过 RulesLoaderExt trait 抽象，支持从多种来源加载规则，
    /// 如文件、数据库或远程服务。
    /// 使用 Arc 包装使其可安全地在多个组件间共享。
    pub loader: Arc<dyn RulesLoaderExt>,
}

impl PriceEditorOptions {
    /// 创建新的计价编辑器配置选项实例
    ///
    /// # 参数
    /// * `options` - 基础编辑器配置选项，控制编辑器的基本行为
    /// * `storage` - 存储接口实现，负责数据持久化
    /// * `loader` - 规则加载器实现，负责规则管理
    ///
    /// # 返回
    /// * `Self` - 配置选项实例，可用于创建计价编辑器
    ///
    /// # 使用示例
    /// ```
    /// let editor_options = RuntimeOptions::default();
    /// let storage = Arc::new(MyStorage::new());
    /// let loader = Arc::new(MyRulesLoader::new());
    ///
    /// let options = PriceEditorOptions::new(editor_options, storage, loader);
    /// let editor = PriceEditor::create(options).await?;
    /// ```
    pub fn new(
        options: RuntimeOptions,
        storage: Arc<dyn StorageTrait>,
        loader: Arc<dyn RulesLoaderExt>,
    ) -> Self {
        Self { editor_options: options, storage, loader }
    }
}
