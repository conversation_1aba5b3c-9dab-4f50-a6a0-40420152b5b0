/// 计价系统通用错误类型
use thiserror::Error;

// ================================
// 通用策略错误
// ================================

/// 策略相关通用错误
#[derive(Error, Debug)]
pub enum CommonStrategyError {
    #[error("不支持的地区: {0}")]
    UnsupportedRegion(String),
    
    #[error("配置加载失败: {0}")]
    ConfigLoadFailed(String),
    
    #[error("策略验证失败: {0}")]
    ValidationFailed(String),
    
    #[error("内部错误: {0}")]
    InternalError(String),
}

// ================================
// 通用配置错误  
// ================================

/// 配置相关通用错误
#[derive(Error, Debug)]
pub enum CommonConfigError {
    #[error("配置文件未找到: {0}")]
    ConfigNotFound(String),
    
    #[error("配置解析失败: {0}")]
    ParseError(String),
    
    #[error("配置验证失败: {0}")]
    ValidationFailed(String),
    
    #[error("必需配置缺失: {0}")]
    MissingRequired(String),
    
    #[error("配置值无效: {key} = {value}")]
    InvalidValue { key: String, value: String },
    
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("序列化错误: {0}")]
    SerdeError(String),
}

// ================================
// 通用计算错误
// ================================

/// 计算相关通用错误  
#[derive(Error, Debug)]
pub enum CommonCalculationError {
    #[error("缺少必需字段: {0}")]
    MissingField(String),
    
    #[error("计算参数无效: {field} = {value}")]
    InvalidParameter { field: String, value: String },
    
    #[error("计算溢出: {0}")]
    CalculationOverflow(String),
    
    #[error("验证失败: {0}")]
    ValidationFailed(String),
}