use std::collections::HashMap;

/// 模板管理器
/// 负责管理和提供所有内置模板
pub struct TemplateManager {
    templates: HashMap<String, String>,
}

impl TemplateManager {
    /// 创建新的模板管理器
    pub fn new() -> Self {
        let mut templates = HashMap::new();
        
        // 加载所有内置模板
        templates.insert("cargo_toml".to_string(), include_str!("../../templates/standard/Cargo.toml.tera").to_string());
        templates.insert("lib_rs".to_string(), include_str!("../../templates/standard/src/lib.rs.tera").to_string());
        templates.insert("nodes_mod".to_string(), include_str!("../../templates/standard/src/nodes/mod.rs.tera").to_string());
        templates.insert("node_definitions".to_string(), include_str!("../../templates/standard/src/nodes/node_definitions.rs.tera").to_string());
        templates.insert("fields".to_string(), include_str!("../../templates/standard/src/nodes/fields.rs.tera").to_string());
        templates.insert("plugins".to_string(), include_str!("../../templates/standard/src/plugins/mod.rs.tera").to_string());
        templates.insert("commands".to_string(), include_str!("../../templates/standard/src/command/mod.rs.tera").to_string());
        templates.insert("router_mod".to_string(), include_str!("../../templates/standard/src/router/mod.rs.tera").to_string());
        templates.insert("handlers".to_string(), include_str!("../../templates/standard/src/router/handlers.rs.tera").to_string());
        templates.insert("dto".to_string(), include_str!("../../templates/standard/src/router/dto.rs.tera").to_string());
        
        Self { templates }
    }
    
    /// 获取 Cargo.toml 模板
    pub fn get_cargo_toml_template(&self) -> &str {
        self.templates.get("cargo_toml").unwrap()
    }
    
    /// 获取 lib.rs 模板
    pub fn get_lib_rs_template(&self) -> &str {
        self.templates.get("lib_rs").unwrap()
    }
    
    /// 获取 nodes/mod.rs 模板
    pub fn get_nodes_mod_template(&self) -> &str {
        self.templates.get("nodes_mod").unwrap()
    }
    
    /// 获取节点定义模板
    pub fn get_node_definitions_template(&self) -> &str {
        self.templates.get("node_definitions").unwrap()
    }
    
    /// 获取字段定义模板
    pub fn get_fields_template(&self) -> &str {
        self.templates.get("fields").unwrap()
    }
    
    /// 获取插件模板
    pub fn get_plugins_template(&self) -> &str {
        self.templates.get("plugins").unwrap()
    }
    
    /// 获取命令模板
    pub fn get_commands_template(&self) -> &str {
        self.templates.get("commands").unwrap()
    }
    
    /// 获取路由器模块模板
    pub fn get_router_mod_template(&self) -> &str {
        self.templates.get("router_mod").unwrap()
    }
    
    /// 获取处理器模板
    pub fn get_handlers_template(&self) -> &str {
        self.templates.get("handlers").unwrap()
    }
    
    /// 获取 DTO 模板
    pub fn get_dto_template(&self) -> &str {
        self.templates.get("dto").unwrap()
    }
    
    /// 获取指定名称的模板
    pub fn get_template(&self, name: &str) -> Option<&String> {
        self.templates.get(name)
    }
    
    /// 列出所有可用的模板
    pub fn list_templates(&self) -> Vec<&String> {
        self.templates.keys().collect()
    }
    
    /// 检查模板是否存在
    pub fn has_template(&self, name: &str) -> bool {
        self.templates.contains_key(name)
    }
}

impl Default for TemplateManager {
    fn default() -> Self {
        Self::new()
    }
}