/// {{ description }} API 数据传输对象定义
/// 
/// 本模块定义了所有与{{ description }} API交互的数据结构
/// 包括请求、响应和查询参数等

use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};

// ==================== 通用查询和分页 ====================

/// 通用列表查询参数
#[derive(Debug, Deserialize)]
pub struct ListQuery {
    /// 页码（从1开始）
    #[serde(default = "default_page")]
    pub page: u32,
    /// 每页大小
    #[serde(default = "default_page_size")]
    pub page_size: u32,
    /// 搜索关键词
    pub search: Option<String>,
    /// 排序字段
    pub sort_by: Option<String>,
    /// 排序方向
    pub sort_order: Option<SortOrder>,
    /// 状态过滤
    pub status: Option<String>,
}

/// 排序方向
#[derive(Debug, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum SortOrder {
    Asc,
    Desc,
}

fn default_page() -> u32 { 1 }
fn default_page_size() -> u32 { 20 }

// ==================== {{ description }}相关DTO ====================

/// {{ description }}响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct {{ node_prefix }}Response {
    /// ID
    pub id: Uuid,
    /// 编码
    pub code: String,
    /// 名称
    pub name: String,
    /// 描述
    pub description: String,
    /// 总金额
    pub total_amount: f64,
    /// 行数
    pub row_count: u32,
    /// 状态
    pub status: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 创建{{ description }}请求
#[derive(Debug, Deserialize)]
pub struct Create{{ node_prefix }}Request {
    /// 编码
    pub code: String,
    /// 名称
    pub name: String,
    /// 描述
    pub description: String,
}

/// 更新{{ description }}请求
#[derive(Debug, Deserialize)]
pub struct Update{{ node_prefix }}Request {
    /// 编码
    pub code: Option<String>,
    /// 名称
    pub name: Option<String>,
    /// 描述
    pub description: Option<String>,
}

// ==================== {{ description }}行相关DTO ====================

/// {{ description }}行响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct {{ node_prefix }}RowResponse {
    /// ID
    pub id: Uuid,
    /// 父级ID
    pub parent_id: Uuid,
    /// 编码
    pub code: String,
    /// 名称
    pub name: String,
    /// 描述
    pub description: String,
    /// 数量
    pub quantity: f64,
    /// 单价
    pub unit_price: f64,
    /// 合价
    pub total_price: f64,
    /// 备注
    pub remarks: String,
}

/// 创建{{ description }}行请求
#[derive(Debug, Deserialize)]
pub struct Create{{ node_prefix }}RowRequest {
    /// 编码
    pub code: String,
    /// 名称
    pub name: String,
    /// 描述
    pub description: String,
    /// 数量
    #[serde(default = "default_quantity")]
    pub quantity: f64,
    /// 单价
    #[serde(default = "default_unit_price")]
    pub unit_price: f64,
    /// 备注
    pub remarks: Option<String>,
}

/// 更新{{ description }}行请求
#[derive(Debug, Deserialize)]
pub struct Update{{ node_prefix }}RowRequest {
    /// 编码
    pub code: Option<String>,
    /// 名称
    pub name: Option<String>,
    /// 描述
    pub description: Option<String>,
    /// 数量
    pub quantity: Option<f64>,
    /// 单价
    pub unit_price: Option<f64>,
    /// 备注
    pub remarks: Option<String>,
}

fn default_quantity() -> f64 { 1.0 }
fn default_unit_price() -> f64 { 0.0 }

// ==================== 计算相关DTO ====================

/// 计算{{ description }}请求
#[derive(Debug, Deserialize)]
pub struct Calculate{{ node_prefix }}Request {
    /// {{ description }}ID
    pub {{ names.snake }}_id: Uuid,
    /// 计算参数
    pub parameters: std::collections::HashMap<String, serde_json::Value>,
    /// 公式版本
    pub formula_version: Option<String>,
}

/// 计算{{ description }}响应
#[derive(Debug, Serialize)]
pub struct Calculate{{ node_prefix }}Response {
    /// 总金额
    pub total_amount: f64,
    /// 费用明细
    pub breakdown: Vec<CalculationBreakdown>,
    /// 计算时间
    pub calculation_date: DateTime<Utc>,
    /// 使用的公式版本
    pub formula_version: String,
}

/// 计算明细
#[derive(Debug, Serialize)]
pub struct CalculationBreakdown {
    /// 费用类别
    pub category: String,
    /// 金额
    pub amount: f64,
    /// 占比（百分比）
    pub percentage: f64,
    /// 详细说明
    pub details: String,
}

/// 批量计算{{ description }}请求
#[derive(Debug, Deserialize)]
pub struct BatchCalculate{{ node_prefix }}Request {
    /// 要计算的项目列表
    pub items: Vec<Calculate{{ node_prefix }}Request>,
}

/// 预览计算请求
#[derive(Debug, Deserialize)]
pub struct PreviewCalculationRequest {
    /// 基础数据
    pub base_data: std::collections::HashMap<String, serde_json::Value>,
    /// 计算参数
    pub parameters: std::collections::HashMap<String, serde_json::Value>,
}

/// 预览计算响应
#[derive(Debug, Serialize)]
pub struct PreviewCalculationResponse {
    /// 预估金额
    pub estimated_amount: f64,
    /// 置信度（百分比）
    pub confidence_level: f64,
    /// 影响因素
    pub factors: Vec<String>,
    /// 建议
    pub recommendations: Vec<String>,
}

// ==================== 验证相关DTO ====================

/// 验证结果
#[derive(Debug, Serialize)]
pub struct ValidationResult {
    /// 是否有效
    pub is_valid: bool,
    /// 错误列表
    pub errors: Vec<String>,
    /// 警告列表
    pub warnings: Vec<String>,
    /// 建议列表
    pub suggestions: Vec<String>,
}

// ==================== 导出相关DTO ====================

/// 导出{{ description }}请求
#[derive(Debug, Deserialize)]
pub struct Export{{ node_prefix }}Request {
    /// 导出格式
    pub format: ExportFormat,
    /// 包含的字段
    pub fields: Option<Vec<String>>,
    /// 过滤条件
    pub filters: Option<std::collections::HashMap<String, serde_json::Value>>,
}

/// 导出格式
#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum ExportFormat {
    Excel,
    Csv,
    Pdf,
    Json,
}

/// 导出{{ description }}响应
#[derive(Debug, Serialize)]
pub struct Export{{ node_prefix }}Response {
    /// 导出ID
    pub export_id: Uuid,
    /// 下载链接
    pub download_url: String,
    /// 过期时间
    pub expires_at: DateTime<Utc>,
    /// 文件大小（字节）
    pub file_size: u64,
    /// 文件格式
    pub format: ExportFormat,
}

// ==================== 公式相关DTO ====================

/// 计算公式
#[derive(Debug, Serialize)]
pub struct CalculationFormula {
    /// ID
    pub id: Uuid,
    /// 名称
    pub name: String,
    /// 版本
    pub version: String,
    /// 描述
    pub description: String,
    /// 公式表达式
    pub formula: String,
    /// 参数列表
    pub parameters: Vec<String>,
    /// 是否启用
    pub is_active: bool,
}

// ==================== 系统相关DTO ====================

/// 健康状态
#[derive(Debug, Serialize)]
pub struct HealthStatus {
    /// 状态
    pub status: String,
    /// 检查时间
    pub timestamp: DateTime<Utc>,
    /// 版本
    pub version: String,
    /// 运行时间（秒）
    pub uptime: u64,
}

/// 扩展信息
#[derive(Debug, Serialize)]
pub struct ExtensionInfo {
    /// 扩展名称
    pub name: String,
    /// 扩展描述
    pub description: String,
    /// 版本号
    pub version: String,
    /// 作者
    pub author: String,
    /// 功能列表
    pub features: Vec<String>,
    /// API版本
    pub api_version: String,
}

// ==================== 测试工具 ====================

#[cfg(test)]
impl {{ node_prefix }}Response {
    /// 创建测试用的{{ description }}响应
    pub fn mock() -> Self {
        Self {
            id: Uuid::new_v4(),
            code: "TEST001".to_string(),
            name: "测试{{ description }}".to_string(),
            description: "这是一个测试{{ description }}".to_string(),
            total_amount: 1000.0,
            row_count: 5,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }
}

#[cfg(test)]
impl {{ node_prefix }}RowResponse {
    /// 创建测试用的{{ description }}行响应
    pub fn mock(parent_id: Uuid) -> Self {
        Self {
            id: Uuid::new_v4(),
            parent_id,
            code: "ROW001".to_string(),
            name: "测试行".to_string(),
            description: "这是一个测试行".to_string(),
            quantity: 1.0,
            unit_price: 100.0,
            total_price: 100.0,
            remarks: "测试备注".to_string(),
        }
    }
}