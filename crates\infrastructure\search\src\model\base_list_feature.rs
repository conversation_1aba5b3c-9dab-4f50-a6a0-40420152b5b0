/*
CREATE TABLE "base_list_feature" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "library_code" varchar,
  "list_code" varchar,
  "feature_name" varchar,
  "feature_value" varchar,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ( 'A' ),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "description" varchar,
  "agency_code" varchar,
"product_code" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseListFeature {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub list_code: Option<String>,
    pub feature_name: Option<String>,
    pub feature_value: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseListFeature {}, "base_list_feature");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "list_code".to_string(),
            "feature_name".to_string(),
            "feature_value".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "list_code".to_string(),
            "feature_name".to_string(),
            "feature_value".to_string(),
        ]),
        sortable_attributes: Some(vec!["library_code".to_string(), "list_code".to_string(), "feature_name".to_string(), "feature_value".to_string()]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseListFeature>("base_list_feature", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseListFeature>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_list_feature").await?;
    let base_list_index = IndexClient::<BaseListFeature> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
