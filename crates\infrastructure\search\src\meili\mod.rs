pub mod client;
pub mod error;
pub mod model;

use std::sync::Arc;

use crate::meili::client::SearchClient;
use crate::meili::model::SearchConfig;
pub use meilisearch_sdk;
use state::InitCell;

pub static MEILI_SEARCH_CLIENT: InitCell<Arc<SearchClient>> = InitCell::new();

pub fn init_search_client(config: Option<SearchConfig>) {
    MEILI_SEARCH_CLIENT.set(Arc::new(SearchClient::new(config.unwrap_or_default())));
}
