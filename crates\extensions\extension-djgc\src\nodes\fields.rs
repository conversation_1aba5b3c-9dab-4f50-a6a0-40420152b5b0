use mf_core::node::Node;

use super::node_definitions::{DjgcFactory, DjgcContainerNode, DjgcRowNode};

///构建单价构成节点 节点定义
///
/// 节点树结构:
/// djgc (单价构成)
/// └── djgcNode+ (单价构成行节点)
///     ├── djgcqfCode (模版编码)
///     ├── djgcstandard (标准)
///     ├── djgctype (单价构成类型)
///     ├── djgccode (费用代号)
///     ├── djgccaculateBase (计算基数)
///     ├── djgcdesc (描述)
///     ├── djgcrate (费率)
///     └── djgcprice (单价)
///
pub fn init_nodes() -> Vec<Node> {
    DjgcFactory::create_djgc_structure()
}

// 为向后兼容提供静态访问器
pub fn djgc_container_node() -> Node {
    DjgcContainerNode::node_definition()
}

pub fn djgc_row_node() -> Node {
    DjgcRowNode::node_definition()
}
