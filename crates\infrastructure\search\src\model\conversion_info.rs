/*
CREATE TABLE "conversion_info" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "conversion_string" varchar,
  "conversion_explain" varchar,
  "source" varchar,
  "source_code" varchar,
  "rule_id" varchar,
  "de_id" varchar,
  "agency_code" varchar,
  "product_code" varchar,
  "rec_status" varchar DEFAULT ( 'A' ),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "description" varchar,
"rec_user_code" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct ConversionInfo {
    pub sequence_nbr: String,
    pub conversion_string: Option<String>,
    pub conversion_explain: Option<String>,
    pub source: Option<String>,
    pub source_code: Option<String>,
    pub rule_id: Option<String>,
    pub de_id: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(ConversionInfo {}, "conversion_info");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "conversion_string".to_string(),
            "conversion_explain".to_string(),
            "source".to_string(),
            "source_code".to_string(),
            "rule_id".to_string(),
            "de_id".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "conversion_string".to_string(),
            "conversion_explain".to_string(),
            "source".to_string(),
            "source_code".to_string(),
            "rule_id".to_string(),
            "de_id".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "conversion_string".to_string(),
            "conversion_explain".to_string(),
            "source".to_string(),
            "source_code".to_string(),
            "rule_id".to_string(),
            "de_id".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<ConversionInfo>("conversion_info", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<ConversionInfo>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("conversion_info").await?;
    let base_list_index = IndexClient::<ConversionInfo> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
