/// 简单的路由器使用示例
/// 
/// 这个示例展示了如何使用 extension-base-schema 提供的路由器
use extension_base_schema::BaseSchemaRouter;
use price_web::app::AppBuilder;

#[tokio::main]
async fn main() {
    println!("启动 extension-base-schema 路由器示例...");

    // 创建应用构建器
    let app = AppBuilder::new()
        // 添加基础扩展路由
        .next("/api/v1/base-schema", BaseSchemaRouter::create_router())
        .addr("127.0.0.1:8080".to_string())
        .build();

    println!("服务器将在 http://127.0.0.1:8080 启动");
    println!("可用的端点:");
    println!("  GET /api/v1/base-schema/health");
    println!("  GET /api/v1/base-schema/info");

    // 启动服务器
    app.run().await;
}