[package]
name = "{{ project_name }}"
version = { workspace = true }
edition = { workspace = true }
authors = { workspace = true }
description = "{{ description }}"

[dependencies]
# 基础依赖
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }

# ModuForge核心依赖
{% if has_nodes -%}
mf_derive = { workspace = true }
mf_core = { workspace = true }
{%- endif %}
{% if has_plugins -%}
mf_state = { workspace = true }
async_trait = { workspace = true }
{%- endif %}
{% if has_commands -%}
mf_macro = { workspace = true }
mf_transform = { workspace = true }
{%- endif %}

# Price-RS基础设施
price_common = { workspace = true }
{% if has_router -%}
price_web = { workspace = true }
{%- endif %}

# 工具依赖
uuid = { workspace = true }
chrono = { workspace = true }

{% if has_router -%}
# Web框架依赖
axum = { workspace = true }
tower = { workspace = true }
tower-http = { workspace = true }
{%- endif %}