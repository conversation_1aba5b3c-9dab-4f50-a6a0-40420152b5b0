use anyhow::Ok;
use mf_state::debug;
use price_search::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient, SearchService},
    model::{IndexSettings, SearchOptions},
};
use serde::{Deserialize, Serialize};

/// 示例文档结构
#[derive(Debug, Serialize, Deserialize)]
struct Product {
    /// 产品ID
    id: String,
    /// 产品名称
    name: String,
    /// 产品描述
    description: String,
    /// 产品价格
    price: f64,
    /// 产品分类
    category: String,
}

/// 创建索引示例
pub async fn create_product_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();

    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("id".to_string()),
        searchable_attributes: Some(vec!["name".to_string(), "description".to_string(), "category".to_string()]),
        filterable_attributes: Some(vec!["price".to_string(), "category".to_string()]),
        sortable_attributes: Some(vec!["price".to_string()]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let product_index = client.create_index::<Product>("products", Some(settings)).await?;

    // 添加示例产品数据
    let products = vec![
        Product {
            id: "1".to_string(),
            name: "智能手机".to_string(),
            description: "高性能5G智能手机，搭载最新处理器".to_string(),
            price: 4999.0,
            category: "电子产品".to_string(),
        },
        Product {
            id: "2".to_string(),
            name: "笔记本电脑".to_string(),
            description: "轻薄笔记本电脑，适合办公和日常使用".to_string(),
            price: 6999.0,
            category: "电子产品".to_string(),
        },
        Product {
            id: "3".to_string(),
            name: "无线耳机".to_string(),
            description: "高音质无线蓝牙耳机，降噪效果好".to_string(),
            price: 999.0,
            category: "配件".to_string(),
        },
    ];

    // 添加文档到索引
    let task = product_index.add_documents(products, None).await?;
    println!("添加文档任务ID: {:?}", task.task_uid);

    Ok(())
}

/// 搜索示例
pub async fn search_products() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();

    // 获取索引
    let index = client.get_index("products").await?;
    let product_index = IndexClient::<Product> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(product_index);

    // 基础搜索
    let options = SearchOptions { query: "智能".to_string(), limit: Some(10), offset: Some(0), ..Default::default() };

    // 执行搜索
    let results = search_service.search(&options).await?;

    // 打印搜索结果
    debug!("搜索结果：共找到 {} 条匹配", results.total_hits);
    for product in results.hits.iter() {
        debug!("{:?}", product);
    }
    debug!("搜索耗时：{} ms", results.processing_time_ms);
    Ok(())
}

/// 获取和删除文档示例
pub async fn document_operations() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();

    // 获取索引并创建搜索服务
    let index = client.get_index("products2").await?;
    let product_index = IndexClient::<Product> { index, _phantom: std::marker::PhantomData };
    let search_service = BasicSearchService::new(product_index);

    // 通过ID获取文档
    let product = search_service.get_by_id("1").await?;
    println!("获取到产品: {} - {}", product.name, product.description);

    // 删除文档
    search_service.delete_by_id("3").await?;
    println!("已删除产品ID: 3");

    Ok(())
}

pub async fn test_all_search() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();

    let all_index = client.get_all_index().await?;
    println!("当前有{}个索引", all_index.len());
    for index in all_index {
        println!("索引名称: {}", index.primary_key.unwrap());
    }
    Ok(())
}
