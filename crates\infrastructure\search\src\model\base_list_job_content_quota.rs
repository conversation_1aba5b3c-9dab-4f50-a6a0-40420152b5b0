/*
CREATE TABLE "base_list_job_content_quota" (
  "sequence_nbr" text(20) NOT NULL,
  "library_code_list" text(255),
  "list_code" text(255),
  "list_id" text(20),
  "job_content" text(255),
  "quota_id" text(20),
  "library_code" text(255),
  "quota_code" text(255),
  "quota_name" text(255),
  "unit" text(255),
  "price" real(16,2),
  "sort_no" integer(11),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" integer(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255)
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};
#[derive(Debug, Serialize, Deserialize)]
pub struct BaseList<PERSON>obContentQuota {
    pub sequence_nbr: String,
    pub library_code_list: Option<String>,
    pub list_code: Option<String>,
    pub list_id: Option<String>,
    pub job_content: Option<String>,
    pub quota_id: Option<String>,
    pub library_code: Option<String>,
    pub quota_code: Option<String>,
    pub quota_name: Option<String>,
    pub unit: Option<String>,
    pub price: Option<String>,
    pub sort_no: Option<f64>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(BaseListJobContentQuota {}, "base_list_job_content_quota");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code_list".to_string(),
            "list_code".to_string(),
            "list_id".to_string(),
            "job_content".to_string(),
            "quota_id".to_string(),
            "library_code".to_string(),
            "quota_code".to_string(),
            "quota_name".to_string(),
            "unit".to_string(),
            "price".to_string(),
            "sort_no".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code_list".to_string(),
            "list_code".to_string(),
            "list_id".to_string(),
            "job_content".to_string(),
            "quota_id".to_string(),
            "library_code".to_string(),
            "quota_code".to_string(),
            "quota_name".to_string(),
            "unit".to_string(),
            "price".to_string(),
            "sort_no".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code_list".to_string(),
            "list_code".to_string(),
            "list_id".to_string(),
            "job_content".to_string(),
            "quota_id".to_string(),
            "library_code".to_string(),
            "quota_code".to_string(),
            "quota_name".to_string(),
            "unit".to_string(),
            "price".to_string(),
            "sort_no".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseListJobContentQuota>("base_list_job_content_quota", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseListJobContentQuota>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_list_job_content_quota").await?;
    let base_list_index = IndexClient::<BaseListJobContentQuota> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
