[package]
name = "price-storage"
version = "0.1.0"
edition = "2021"
description = "计价软件存储"
[lib]
path="./src/lib.rs"

[dependencies]
moduforge-model = { workspace = true }
moduforge-state = { workspace = true }
moduforge-transform = { workspace = true }
moduforge-core = { workspace = true }
rbs = {workspace = true}
rbatis = {workspace = true}
rbdc-sqlite = { workspace = true }
async-trait ={workspace = true}
anyhow = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
zstd = "0.13"

sha2 = "0.10"
chrono = { workspace = true }
zip = "3.0.0"
uuid = { workspace = true }
tokio = { workspace = true }
aes-gcm = "0.10.3"
base64 = "0.21.7"
rand = "0.8.5"
tempfile = "3.8.1"