use std::collections::HashMap;

use async_trait::async_trait;
use mf_state::{Transaction, transaction::Command};
use mf_transform::TransformResult;
use serde::Deserialize;
use serde_json::Value;

#[derive(Debug, Deserialize)]
pub struct InsertCommand {
    pub parent_id: Box<str>,
    pub name: Box<str>,
    pub r#type: String,
    pub other: HashMap<String, Value>,
}
impl InsertCommand {
    pub fn new(
        parent_id: Box<str>,
        name: Box<str>,
        r#type: String,
    ) -> Self {
        Self { parent_id, name, r#type, other: HashMap::new() }
    }
    pub fn add_attr(
        &mut self,
        key: &str,
        value: Value,
    ) {
        self.other.insert(key.to_string(), value);
    }
}

#[async_trait]
impl Command for InsertCommand {
    async fn execute(
        &self,
        tr: &mut Transaction,
    ) -> TransformResult<()> {
        // 根据参数中的parent_id获取目标节点
        let point_node = {
            match tr.doc().get_node(&self.parent_id) {
                Some(n) => n,
                None => {
                    return Err(anyhow::anyhow!("目标节点不存在".to_string()));
                },
            }
        };

        if let Some(node_type) = tr.schema.nodes.get(&self.r#type) {
            let nodes = node_type.create_and_fill(None, Some(&self.other), vec![], None, &tr.schema);
            tr.add_node(point_node.id.clone(), vec![nodes])?;
        } else {
            return Err(anyhow::anyhow!("type参数节点类型不存在".to_string()));
        }

        // set_meta 可以给后续流程传递参数
        // tr.set_meta(key, value);
        // tr.add_node(self.point_id.clone(), vec![tr.schema.nodes.get("DWGC").unwrap().create(None, None, vec![], None)]);
        Ok(())
    }

    fn name(&self) -> String {
        "InsertCommand".to_string()
    }
}
