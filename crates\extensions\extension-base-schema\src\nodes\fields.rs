use mf_core::node::Node;

use super::project_structure::{ProjectStructureFactory, ProjectNode, SingleProjectNode, UnitProjectNode};

pub const DWGC_STR: &str = "DWGC";
pub const DXGC_STR: &str = "DXGC";
pub const GCXM_STR: &str = "GCXM";

///构建 工程项目结构          节点定义
///
/// 节点树结构:
/// GCXM (工程项目)
/// └── DXGC* (多个单项)
///     └── DWGC* (多个单位)
///
pub fn init_project_structure() -> Vec<Node> {
    ProjectStructureFactory::create_project_structure()
}

///构建 单位工程 项目结构      节点定义
///
/// 节点树结构:
/// GCXM (工程项目)
/// └── DWGC* (多个单位)
///
pub fn init_unit_project_structure() -> Vec<Node> {
    ProjectStructureFactory::create_unit_project_structure()
}

// 为向后兼容提供静态访问器
pub fn gcxm_node() -> Node {
    ProjectNode::node_definition()
}

pub fn dxgc_node() -> Node {
    SingleProjectNode::node_definition()
}

pub fn dwgc_node() -> Node {
    UnitProjectNode::node_definition()
}




// pub fn init_attr_fields() {
//     let mut ext = Extension::new();
//     let attributes = HashMap::from_iter(vec![("constructName".to_string(), AttributeSpec { default: None })]);
//     // 工程项目、单项工程、单位工程 给这三个节点定义属性
//     ext.add_global_attribute(GlobalAttributeItem { types: vec!["GCXM".to_string(), "DXGC".to_string(), "DWGC".to_string()], attributes });
// }
