/*
CREATE TABLE "base_de_zs_cg_relation" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "storey" varchar,
  "zs_de_code" varchar,
  "cg_de_code" varchar,
  "location" varchar,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ('A'),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "description" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeZsCgRelation {
    pub sequence_nbr: String,
    pub storey: Option<String>,
    pub zs_de_code: Option<String>,
    pub cg_de_code: Option<String>,
    pub location: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(BaseDeZsCgRelation {}, "base_de_zs_cg_relation");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "storey".to_string(),
            "zs_de_code".to_string(),
            "cg_de_code".to_string(),
            "location".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "storey".to_string(),
            "zs_de_code".to_string(),
            "cg_de_code".to_string(),
            "location".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "storey".to_string(),
            "zs_de_code".to_string(),
            "cg_de_code".to_string(),
            "location".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeZsCgRelation>("base_de_zs_cg_relation", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeZsCgRelation>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_zs_cg_relation").await?;
    let base_de_zs_cg_relation_index = IndexClient::<BaseDeZsCgRelation> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_zs_cg_relation_index);
    Ok(search_service)
}
