/*
CREATE TABLE "base_list_2022" (
  "sequence_nbr" text(20) NOT NULL,
  "library_code" text(255),
  "library_name" text(255),
  "bd_code_level01" text(255),
  "bd_name_level01" text(255),
  "bd_code_level02" text(255),
  "bd_name_level02" text(255),
  "bd_code_level03" text(255),
  "bd_name_level03" text(255),
  "bd_code_level04" text(255),
  "bd_name_level04" text(255),
  "unit" text(50),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "zjcs_label_name" text(255),
  "zjcs_class_code" text(255),
  "zjcs_class_name" text(255),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseList2022 {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub library_name: Option<String>,
    pub bd_code_level01: Option<String>,
    pub bd_name_level01: Option<String>,
    pub bd_code_level02: Option<String>,
    pub bd_name_level02: Option<String>,
    pub bd_code_level03: Option<String>,
    pub bd_name_level03: Option<String>,
    pub bd_code_level04: Option<String>,
    pub bd_name_level04: Option<String>,
    pub unit: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub zjcs_label_name: Option<String>,
    pub zjcs_class_code: Option<String>,
    pub zjcs_class_name: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseList2022 {}, "base_list_2022");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "bd_code_level01".to_string(),
            "bd_name_level01".to_string(),
            "bd_code_level02".to_string(),
            "bd_name_level02".to_string(),
            "bd_code_level03".to_string(),
            "bd_name_level03".to_string(),
            "bd_code_level04".to_string(),
            "bd_name_level04".to_string(),
            "unit".to_string(),
            "zjcs_label_name".to_string(),
            "zjcs_class_code".to_string(),
            "zjcs_class_name".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "bd_code_level01".to_string(),
            "bd_name_level01".to_string(),
            "bd_code_level02".to_string(),
            "bd_name_level02".to_string(),
            "bd_code_level03".to_string(),
            "bd_name_level03".to_string(),
            "bd_code_level04".to_string(),
            "bd_name_level04".to_string(),
            "unit".to_string(),
            "zjcs_label_name".to_string(),
            "zjcs_class_code".to_string(),
            "zjcs_class_name".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "bd_code_level01".to_string(),
            "bd_name_level01".to_string(),
            "bd_code_level02".to_string(),
            "bd_name_level02".to_string(),
            "bd_code_level03".to_string(),
            "bd_name_level03".to_string(),
            "bd_code_level04".to_string(),
            "bd_name_level04".to_string(),
            "unit".to_string(),
            "zjcs_label_name".to_string(),
            "zjcs_class_code".to_string(),
            "zjcs_class_name".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseList2022>("base_list_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseList2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_list_2022").await?;
    let base_list_index = IndexClient::<BaseList2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
