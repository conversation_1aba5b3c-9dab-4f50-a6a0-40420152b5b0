/*
CREATE TABLE "base_tax_reform_documents" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "name" varchar,
  "type" varchar,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ( 'A' ),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
"description" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseTaxReformDocuments {
    pub sequence_nbr: String,
    pub name: String,
    pub r#type: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(BaseTaxReformDocuments {}, "base_tax_reform_documents");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec!["name".to_string(), "type".to_string()]),
        filterable_attributes: Some(vec!["name".to_string(), "type".to_string()]),
        sortable_attributes: Some(vec!["name".to_string(), "type".to_string()]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseTaxReformDocuments>("base_tax_reform_documents", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseTaxReformDocuments>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_tax_reform_documents").await?;
    let base_list_index = IndexClient::<BaseTaxReformDocuments> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
