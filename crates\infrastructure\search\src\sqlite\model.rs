use std::time::Duration;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct SqliteConfig {
    /// 数据库文件路径
    pub path: String,
    /// 是否启用 WAL 模式 - Write Ahead Logging，用于提高写入性能
    pub max_connections: u32,
    /// 是否启用外键约束，用于维护数据完整性
    pub connect_timeout: Duration,
}

/// 为 SqliteConfig 实现默认值
impl Default for SqliteConfig {
    fn default() -> Self {
        Self { path: "db.sqlite".to_string(), max_connections: 10, connect_timeout: Duration::from_secs(10) }
    }
}
