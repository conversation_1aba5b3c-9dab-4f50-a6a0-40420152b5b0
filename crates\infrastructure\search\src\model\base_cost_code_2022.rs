/*
CREATE TABLE "base_cost_code_2022" (
  "sequence_nbr" text NOT NULL,
  "type" text(16) NOT NULL,
  "mark_code" text(32),
  "code" text(32) NOT NULL,
  "name" text(255) NOT NULL,
  "province" integer(20),
  "de_standard_id" text(20),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64)
); */

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};
#[derive(Debug, Serialize, Deserialize)]
pub struct BaseCostCode2022 {
    pub sequence_nbr: String,
    pub r#type: String,
    pub mark_code: Option<String>,
    pub code: String,
    pub name: String,
    pub province: Option<i32>,
    pub de_standard_id: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseCostCode2022 {}, "base_cost_code_2022");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "type".to_string(),
            "mark_code".to_string(),
            "code".to_string(),
            "name".to_string(),
            "province".to_string(),
            "de_standard_id".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "type".to_string(),
            "mark_code".to_string(),
            "code".to_string(),
            "name".to_string(),
            "province".to_string(),
            "de_standard_id".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "type".to_string(),
            "mark_code".to_string(),
            "code".to_string(),
            "name".to_string(),
            "province".to_string(),
            "de_standard_id".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseCostCode2022>("base_cost_code_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseCostCode2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_cost_code_2022").await?;
    let base_cost_code_2022_index = IndexClient::<BaseCostCode2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_cost_code_2022_index);
    Ok(search_service)
}
