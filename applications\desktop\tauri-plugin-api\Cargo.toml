[package]
name = "tauri-plugin-api"
version = "0.1.0"
authors = [ "You" ]
description = ""
edition = "2021"
rust-version = "1.71"
exclude = ["/examples", "/webview-dist", "/webview-src", "/node_modules"]
links = "tauri-plugin-api"

[dependencies]
tauri = { version = "2" }
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
axum = { workspace = true }
tokio = { workspace = true }
price-budget = { workspace = true }
price-web = { workspace = true }
shared = { workspace = true }
async-channel = { workspace = true }
async-trait = { workspace = true }
anyhow = { workspace = true }
dashmap = { workspace = true }
tower=  { workspace = true }


[build-dependencies]
tauri-plugin = { version = "2", features = ["build"] }
