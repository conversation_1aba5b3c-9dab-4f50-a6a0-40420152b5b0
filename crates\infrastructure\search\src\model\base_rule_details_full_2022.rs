/*
CREATE TABLE "base_rule_details_full_2022" (
  "sequence_nbr" text(18),
  "library_code" text(255),
  "relation_group_code" text(255),
  "kind" text(255),
  "relation_group_id" text(255),
  "relation_group_name" text(255),
  "relation_group_cnt" integer(11),
  "relation_group_rule" text(255),
  "file_details_id" text(18),
  "relation_code" text(255),
  "relation" text(255),
  "math" text(255),
  "de_id" text(18),
  "de_code" text(255),
  "de_name" text(255),
  "default_value" real(16,6),
  "default_value_max" text(255),
  "default_value_min" text(255),
  "rule_range" text(255),
  "relation_de_code" text(255),
  "relation_de_id" text(18),
  "data_format" integer(11),
  "sort_no" integer(18),
  "type" text(255),
  "rec_user_code" text(255),
  "rec_status" text(255),
  "rec_date" text(255),
  "extend1" text(255),
  "extend2" text(255),
  "extend3" text(255),
  "description" text(255),
  "agency_code" text(255),
  "product_code" text(255),
  "top_group_type" text(255),
  "rcj_id" text(18),
  "sort_no_global" integer(11),
  "exclude_material_codes" text(255)
);

CREATE INDEX "index_deId"
ON "base_rule_details_full_2022" (
  "de_id" ASC
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseRuleDetailsFull2022 {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub relation_group_code: Option<String>,
    pub kind: Option<String>,
    pub relation_group_id: Option<String>,
    pub relation_group_name: Option<String>,
    pub relation_group_cnt: Option<i32>,
    pub relation_group_rule: Option<String>,
    pub file_details_id: Option<String>,
    pub relation_code: Option<String>,
    pub relation: Option<String>,
    pub math: Option<String>,
    pub de_id: Option<String>,
    pub de_code: Option<String>,
    pub de_name: Option<String>,
    pub default_value: Option<f64>,
    pub default_value_max: Option<String>,
    pub default_value_min: Option<String>,
    pub rule_range: Option<String>,
    pub relation_de_code: Option<String>,
    pub relation_de_id: Option<String>,
    pub data_format: Option<i32>,
    pub sort_no: Option<i32>,
    pub r#type: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
    pub top_group_type: Option<String>,
    pub rcj_id: Option<String>,
    pub sort_no_global: Option<i32>,
    pub exclude_material_codes: Option<String>,
}
//crud!(BaseRuleDetailsFull2022 {}, "base_rule_details_full_2022");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "relation_group_code".to_string(),
            "kind".to_string(),
            "relation_group_id".to_string(),
            "relation_group_name".to_string(),
            "relation_group_cnt".to_string(),
            "relation_group_rule".to_string(),
            "file_details_id".to_string(),
            "relation_code".to_string(),
            "relation".to_string(),
            "math".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "default_value".to_string(),
            "default_value_max".to_string(),
            "default_value_min".to_string(),
            "rule_range".to_string(),
            "relation_de_code".to_string(),
            "relation_de_id".to_string(),
            "data_format".to_string(),
            "sort_no".to_string(),
            "type".to_string(),
            "top_group_type".to_string(),
            "rcj_id".to_string(),
            "sort_no_global".to_string(),
            "exclude_material_codes".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "relation_group_code".to_string(),
            "kind".to_string(),
            "relation_group_id".to_string(),
            "relation_group_name".to_string(),
            "relation_group_cnt".to_string(),
            "relation_group_rule".to_string(),
            "file_details_id".to_string(),
            "relation_code".to_string(),
            "relation".to_string(),
            "math".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "default_value".to_string(),
            "default_value_max".to_string(),
            "default_value_min".to_string(),
            "rule_range".to_string(),
            "relation_de_code".to_string(),
            "relation_de_id".to_string(),
            "data_format".to_string(),
            "sort_no".to_string(),
            "type".to_string(),
            "top_group_type".to_string(),
            "rcj_id".to_string(),
            "sort_no_global".to_string(),
            "exclude_material_codes".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "relation_group_code".to_string(),
            "kind".to_string(),
            "relation_group_id".to_string(),
            "relation_group_name".to_string(),
            "relation_group_cnt".to_string(),
            "relation_group_rule".to_string(),
            "file_details_id".to_string(),
            "relation_code".to_string(),
            "relation".to_string(),
            "math".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "default_value".to_string(),
            "default_value_max".to_string(),
            "default_value_min".to_string(),
            "rule_range".to_string(),
            "relation_de_code".to_string(),
            "relation_de_id".to_string(),
            "data_format".to_string(),
            "sort_no".to_string(),
            "type".to_string(),
            "top_group_type".to_string(),
            "rcj_id".to_string(),
            "sort_no_global".to_string(),
            "exclude_material_codes".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseRuleDetailsFull2022>("base_rule_details_full_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseRuleDetailsFull2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_rule_details_full_2022").await?;
    let base_list_index = IndexClient::<BaseRuleDetailsFull2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
