/*
CREATE TABLE "base_unit_project_type" (
  "sequence_nbr" text(20) NOT NULL,
  "unit_project_name" text(255),
  "del_flag" text(50),
  "remark" text(1024),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  "sort_no" integer(11),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseUnitProjectType {
    pub sequence_nbr: String,
    pub unit_project_name: Option<String>,
    pub del_flag: Option<String>,
    pub remark: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
    pub sort_no: Option<i32>,
}
//crud!(BaseUnitProjectType {}, "base_unit_project_type");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec!["unit_project_name".to_string(), "del_flag".to_string(), "remark".to_string(), "sort_no".to_string()]),
        filterable_attributes: Some(vec!["unit_project_name".to_string(), "del_flag".to_string(), "remark".to_string(), "sort_no".to_string()]),
        sortable_attributes: Some(vec!["unit_project_name".to_string(), "del_flag".to_string(), "remark".to_string(), "sort_no".to_string()]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseUnitProjectType>("base_unit_project_type", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseUnitProjectType>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_unit_project_type").await?;
    let base_list_index = IndexClient::<BaseUnitProjectType> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
