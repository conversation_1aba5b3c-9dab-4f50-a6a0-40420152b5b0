{"default": {"identifier": "default", "description": "enables the default permissions", "local": true, "windows": ["*"], "permissions": ["core:default", "core:window:default", "core:window:allow-create", "core:window:allow-start-dragging", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-toggle-maximize", "core:window:allow-unmaximize", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-set-focus", "core:window:allow-center", "core:window:allow-set-position", "core:window:allow-set-size", "core:window:allow-is-maximized", "core:window:allow-is-minimized", "core:window:allow-is-visible", "core:window:allow-is-focused", "core:webview:default", "core:webview:allow-create-webview-window", "core:tray:allow-get-by-id", "core:tray:allow-new", "core:tray:allow-set-menu", "core:tray:allow-set-icon", "core:tray:allow-set-tooltip", "core:tray:allow-set-title", "core:tray:allow-set-temp-dir-path"]}, "remote-capability": {"identifier": "remote-capability", "description": "enables permissions for remote URLs (localhost development)", "remote": {"urls": ["http://localhost:*", "https://localhost:*"]}, "local": true, "windows": ["*"], "webviews": ["*"], "permissions": ["core:default", "core:window:default", "core:window:allow-create", "core:window:allow-start-dragging", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-toggle-maximize", "core:window:allow-unmaximize", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-set-focus", "core:window:allow-center", "core:window:allow-set-position", "core:window:allow-set-size", "core:window:allow-is-maximized", "core:window:allow-is-minimized", "core:window:allow-is-visible", "core:window:allow-is-focused", "core:webview:default", "core:webview:allow-create-webview-window"]}}