// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use p_desktop_lib::{init_context, init_event_handler};
use tauri::Manager;
#[tokio::main]
async fn main() -> anyhow::Result<()> {
    init_context().await?;
    run().await?;
    Ok(())
}

/// 启动入口
pub async fn run() -> anyhow::Result<()> {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            let app_handle = app.app_handle();
            init_event_handler(app_handle.clone());
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
    Ok(())
}
