/*
CREATE TABLE "base_fee_file" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "qf_code" varchar,
  "qf_name" varchar,
  "del_flag" varchar,
  "remark" varchar,
  "sort_no" integer,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ('A'),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "description" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseFeeFile {
    pub sequence_nbr: String,
    pub qf_code: Option<String>,
    pub qf_name: Option<String>,
    pub del_flag: Option<String>,
    pub remark: Option<String>,
    pub sort_no: Option<i32>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(BaseFeeFile {}, "base_fee_file");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "qf_code".to_string(),
            "qf_name".to_string(),
            "del_flag".to_string(),
            "remark".to_string(),
            "sort_no".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "qf_code".to_string(),
            "qf_name".to_string(),
            "del_flag".to_string(),
            "remark".to_string(),
            "sort_no".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "qf_code".to_string(),
            "qf_name".to_string(),
            "del_flag".to_string(),
            "remark".to_string(),
            "sort_no".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseFeeFile>("base_fee_file", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseFeeFile>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_fee_file").await?;
    let base_fee_file_index = IndexClient::<BaseFeeFile> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_fee_file_index);
    Ok(search_service)
}
