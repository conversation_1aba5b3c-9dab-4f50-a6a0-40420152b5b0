/*
CREATE TABLE "base_list_calc_rule" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "library_code" varchar,
  "list_code" varchar,
  "calc_rule" varchar,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ( 'A' ),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
"description" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseListCalcRule {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub list_code: Option<String>,
    pub calc_rule: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(BaseListCalcRule {}, "base_list_calc_rule");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec!["library_code".to_string(), "list_code".to_string(), "calc_rule".to_string()]),
        filterable_attributes: Some(vec!["library_code".to_string(), "list_code".to_string(), "calc_rule".to_string()]),
        sortable_attributes: Some(vec!["library_code".to_string(), "list_code".to_string(), "calc_rule".to_string()]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseListCalcRule>("base_list_calc_rule", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseListCalcRule>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_list_calc_rule").await?;
    let base_list_index = IndexClient::<BaseListCalcRule> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
