/*
CREATE TABLE "base_de_chapter_2022" (
  "sequence_nbr" text(255) NOT NULL,
  "library_code" text(255),
  "library_name" text(255),
  "classlevel07" text(255),
  "classlevel06" text(255),
  "classlevel05" text(255),
  "classlevel04" text(255),
  "classlevel03" text(255),
  "classlevel02" text(255),
  "classlevel01" text(255),
  "sort_no" integer(11),
  "classlevel_split_concat" text(255),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeChapter2022 {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub library_name: Option<String>,
    pub classlevel07: Option<String>,
    pub classlevel06: Option<String>,
    pub classlevel05: Option<String>,
    pub classlevel04: Option<String>,
    pub classlevel03: Option<String>,
    pub classlevel02: Option<String>,
    pub classlevel01: Option<String>,
    pub sort_no: Option<i32>,
    pub classlevel_split_concat: Option<String>,
}
//crud!(BaseDeChapter2022 {}, "base_de_chapter_2022");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "classlevel07".to_string(),
            "classlevel06".to_string(),
            "classlevel05".to_string(),
            "classlevel04".to_string(),
            "classlevel03".to_string(),
            "classlevel02".to_string(),
            "classlevel01".to_string(),
            "classlevel_split_concat".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "classlevel07".to_string(),
            "classlevel06".to_string(),
            "classlevel05".to_string(),
            "classlevel04".to_string(),
            "classlevel03".to_string(),
            "classlevel02".to_string(),
            "classlevel01".to_string(),
            "sort_no".to_string(),
            "classlevel_split_concat".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "classlevel07".to_string(),
            "classlevel06".to_string(),
            "classlevel05".to_string(),
            "classlevel04".to_string(),
            "classlevel03".to_string(),
            "classlevel02".to_string(),
            "classlevel01".to_string(),
            "sort_no".to_string(),
            "classlevel_split_concat".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeChapter2022>("base_de_chapter_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeChapter2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_chapter_2022").await?;
    let base_de_chapter_2022_index = IndexClient::<BaseDeChapter2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_chapter_2022_index);
    Ok(search_service)
}
