# Search 库

Meilisearch 搜索引擎的 Rust 封装库，提供简单易用的 API 进行文档索引和搜索。

## 功能特性

- 简单易用的 Meilisearch 客户端封装
- 文档索引和更新
- 全文搜索与过滤
- 错误处理封装
- 异步接口支持

## 依赖

- Rust 2024 Edition
- meilisearch-sdk 0.25.0
- tokio (异步运行时)
- serde (序列化支持)

## 使用示例

### 初始化客户端

```rust
use search::{SearchClient, SearchConfig};

// 创建搜索配置
let config = SearchConfig {
    host: "http://localhost:7700".to_string(),
    api_key: Some("your_api_key".to_string()), // 如果有的话
};

// 创建搜索客户端
let client = SearchClient::new(config);
```

### 创建索引并添加文档

```rust
use search::{SearchClient, IndexSettings};
use serde::{Deserialize, Serialize};

// 定义文档结构
#[derive(Debug, Serialize, Deserialize)]
struct Product {
    id: String,
    name: String,
    description: String,
    price: f64,
}

// 创建索引设置
let settings = IndexSettings {
    primary_key: Some("id".to_string()),
    searchable_attributes: Some(vec![
        "name".to_string(),
        "description".to_string(),
    ]),
    filterable_attributes: Some(vec![
        "price".to_string(),
    ]),
    sortable_attributes: Some(vec![
        "price".to_string(),
    ]),
    displayed_attributes: None, // 显示所有字段
};

// 创建索引
let product_index = client.create_index::<Product>("products", Some(settings)).await?;

// 添加文档
let products = vec![
    Product {
        id: "1".to_string(),
        name: "智能手机".to_string(),
        description: "高性能5G智能手机".to_string(),
        price: 4999.0,
    },
    // 更多产品...
];

let task = product_index.add_documents(products, None).await?;
```

### 执行搜索

```rust
use search::{SearchOptions, BasicSearchService, SearchService};

// 创建搜索服务
let search_service = BasicSearchService::new(product_index);

// 基础搜索
let options = SearchOptions {
    query: "智能".to_string(),
    limit: Some(10),
    offset: Some(0),
    ..Default::default()
};

// 执行搜索
let results = search_service.search(&options).await?;

// 打印搜索结果
println!("搜索结果：共找到 {} 条匹配", results.total_hits);
for product in results.hits {
    println!("{} - ¥{:.2}", product.name, product.price);
}
```

### 使用过滤器

```rust
// 过滤价格低于1000的产品
let filter_options = SearchOptions {
    query: "".to_string(),
    filter: Some("price < 1000".to_string()),
    ..Default::default()
};

let filter_results = search_service.search(&filter_options).await?;
```

## 错误处理

该库提供了自定义的错误类型 `SearchError`，用于处理各种可能出现的错误情况：

```rust
#[derive(Debug, Error)]
pub enum SearchError {
    #[error("搜索客户端错误: {0}")]
    ClientError(String),
    
    #[error("索引错误: {0}")]
    IndexError(String),
    
    #[error("文档错误: {0}")]
    DocumentError(String),
    
    #[error("序列化错误: {0}")]
    SerializationError(String),
    
    #[error("未知错误: {0}")]
    UnknownError(String),
}
```

## 完整示例

可以参考 tests 目录下的 文件中的完整示例代码。 