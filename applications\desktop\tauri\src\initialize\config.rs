use crate::config::config::ApplicationConfig;
use crate::config::option::Opt;
use clap::Parser;
use mf_state::init_logging;
use shared::ContextHelper;
use tokio::fs::read_to_string;
pub async fn init_config() -> anyhow::Result<()> {
    let _ = build_config().await;
    let _ = init_logger();
    Ok(())
}

fn init_logger() -> anyhow::Result<()> {
    let ref_app_config = ContextHelper::get::<ApplicationConfig>();
    init_logging(ref_app_config.log().log_level(), Some(ref_app_config.log().log_dir()))?;
    Ok(())
}

async fn build_config() -> anyhow::Result<()> {
    let opt = Opt::parse();

    // 确定配置文件路径
    let config_path = if opt.config_path.is_empty() {
        #[cfg(debug_assertions)]
        {
            "./desktop-tauri/tauri/config.toml"
        }
        #[cfg(not(debug_assertions))]
        {
            "./config.toml"
        }
    } else {
        opt.config_path.as_str()
    };

    // 读取并解析配置文件
    let content = read_to_string(config_path).await.map_err(|e| anyhow::anyhow!("Failed to read config file at {}: {}", config_path, e))?;

    let config = ApplicationConfig::from_toml(&content);
    ContextHelper::set(config);
    Ok(())
}
