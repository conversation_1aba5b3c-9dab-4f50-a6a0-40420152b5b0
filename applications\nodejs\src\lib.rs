use std::collections::HashMap;
use std::sync::Arc;

use axum::Router;
use axum::http::{Request, HeaderValue, HeaderName};
use axum::body::Body;
use mf_core::event::EventHandler;
use mf_core::{ForgeError, ForgeResult};
use napi::threadsafe_function::ThreadSafeCallContext;
use price_web::app::AppBuilder;
use serde::{de, Deserialize, Serialize};
use serde_json::json;
use serde_json::Value;
use tower::{Service};
use napi::{Result, bindgen_prelude::*, JsObject};
use napi_derive::napi;
use napi::threadsafe_function::ThreadsafeFunction;
use napi::threadsafe_function::ThreadsafeFunctionCallMode;

#[napi]
#[derive(Debug, Serialize, Deserialize)]
pub struct PriceRequest {
    pub path: String,
    pub headers: Option<HashMap<String, String>>,
    pub body: Value,
}

impl FromNapiValue for PriceRequest {
    unsafe fn from_napi_value(env: napi::sys::napi_env, napi_val: napi::sys::napi_value) -> napi::Result<Self> {
        let obj = JsObject::from_napi_value(env, napi_val)?;
        let path: String = obj.get_named_property("path")?;
        let headers: Option<HashMap<String, String>> = obj.get_named_property("headers")?;
        let body_obj: JsObject = obj.get_named_property("body")?;
        let body_str: String = body_obj.coerce_to_string()?.into_utf8()?.as_str()?.to_string();
        let body: Value = serde_json::from_str(&body_str).unwrap_or(Value::Null);
        
        Ok(Self {
            path,
            headers,
            body,
        })
    }
}

impl PriceRequest {
    pub fn into_request(self) -> Request<Body> {
        let mut builder = Request::builder()
            .uri(format!("/{}", self.path))
            .header("Content-Type", "application/json");

        if let Some(headers) = self.headers {
            for (name, value) in headers {
                if let (Ok(name), Ok(value)) = (name.parse::<HeaderName>(), value.parse::<HeaderValue>()) {
                    builder = builder.header(name, value);
                }
            }
        }

        let body_str = serde_json::to_string(&self.body).unwrap_or_default();
        
        builder
            .body(Body::from(body_str))
            .unwrap()
    }
}

#[napi]
#[derive(Debug, Serialize, Deserialize)]
pub struct PriceResponse {
    pub status: u16,
    pub headers: HashMap<String, String>,
    pub body: Value,
}

struct Api {
    router_map: dashmap::DashMap<String, Router>,
}

impl Api {
    pub fn new() -> Self {
        //构建api
       let router_map = dashmap::DashMap::new();
        let app = AppBuilder::new().next_map(router_map.clone().into()).build();
        let service = app.app.clone();
        tokio::task::spawn(async move {
            app.run().await;
        });
        router_map.insert("defalut".to_string(), service);
        Self { router_map }
    }

    pub async fn req(&self, request: PriceRequest) -> PriceResponse {
        match self.router_map.get("defalut") {
            Some(router) => {
                let http_request = request.into_request();
                let res = router.value().clone().call(http_request).await.unwrap();
                
                let status = res.status().as_u16();
                
                let mut headers = HashMap::new();
                for (name, value) in res.headers() {
                    if let (Ok(name), Ok(value)) = (name.to_string().parse::<String>(), value.to_str().map(|s| s.to_string())) {
                        headers.insert(name, value);
                    }
                }

                let body_bytes = axum::body::to_bytes(res.into_body(), usize::MAX).await.unwrap();
                let body = serde_json::from_slice(&body_bytes).unwrap_or(Value::Null);

                PriceResponse {
                    status,
                    headers,
                    body,
                }
            },
            None => todo!(),
        }
    }
}

#[napi]
pub struct JsApi(Arc<Api>);

#[napi]
impl JsApi {
    #[napi(constructor)]
    pub fn new() -> Self {
        let api = Api::new();
        Self(Arc::new(api))
    }

    #[napi]
    pub async fn req(&self, request: PriceRequest) -> Result<PriceResponse> {
        let response = self.0.req(request).await;
        Ok(response)
    }
}
use shared::event::Event;

#[napi]
pub struct NodeEventHandler { 
    js_fn: ThreadsafeFunction<serde_json::Value>,
}

impl NodeEventHandler {
    pub fn new(js_fn: JsFunction) -> napi::Result<Self> {
        let tsfn = js_fn.create_threadsafe_function(0, |ctx: ThreadSafeCallContext<serde_json::Value>| {
            Ok(vec![ctx.value])
        })?;
        Ok(Self { js_fn: tsfn })
    }
}

use std::fmt::{self, Debug};

impl Debug for NodeEventHandler {
    fn fmt(
        &self,
        f: &mut fmt::Formatter<'_>,
    ) -> fmt::Result {
        write!(f, "NodeEventHandler")
    }
}
#[async_trait::async_trait]
impl EventHandler<Event> for NodeEventHandler {
    async fn handle(&self, event: &Event) -> ForgeResult<()> {
        let event_json = serde_json::json!({"key": event.0, "value": event.1});
        match self.js_fn.call(Ok(event_json), ThreadsafeFunctionCallMode::Blocking) {
            Status::Ok => Ok(()),
            Status::InvalidArg => Err(ForgeError::Other(anyhow::anyhow!("未知的错误"))),
            Status::ObjectExpected => Err(ForgeError::Other(anyhow::anyhow!("Object expected"))),
            Status::StringExpected => Err(ForgeError::Other(anyhow::anyhow!("String expected"))),
            Status::NameExpected => Err(ForgeError::Other(anyhow::anyhow!("Name expected"))),
            Status::FunctionExpected => Err(ForgeError::Other(anyhow::anyhow!("Function expected"))),
            Status::NumberExpected => Err(ForgeError::Other(anyhow::anyhow!("Number expected"))),
            Status::BooleanExpected => Err(ForgeError::Other(anyhow::anyhow!("Boolean expected"))),
            _ => Err(ForgeError::Other(anyhow::anyhow!("Unknown error occurred"))),
        }   
    }
}

