/*
CREATE TABLE "base_list_de_standard" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "name" varchar,
  "area_id" varchar,
  "type" varchar,
  "release_year" varchar,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ( 'A' ),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
"description" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseListDeStandard {
    pub sequence_nbr: String,
    pub name: Option<String>,
    pub area_id: Option<String>,
    pub r#type: Option<String>,
    pub release_year: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(BaseListDeStandard {}, "base_list_de_standard");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec!["name".to_string(), "area_id".to_string(), "type".to_string(), "release_year".to_string()]),
        filterable_attributes: Some(vec!["name".to_string(), "area_id".to_string(), "type".to_string(), "release_year".to_string()]),
        sortable_attributes: Some(vec!["name".to_string(), "area_id".to_string(), "type".to_string(), "release_year".to_string()]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseListDeStandard>("base_list_de_standard", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseListDeStandard>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_list_de_standard").await?;
    let base_list_index = IndexClient::<BaseListDeStandard> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
