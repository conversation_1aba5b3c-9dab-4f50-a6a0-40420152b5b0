use aes_gcm::{
    aead::{Aead, KeyInit},
    Aes256Gcm, Key, Nonce,
};
use anyhow::{Result, anyhow};
use base64::{engine::general_purpose::STANDARD as BASE64, Engine};
use rand::{rngs::OsRng, RngCore};
use std::{fmt, fs, path::Path};

pub struct Crypto {
    cipher: Aes256Gcm,
}

impl fmt::Debug for Crypto {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("Crypto").finish()
    }
}

impl Crypto {
    /// 生成新的加密密钥并保存到文件
    pub fn generate_key_file(path: &Path) -> Result<Vec<u8>> {
        let mut key = [0u8; 32];
        OsRng.fill_bytes(&mut key);
        
        // 将密钥保存为 base64 格式
        let key_str = BASE64.encode(key);
        fs::write(path, key_str)?;
        
        Ok(key.to_vec())
    }

    /// 从文件加载加密密钥
    pub fn load_key_from_file(path: &Path) -> Result<Vec<u8>> {
        let key_str = fs::read_to_string(path)?;
        let key = BASE64.decode(key_str.trim())?;
        
        if key.len() != 32 {
            return Err(anyhow!("Invalid key length, expected 32 bytes"));
        }
        
        Ok(key)
    }

    pub fn new(key: &[u8]) -> Result<Self> {
        if key.len() != 32 {
            return Err(anyhow!("Key must be 32 bytes for AES-256"));
        }
        let cipher_key = Key::<Aes256Gcm>::from_slice(key);
        let cipher = Aes256Gcm::new(cipher_key);
        Ok(Self { cipher })
    }

    pub fn encrypt(&self, data: &[u8]) -> Result<String> {
        let mut nonce_bytes = [0u8; 12];
        OsRng.fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        let ciphertext = self.cipher.encrypt(nonce, data)
            .map_err(|e| anyhow!("Encryption failed: {}", e))?;
        
        // Combine nonce and ciphertext
        let mut result = Vec::with_capacity(nonce_bytes.len() + ciphertext.len());
        result.extend_from_slice(&nonce_bytes);
        result.extend_from_slice(&ciphertext);
        
        Ok(BASE64.encode(result))
    }

    pub fn decrypt(&self, encrypted_data: &str) -> Result<Vec<u8>> {
        let data = BASE64.decode(encrypted_data)?;
        
        if data.len() < 12 {
            return Err(anyhow!("Invalid encrypted data"));
        }

        let (nonce_bytes, ciphertext) = data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);
        
        let plaintext = self.cipher.decrypt(nonce, ciphertext)
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;
        Ok(plaintext)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    #[test]
    fn test_key_generation_and_loading() {
        let temp_file = NamedTempFile::new().unwrap();
        
        // 生成并保存密钥
        let key1 = Crypto::generate_key_file(temp_file.path()).unwrap();
        
        // 从文件加载密钥
        let key2 = Crypto::load_key_from_file(temp_file.path()).unwrap();
        
        assert_eq!(key1, key2);
    }

    #[test]
    fn test_encryption_decryption() {
        let key = [1u8; 32]; // 256-bit key
        let crypto = Crypto::new(&key).unwrap();
        
        let original_data = b"Hello, World!";
        let encrypted = crypto.encrypt(original_data).unwrap();
        println!("encrypted: {}", encrypted);
        let decrypted = crypto.decrypt(&encrypted).unwrap();
        assert_eq!(original_data, decrypted.as_slice());
    }
} 