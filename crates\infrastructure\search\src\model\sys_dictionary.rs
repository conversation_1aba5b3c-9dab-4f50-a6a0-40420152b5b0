/*
CREATE TABLE "sys_dictionary" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "dict_code" varchar,
  "entry_key" varchar,
  "entry_value" varchar,
  "order_num" varchar,
  "rec_user_code" varchar,
  "rec_date" varchar,
  "rec_status" varchar DEFAULT ( 'A' ),
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
"description" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct SysDictionary {
    pub sequence_nbr: String,
    pub dict_code: Option<String>,
    pub entry_key: Option<String>,
    pub entry_value: Option<String>,
    pub order_num: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(SysDictionary {}, "sys_dictionary");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec!["dict_code".to_string(), "entry_key".to_string(), "entry_value".to_string(), "order_num".to_string()]),
        filterable_attributes: Some(vec!["dict_code".to_string(), "entry_key".to_string(), "entry_value".to_string(), "order_num".to_string()]),
        sortable_attributes: Some(vec!["dict_code".to_string(), "entry_key".to_string(), "entry_value".to_string(), "order_num".to_string()]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<SysDictionary>("sys_dictionary", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<SysDictionary>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("sys_dictionary").await?;
    let base_list_index = IndexClient::<SysDictionary> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
