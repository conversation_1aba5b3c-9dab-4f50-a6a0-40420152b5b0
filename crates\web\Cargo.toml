[package]
name = "price-web"
version={workspace=true}
edition={workspace=true}
description="axum web 服务"

[dependencies]
axum = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
dashmap = { workspace = true }

tokio = { workspace = true } 
moduforge-model = { workspace = true }
moduforge-state = { workspace = true }
moduforge-transform = { workspace = true }
moduforge-core = { workspace = true }
moduforge-macros = { workspace = true }
