use rbatis::{crud, executor::Executor, impl_select, table_sync::SqliteTableMapper, <PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct State {
    pub id: i64,
    pub fields_instances: Vec<u8>, // Serialized fields_instances
    pub node_pool: Vec<u8>,
}

crud!(State {}, "states");
impl_select!(State{select_by_id(id:i64) -> Option => "`where id = #{id} limit 1`"});
pub async fn do_sync_table_state(conn: &dyn Executor) {
    let table = State { id: 0, fields_instances: "blob".to_string().into(), node_pool: "blob".to_string().into() };
    let _ = RBatis::sync(conn, &SqliteTableMapper {}, &table, "states").await;
}
