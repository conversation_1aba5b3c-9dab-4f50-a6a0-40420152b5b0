/*
CREATE TABLE "base_tax_reform_file_2022" (
  "sequence_nbr" text(20) NOT NULL,
  "taxation_method_type" integer(4),
  "taxation_method_name" text(10),
  "tax_reform_file_name" text(20),
  "output_tax_rate" real(16,6),
  "input_tax_rate" real(16,6),
  "simple_tax_rate" real(16,6),
  "area_id" text(20),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseTaxReformFile2022 {
    pub sequence_nbr: String,
    pub taxation_method_type: Option<i32>,
    pub taxation_method_name: Option<String>,
    pub tax_reform_file_name: Option<String>,
    pub output_tax_rate: Option<f64>,
    pub input_tax_rate: Option<f64>,
    pub simple_tax_rate: Option<f64>,
    pub area_id: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseTaxReformFile2022 {}, "base_tax_reform_file_2022");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "taxation_method_type".to_string(),
            "taxation_method_name".to_string(),
            "tax_reform_file_name".to_string(),
            "output_tax_rate".to_string(),
            "input_tax_rate".to_string(),
            "simple_tax_rate".to_string(),
            "area_id".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "taxation_method_type".to_string(),
            "taxation_method_name".to_string(),
            "tax_reform_file_name".to_string(),
            "output_tax_rate".to_string(),
            "input_tax_rate".to_string(),
            "simple_tax_rate".to_string(),
            "area_id".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "taxation_method_type".to_string(),
            "taxation_method_name".to_string(),
            "tax_reform_file_name".to_string(),
            "output_tax_rate".to_string(),
            "input_tax_rate".to_string(),
            "simple_tax_rate".to_string(),
            "area_id".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BaseTaxReformFile2022>("base_tax_reform_file_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseTaxReformFile2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_tax_reform_file_2022").await?;
    let base_list_index = IndexClient::<BaseTaxReformFile2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
