/*
CREATE TABLE "base_de_relation_variable_coefficient_2022" (
  "id" text(11) NOT NULL,
  "groupid" text(11),
  "variable_name" text(255),
  "variable_code" text(255),
  "formula" text(255),
  "default" text(255),
  "value" text(255),
  "de_effect_range" text,
  "if_editable" integer(11),
  "remark" text(255),
  PRIMARY KEY ("id")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeRelationVariableCoefficient2022 {
    pub id: String,
    pub groupid: Option<String>,
    pub variable_name: Option<String>,
    pub variable_code: Option<String>,
    pub formula: Option<String>,
    pub default: Option<String>,
    pub value: Option<String>,
    pub de_effect_range: Option<String>,
    pub if_editable: Option<i32>,
    pub remark: Option<String>,
}
//crud!(BaseDeRelationVariableCoefficient2022 {}, "base_de_relation_variable_coefficient_2022");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("id".to_string()),
        searchable_attributes: Some(vec![
            "groupid".to_string(),
            "variable_name".to_string(),
            "variable_code".to_string(),
            "formula".to_string(),
            "default".to_string(),
            "value".to_string(),
            "de_effect_range".to_string(),
            "remark".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "groupid".to_string(),
            "variable_name".to_string(),
            "variable_code".to_string(),
            "formula".to_string(),
            "default".to_string(),
            "value".to_string(),
            "de_effect_range".to_string(),
            "if_editable".to_string(),
            "remark".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "groupid".to_string(),
            "variable_name".to_string(),
            "variable_code".to_string(),
            "formula".to_string(),
            "default".to_string(),
            "value".to_string(),
            "de_effect_range".to_string(),
            "if_editable".to_string(),
            "remark".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeRelationVariableCoefficient2022>("base_de_relation_variable_coefficient_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeRelationVariableCoefficient2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_relation_variable_coefficient_2022").await?;
    let base_de_relation_variable_coefficient_2022_index =
        IndexClient::<BaseDeRelationVariableCoefficient2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_relation_variable_coefficient_2022_index);
    Ok(search_service)
}
