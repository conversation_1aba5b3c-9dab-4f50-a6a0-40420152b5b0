/*
CREATE TABLE "base_clpb" (
  "sequence_nbr" text(20) NOT NULL,
  "rcj_id" text(64) NOT NULL,
  "library_code" text(255),
  "pb_code" text(255),
  "pb_name" text(255),
  "material_code" text(50),
  "kind" integer(255),
  "material_name" text(100),
  "specification" text(500),
  "unit" text(32),
  "res_qty" real(16,6),
  "de_price" real(16,6),
  "market_price" real(16,6),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  "tax_removal" real(16,6),
  "kind_sc" text(255),
  "transfer_factor" text(255),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseClpb {
    pub sequence_nbr: String,
    pub rcj_id: String,
    pub library_code: Option<String>,
    pub pb_code: Option<String>,
    pub pb_name: Option<String>,
    pub material_code: Option<String>,
    pub kind: Option<i32>,
    pub material_name: Option<String>,
    pub specification: Option<String>,
    pub unit: Option<String>,
    pub res_qty: Option<f64>,
    pub de_price: Option<f64>,
    pub market_price: Option<f64>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
    pub tax_removal: Option<f64>,
    pub kind_sc: Option<String>,
    pub transfer_factor: Option<String>,
}
//crud!(BaseClpb {}, "base_clpb");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "pb_code".to_string(),
            "pb_name".to_string(),
            "material_code".to_string(),
            "kind".to_string(),
            "material_name".to_string(),
            "specification".to_string(),
            "unit".to_string(),
            "res_qty".to_string(),
            "de_price".to_string(),
            "market_price".to_string(),
            "rec_user_code".to_string(),
            "rec_date".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "tax_removal".to_string(),
            "kind_sc".to_string(),
            "transfer_factor".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "pb_code".to_string(),
            "pb_name".to_string(),
            "material_code".to_string(),
            "kind".to_string(),
            "material_name".to_string(),
            "specification".to_string(),
            "unit".to_string(),
            "res_qty".to_string(),
            "de_price".to_string(),
            "market_price".to_string(),
            "rec_user_code".to_string(),
            "rec_date".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "tax_removal".to_string(),
            "kind_sc".to_string(),
            "transfer_factor".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "pb_code".to_string(),
            "pb_name".to_string(),
            "material_code".to_string(),
            "kind".to_string(),
            "material_name".to_string(),
            "specification".to_string(),
            "unit".to_string(),
            "res_qty".to_string(),
            "de_price".to_string(),
            "market_price".to_string(),
            "rec_user_code".to_string(),
            "rec_date".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "tax_removal".to_string(),
            "kind_sc".to_string(),
            "transfer_factor".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseClpb>("base_clpb", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseClpb>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_clpb").await?;
    let base_clpb_index = IndexClient::<BaseClpb> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_clpb_index);
    Ok(search_service)
}
