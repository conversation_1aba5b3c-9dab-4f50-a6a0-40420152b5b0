<?xml version="1.0" encoding="UTF-8"?>
<!-- 人材机Schema文件 -->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://pricing-dev.oss-cn-hangzhou.aliyuncs.com/moduforge/moduforge-schema.xsd">
    <nodes>
        <node name="rcj" desc="人材机明细" content="">
            <attrs>
                <!-- 基本信息 -->
                <attr name="materialCode" default=""/>
                <attr name="standardId" default=""/>
                <attr name="materialName" default=""/>
                <attr name="specification" default=""/>
                <attr name="unit" default=""/>
                <attr name="unitId" default=""/>
                <attr name="kind" default="0"/>
                <attr name="libraryCode" default=""/>
                <attr name="type" default=""/>
                <attr name="constructId" default=""/>
                
                <!-- 材料属性 -->
                <attr name="ifDonorMaterial" default="0"/>
                <attr name="kindBackUp" default="0"/>
                <attr name="deId" default=""/>
                <attr name="resQty" default="0"/>
                <attr name="initResQty" default="0"/>
                <attr name="levelMark" default=""/>
                <attr name="totalNumber" default="0"/>
                <attr name="total" default="0"/>
                
                <!-- 明细和标记 -->
                <attr name="rcjDetailsDTOs" default=""/>
                <attr name="markSum" default="0"/>
                <attr name="referenceRecord" default=""/>
                <attr name="rcjFlag" default="0"/>
                
                <!-- 价格相关 -->
                <attr name="dePrice" default="0"/>
                <attr name="marketPrice" default="0"/>
                <attr name="priceBaseJournal" default="0"/>
                <attr name="priceBaseJournalTax" default="0"/>
                <attr name="priceMarket" default="0"/>
                <attr name="priceMarketTax" default="0"/>
                <attr name="priceMarketFormula" default="0"/>
                <attr name="priceMarketTaxFormula" default="0"/>
                <attr name="marketPriceFormula" default="0"/>
                <attr name="taxRate" default="0"/>
                
                <!-- 状态控制 -->
                <attr name="isFyrcj" default="0"/>
                <attr name="isChangeAva" default="0"/>
                <attr name="isLock" default="false"/>
                <attr name="kindSc" default=""/>
                <attr name="transferFactor" default=""/>
                <attr name="formattedCoefficient" default="0"/>
                
                <!-- 等级分类 -->
                <attr name="level1" default=""/>
                <attr name="level2" default=""/>
                <attr name="level3" default=""/>
                <attr name="level4" default=""/>
                <attr name="level5" default=""/>
                <attr name="level6" default=""/>
                <attr name="level7" default=""/>
                
                <!-- 编码管理 -->
                <attr name="agencyCode" default=""/>
                <attr name="productCode" default=""/>
                <attr name="sortNo" default="0"/>
                <attr name="isDataTaxRate" default=""/>
                <attr name="singleId" default=""/>
                <attr name="initMaterialCode" default=""/>
            </attrs>
        </node>
    </nodes>
</schema>