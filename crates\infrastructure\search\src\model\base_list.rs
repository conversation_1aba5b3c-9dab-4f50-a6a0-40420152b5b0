/*
CREATE TABLE "base_list" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "library_code" varchar,
  "library_name" varchar,
  "bd_code_level01" varchar,
  "bd_name_level01" varchar,
  "bd_code_level02" varchar,
  "bd_name_level02" varchar,
  "bd_code_level03" varchar,
  "bd_name_level03" varchar,
  "bd_code_level04" varchar,
  "bd_name_level04" varchar,
  "unit" varchar,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ('A'),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "zjcs_label_name" varchar,
  "zjcs_class_code" varchar,
  "zjcs_class_name" varchar,
  "description" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};
#[derive(Debug, Serialize, Deserialize)]
pub struct BaseList {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub library_name: Option<String>,
    pub bd_code_level01: Option<String>,
    pub bd_name_level01: Option<String>,
    pub bd_code_level02: Option<String>,
    pub bd_name_level02: Option<String>,
    pub bd_code_level03: Option<String>,
    pub bd_name_level03: Option<String>,
    pub bd_code_level04: Option<String>,
    pub bd_name_level04: Option<String>,
    pub unit: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub zjcs_label_name: Option<String>,
    pub zjcs_class_code: Option<String>,
    pub zjcs_class_name: Option<String>,
    pub description: Option<String>,
}
//crud!(BaseList {}, "base_list");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "bd_code_level01".to_string(),
            "bd_name_level01".to_string(),
            "bd_code_level02".to_string(),
            "bd_name_level02".to_string(),
            "bd_code_level03".to_string(),
            "bd_name_level03".to_string(),
            "bd_code_level04".to_string(),
            "bd_name_level04".to_string(),
            "unit".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "zjcs_label_name".to_string(),
            "zjcs_class_code".to_string(),
            "zjcs_class_name".to_string(),
            "description".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "bd_code_level01".to_string(),
            "bd_name_level01".to_string(),
            "bd_code_level02".to_string(),
            "bd_name_level02".to_string(),
            "bd_code_level03".to_string(),
            "bd_name_level03".to_string(),
            "bd_code_level04".to_string(),
            "bd_name_level04".to_string(),
            "unit".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "zjcs_label_name".to_string(),
            "zjcs_class_code".to_string(),
            "zjcs_class_name".to_string(),
            "description".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "bd_code_level01".to_string(),
            "bd_name_level01".to_string(),
            "bd_code_level02".to_string(),
            "bd_name_level02".to_string(),
            "bd_code_level03".to_string(),
            "bd_name_level03".to_string(),
            "bd_code_level04".to_string(),
            "bd_name_level04".to_string(),
            "unit".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "zjcs_label_name".to_string(),
            "zjcs_class_code".to_string(),
            "zjcs_class_name".to_string(),
            "description".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseList>("base_list", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseList>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_list").await?;
    let base_list_index = IndexClient::<BaseList> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
