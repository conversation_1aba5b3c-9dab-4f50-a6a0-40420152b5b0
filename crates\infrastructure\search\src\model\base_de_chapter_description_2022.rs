/*
CREATE TABLE "base_de_chapter_description_2022" (
  "sequence_nbr" text(20) NOT NULL,
  "gznr_oss_url" text(255),
  "zjsm_oss_url" text(255),
  "jsgz_oss_url" text(255),
  "library_code" text(50),
  "type_name" text(255),
  "classify_level1" text(255),
  "classify_level2" text(255),
  "classify_level3" text(255),
  "classify_level4" text(255),
  "type" integer(4),
  "level" integer(4),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64)
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeChapterDescription2022 {
    pub sequence_nbr: String,
    pub gznr_oss_url: Option<String>,
    pub zjsm_oss_url: Option<String>,
    pub jsgz_oss_url: Option<String>,
    pub library_code: Option<String>,
    pub type_name: Option<String>,
    pub classify_level1: Option<String>,
    pub classify_level2: Option<String>,
    pub classify_level3: Option<String>,
    pub classify_level4: Option<String>,
    pub r#type: Option<i32>,
    pub level: Option<i32>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseDeChapterDescription2022 {}, "base_de_chapter_description_2022");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "gznr_oss_url".to_string(),
            "zjsm_oss_url".to_string(),
            "jsgz_oss_url".to_string(),
            "library_code".to_string(),
            "type_name".to_string(),
            "classify_level1".to_string(),
            "classify_level2".to_string(),
            "classify_level3".to_string(),
            "classify_level4".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "gznr_oss_url".to_string(),
            "zjsm_oss_url".to_string(),
            "jsgz_oss_url".to_string(),
            "library_code".to_string(),
            "type_name".to_string(),
            "classify_level1".to_string(),
            "classify_level2".to_string(),
            "classify_level3".to_string(),
            "classify_level4".to_string(),
            "type".to_string(),
            "level".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "gznr_oss_url".to_string(),
            "zjsm_oss_url".to_string(),
            "jsgz_oss_url".to_string(),
            "library_code".to_string(),
            "type_name".to_string(),
            "classify_level1".to_string(),
            "classify_level2".to_string(),
            "classify_level3".to_string(),
            "classify_level4".to_string(),
            "type".to_string(),
            "level".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeChapterDescription2022>("base_de_chapter_description_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeChapterDescription2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_chapter_description_2022").await?;
    let base_de_chapter_description_2022_index = IndexClient::<BaseDeChapterDescription2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_chapter_description_2022_index);
    Ok(search_service)
}
