// SQLite 存储模块
mod entity;

use std::{
    fs::{self, create_dir_all, File},
    io::{Read, Cursor},
    path::{Path, PathBuf},
    sync::{
        atomic::{AtomicUsize, Ordering},
        Arc,
    },
};

use crate::{
    step_factory::{StepFactory, StepFactoryRegistry},
    StorageOptions, StorageResult, StorageTrait,
};
use anyhow::Result;
use async_trait::async_trait;
use entity::{
    resource::{self, Resource},
    state::{self, State},
    transaction,
};
use mf_core::error_utils::storage_error;
use mf_model::id_generator::IdGenerator;
use mf_state::{info, state::StateSerialize, Configuration, State as CoreState, Transaction};

use rbatis::RBatis;
use serde::{Deserialize, Serialize};
use serde_json;
use zip::write::ExtendedFileOptions;
use crate::crypto::Crypto;

#[derive(Debug, Serialize, Deserialize)]
struct TypeWrapper {
    type_id: String,
    data: Vec<u8>,
}

/// SQLite 存储配置结构体
#[derive(Debug, Clone)]
pub struct SqliteConfig {
    /// 数据库文件路径
    pub path: String,
    /// 是否启用 WAL 模式 - Write Ahead Logging，用于提高写入性能
    pub enable_wal: bool,
    /// 是否启用外键约束，用于维护数据完整性
    pub enable_foreign_keys: bool,
}

/// 为 SqliteConfig 实现默认值
impl Default for SqliteConfig {
    fn default() -> Self {
        Self { path: "db.sqlite".to_string(), enable_wal: true, enable_foreign_keys: true }
    }
}

/// SQLite 存储实现结构体
#[derive(Debug)]
pub struct SqliteStorage {
    /// 数据库连接
    conn: Option<RBatis>,
    /// 操作计数器，用于控制快照频率
    counter: AtomicUsize,
    /// 快照间隔，每隔多少次操作保存一次完整状态
    snapshot_interval: usize,
    options: StorageOptions,
    step_factory: StepFactoryRegistry,
    /// 加密工具
    crypto: Option<Crypto>,
}

impl SqliteStorage {
    pub fn register(
        &mut self,
        type_id: &str,
        factory: Arc<dyn StepFactory>,
    ) {
        self.step_factory.register(type_id, factory);
    }

    /// 确保数据库表存在
    async fn ensure_table(&self) -> Result<()> {
        if let Some(conn) = &self.conn {
            resource::do_sync_table_resource(conn).await;
            state::do_sync_table_state(conn).await;
            transaction::do_sync_table_transaction(conn).await;
        }
        Ok(())
    }

    /// 初始化 SQLite 存储
    pub async fn new(options: StorageOptions) -> StorageResult<Self> {
        // 创建存储实例根目录
        let root_dir = options.get_root_dir();
        std::fs::create_dir_all(&root_dir).map_err(|e| storage_error(e.to_string()))?;

        // 创建资源目录
        std::fs::create_dir_all(options.get_resource_dir()).map_err(|e| storage_error(e.to_string()))?;

        // 创建配置
        let config = SqliteConfig { path: options.get_db_path().to_string_lossy().to_string(), ..Default::default() };

        let mut url = format!("sqlite://{}", config.path);

        // 配置 WAL 模式和外键约束
        if config.enable_wal {
            url.push_str("?mode=rwc");
        }
        let rb = RBatis::new();
        rb.init(rbdc_sqlite::Driver {}, &format!("sqlite://{}", config.path)).unwrap();

        let crypto = if let Some(key) = options.encryption_key.clone() {
            Some(Crypto::new(&key).map_err(|e| storage_error(e.to_string()))?)
        } else {
            None
        };

        let s: SqliteStorage =
            Self { conn: Some(rb), counter: AtomicUsize::new(1), snapshot_interval: 50, options, step_factory: StepFactoryRegistry::new(), crypto };
        // 确保必要的表已创建
        s.ensure_table().await.map_err(|e| storage_error(e.to_string()))?;
        Ok(s)
    }

    /// 获取资源存储目录
    fn get_resource_dir(&self) -> Result<PathBuf> {
        Ok(self.options.get_resource_dir())
    }

    /// 获取资源文件
    pub async fn get_resource(
        &self,
        id: i64,
    ) -> StorageResult<(Vec<u8>, String)> {
        if let Some(conn) = &self.conn {
            let resource = Resource::select_by_id(conn, id).await.map_err(|e| storage_error(e.to_string()))?.unwrap();
            let mut file = File::open(&resource.path).map_err(|e| storage_error(e.to_string()))?;
            let mut data = Vec::new();
            file.read_to_end(&mut data).map_err(|e| storage_error(e.to_string()))?;

            Ok((data, resource.mime_type))
        } else {
            Err(storage_error("Database connection not available"))
        }
    }

    /// 删除资源文件
    pub async fn delete_resource(
        &self,
        id: i64,
    ) -> StorageResult<()> {
        if let Some(conn) = &self.conn {
            let resource = Resource::select_by_id(conn, id).await.map_err(|e| storage_error(e.to_string()))?.unwrap();
            // 删除物理文件
            std::fs::remove_file(&resource.path).map_err(|e| storage_error(e.to_string()))?;
            let _ = Resource::delete_by_column(conn, "id", id).await;

            Ok(())
        } else {
            Err(storage_error("Database connection not available"))
        }
    }

    /// 获取资源信息
    pub async fn get_resource_info(
        &self,
        id: i64,
    ) -> StorageResult<Resource> {
        if let Some(conn) = &self.conn {
            let resource = Resource::select_by_id(conn, id).await.map_err(|e| storage_error(e.to_string()))?.unwrap();
            Ok(resource)
        } else {
            Err(storage_error("Database connection not available"))
        }
    }

    /// 导出所有数据为zip文件
    pub async fn export_to_zip(
        &self,
        output_path: &Path,
    ) -> StorageResult<()> {
        use std::fs::File;
        use std::io::Write;
        use zip::{write::FileOptions, ZipWriter};
        // 创建zip文件
        let file = File::create(output_path).map_err(|e| storage_error(e.to_string()))?;
        let mut zip = ZipWriter::new(file);
        let options = FileOptions::<ExtendedFileOptions>::default().compression_method(zip::CompressionMethod::Deflated).unix_permissions(0o755);

        // 添加数据库文件
        let db_path = self.options.get_db_path();
        let db_name = db_path
            .file_name()
            .ok_or_else(|| storage_error("Invalid database path".to_string()))
            .map_err(|e| storage_error(e.to_string()))?
            .to_string_lossy();
        zip.start_file(&db_name, options.clone()).map_err(|e| storage_error(e.to_string()))?;
        let db_content = std::fs::read(&db_path).map_err(|e| storage_error(e.to_string()))?;
        zip.write_all(&db_content).map_err(|e| storage_error(e.to_string()))?;

        // 添加资源文件
        let resource_dir = self.get_resource_dir().map_err(|e| storage_error(e.to_string()))?;
        if resource_dir.exists() {
            for entry in std::fs::read_dir(&resource_dir).map_err(|e| storage_error(e.to_string()))? {
                let entry = entry.map_err(|e| storage_error(e.to_string()))?;
                let path = entry.path();
                if path.is_file() {
                    let file_name = path
                        .file_name()
                        .ok_or_else(|| storage_error("Invalid file name".to_string()))
                        .map_err(|e| storage_error(e.to_string()))?
                        .to_string_lossy();
                    let zip_path = format!("resources/{}", file_name);
                    zip.start_file(&zip_path, options.clone()).map_err(|e| storage_error(e.to_string()))?;
                    let content = std::fs::read(&path).map_err(|e| storage_error(e.to_string()))?;
                    zip.write_all(&content).map_err(|e| storage_error(e.to_string()))?;
                }
            }
        }

        zip.finish().map_err(|e| storage_error(e.to_string()))?;
        Ok(())
    }

    /// 从zip文件导入数据
    pub async fn import_from_zip(
        &self,
        zip_path: &Path,
    ) -> StorageResult<()> {
        use std::io::Read;
        use zip::ZipArchive;
        let file = File::open(zip_path).map_err(|e| storage_error(e.to_string()))?;
        let mut archive = ZipArchive::new(file).map_err(|e| storage_error(e.to_string()))?;

        // 导入数据库文件
        let db_path = self.options.get_db_path();
        let db_name = db_path
            .file_name()
            .ok_or_else(|| Box::new(storage_error("Invalid database path".to_string())))
            .map_err(|e| storage_error(e.to_string()))?
            .to_string_lossy();

        if let Ok(mut db_file) = archive.by_name(&db_name) {
            let mut contents = Vec::new();
            db_file.read_to_end(&mut contents).map_err(|e| storage_error(e.to_string()))?;
            std::fs::write(&db_path, contents).map_err(|e| storage_error(e.to_string()))?;
        }

        // 导入资源文件
        let resource_dir = self.get_resource_dir().map_err(|e| storage_error(e.to_string()))?;
        create_dir_all(&resource_dir).map_err(|e| storage_error(e.to_string()))?;

        for i in 0..archive.len() {
            let mut file = archive.by_index(i).map_err(|e| storage_error(e.to_string()))?;
            let file_path = file.name();

            if file_path.starts_with("resources/") {
                let file_name = file_path.trim_start_matches("resources/");
                let output_path = resource_dir.join(file_name);

                let mut contents = Vec::new();
                file.read_to_end(&mut contents).map_err(|e| storage_error(e.to_string()))?;
                std::fs::write(output_path, contents).map_err(|e| storage_error(e.to_string()))?;
            }
        }
        Ok(())
    }

    pub async fn copy_file_to_resource(
        target_dir: PathBuf,
        path: String,
        new_name: Option<String>,
    ) -> StorageResult<PathBuf> {
        // 获取文件名称
        let path = Path::new(&path);
        let file_name = path.file_name().unwrap().to_str().unwrap();
        // 获取目标目录
        //let target_dir = &self.options.get_resource_dir();
        // 使用tokio异步拷贝文件到指定目录
        let res = tokio::fs::copy(path, &target_dir).await.unwrap();
        info!("res: {:?}", res);
        let mut new_file = target_dir.join(file_name);
        // 检查拷贝后的文件是否存在
        if !&new_file.exists() {
            return Err(storage_error("File copy failed".to_string()));
        }

        // 给复制后的文件重新命名
        new_file.set_file_name(new_name.unwrap_or(IdGenerator::get_id().to_string()));

        Ok(new_file)
    }

    /// 加密数据
    fn encrypt_data(&self, data: &[u8]) -> StorageResult<Vec<u8>> {
        if let Some(crypto) = &self.crypto {
            let encrypted = crypto.encrypt(data).map_err(|e| storage_error(e.to_string()))?;
            let encrypted_bytes = encrypted.as_bytes();
            let encoded = zstd::encode_all(encrypted_bytes, 3).map_err(|e| storage_error(e.to_string()))?;
            Ok(encoded)
        } else {
            let encoded = zstd::encode_all(data, 3).map_err(|e| storage_error(e.to_string()))?;
            Ok(encoded)
        }
    }

    /// 解密数据
    fn decrypt_data(&self, data: &[u8]) -> StorageResult<Vec<u8>> {
        if let Some(crypto) = &self.crypto {
            // 先解压 zstd 数据
            let decoded = zstd::decode_all(Cursor::new(data)).map_err(|e| storage_error(e.to_string()))?;
            // 再解密
            let decrypted = crypto.decrypt(std::str::from_utf8(&decoded)
                .map_err(|e| storage_error(e.to_string()))?)
                .map_err(|e| storage_error(e.to_string()))?;
            Ok(decrypted)
        } else {
            let decoded = zstd::decode_all(Cursor::new(data)).map_err(|e| storage_error(e.to_string()))?;
            Ok(decoded)
        }
    }
}

#[async_trait]
impl StorageTrait for SqliteStorage {
    /// 保存状态到数据库
    async fn save_state(
        &self,
        state: &Arc<CoreState>,
    ) -> StorageResult<()> {
        let root_id = state.version;
        // 收集插件状态
        let state_serialize = state.serialize().await.map_err(|e| storage_error(e.to_string()))?;
        // 加密数据
        let encrypted_node_pool = self.encrypt_data(&state_serialize.node_pool.as_slice())?;
        let encrypted_state_field = self.encrypt_data(&state_serialize.state_fields.as_slice())?;

        // 保存到数据库
        if let Some(conn) = &self.conn {
            // 再删除状态数据
            let _ = State::delete_by_column(conn, "id", root_id as i64).await;
            let model = State { id: root_id as i64, node_pool: encrypted_node_pool, fields_instances: encrypted_state_field };
            let _ = State::insert(conn, &model).await;
        }
        Ok(())
    }

    /// 保存事务到数据库
    async fn save_transaction(
        &self,
        old_id: u64,
        trs: &Arc<Vec<Transaction>>,
        new_state: &Arc<CoreState>,
    ) -> StorageResult<()> {
        // 序列化并压缩事务步骤
        //收集所有事务的步骤
        let steps: Vec<TypeWrapper> =
            trs.iter().flat_map(|tr| tr.steps.iter().map(|item| TypeWrapper { type_id: item.name(), data: item.serialize().unwrap() })).collect();
        let steps_str = serde_json::to_string(&steps).map_err(|e| storage_error(e.to_string()))?;
        let steps_field = zstd::encode_all(steps_str.as_bytes(), 3).map_err(|e| storage_error(e.to_string()))?;

        if let Some(conn) = &self.conn {
            // 更新计数器
            let count = self.counter.fetch_add(trs.len(), Ordering::SeqCst) + 1;

            // 每隔 snapshot_interval 次操作保存一次完整状态
            if count % self.snapshot_interval == 0 {
                // 先保存状态
                self.save_state(new_state).await?;
                // 再清空事务表
                let _ = transaction::Transaction::delete_by_column(conn, "state_id", old_id as i64).await;
            } else {
                // 检查状态是否存在，不存在则先保存
                let state = State::select_by_id(conn, new_state.version as i64).await.map_err(|e| storage_error(e.to_string()))?;
                if state.is_none() {
                    self.save_state(new_state).await?;
                }

                // 保存事务
                let model = transaction::Transaction { id: trs.last().unwrap().id as i64, state_id: old_id as i64, steps: steps_field };
                transaction::Transaction::insert(conn, &model).await.map_err(|e| storage_error(e.to_string()))?;
            }
        }
        Ok(())
    }

    /// 从数据库加载状态
    async fn load_state(
        &self,
        configuration: &Configuration,
    ) -> StorageResult<Arc<CoreState>> {
        if let Some(conn) = &self.conn {
            // 查找指定版本的状态
            let select_state = State::select_all(conn).await.map_err(|e| storage_error(e.to_string()))?;
            if let Some(state) = select_state.first() {
                let state_id = state.id;
                // 解密数据
                let node_pool = self.decrypt_data(&state.node_pool)?;
                let fields_instances = self.decrypt_data(&state.fields_instances)?;
                
                let state_serialize = StateSerialize {
                    node_pool: node_pool,
                    state_fields: fields_instances,
                };
                let mut state = CoreState::deserialize(&state_serialize, configuration).await.map_err(|e| storage_error(e.to_string()))?;
                //查找对应的transaction
                let transactions =
                    transaction::Transaction::select_by_column(conn, "state_id", state_id).await.map_err(|e| storage_error(e.to_string()))?;
                for transaction in transactions {
                    let steps_vec = zstd::decode_all(transaction.steps.as_slice()).map_err(|e| storage_error(e.to_string()))?;
                    let steps: Vec<TypeWrapper> = serde_json::from_slice(&steps_vec).map_err(|e| storage_error(e.to_string()))?;
                    let mut tr = Transaction::new(&state);
                    for s in steps.into_iter() {
                        let step = self.step_factory.create(&s.type_id, &s.data);
                        let _ = tr.step(step);
                    }
                    if let Ok(value) = state.apply(tr).await {
                        state = value.state;
                    }
                }
                Ok(Arc::new(state))
            } else {
                Err(storage_error("State not found".to_string()))
            }
        } else {
            Err(storage_error("Database not connected".to_string()))
        }
    }

    /// 导出数据到指定路径或默认路径
    ///
    /// # 参数
    /// - `path`: 一个可选的字符串参数，表示用户指定的导出路径如果未提供，则使用默认路径
    ///
    /// # 返回
    /// - `StorageResult<()>`: 表示导出操作成功或失败的异步结果如果操作成功，则返回Ok(())；
    ///   如果操作失败，则返回一个错误信息
    async fn export(
        &self,
        path: Option<String>,
    ) -> StorageResult<()> {
        // 初始化导出路径为默认路径，位于根目录的父目录下，并以实例ID命名
        let mut export_path = self.options.get_root_dir().parent().unwrap().join(format!("{}.ysf", self.options.id));

        // 如果用户提供了具体的导出路径，则使用该路径
        if path.is_some() {
            export_path = PathBuf::from(path.unwrap());
        }
        println!("导出路径：{:?}", &export_path);
        // 将数据导出到ZIP文件中
        self.export_to_zip(&export_path).await?;

        // 返回操作成功
        Ok(())
    }

    async fn import(
        &self,
        zip_path: &Path,
    ) -> StorageResult<()> {
        let _ = self.import_from_zip(zip_path).await;
        Ok(())
    }

    // 保存资源到数据库和指定的资源目录
    //
    // 该函数接收一个文件路径，检查文件是否存在，然后生成一个新的文件名，
    // 并将文件拷贝到资源目录中，同时将新文件的信息存储到数据库中
    //
    // 参数:
    // - path: &Path - 要保存的资源的文件路径
    //
    // 返回值:
    // - StorageResult<String> - 返回一个结果，包含新插入记录的ID作为字符串，或者一个错误
    async fn save_resource(
        &self,
        path: &Path,
    ) -> StorageResult<String> {
        let conn = self.conn.as_ref().ok_or_else(|| storage_error("Database connection not available".to_string()))?;
        // 检查文件是否存在
        if !path.exists() {
            return Err(storage_error("The file corresponding to the path does not exist".to_string()));
        }
        // 获取文件名称和扩展名
        let file_name = path.file_name().unwrap().to_str().unwrap();
        let extension = path.extension().and_then(|ext| ext.to_str()).unwrap_or("");
        // 获取目标目录
        let target_dir = &self.options.get_resource_dir();
        // 构建完整的目标文件路径
        let target_path = target_dir.join(file_name);
        // 使用tokio异步拷贝文件到指定文件(这个copy是把一个文件拷贝到另一个文件上，而不是拷贝到另一个文件夹中，target_path是一个文件的路径)
        tokio::fs::copy(path, &target_path).await.map_err(|e| storage_error(e.to_string()))?;

        // 检查拷贝后的文件是否存在
        if !target_path.exists() {
            return Err(storage_error("File copy failed".to_string()));
        }

        // 生成新的文件名（包含扩展名）
        let new_name = if !extension.is_empty() { format!("{}.{}", IdGenerator::get_id(), extension) } else { IdGenerator::get_id().to_string() };

        // 构建新的文件路径
        let new_file = target_dir.join(&new_name);

        // 实际执行文件重命名
        std::fs::rename(&target_path, &new_file).map_err(|e| storage_error(e.to_string()))?;

        // 文件元数据
        let file_meta_data = fs::metadata(&new_file).map_err(|e| storage_error(e.to_string()))?;
        // 文件类型
        let memi_type = if !extension.is_empty() { extension.to_string() } else { String::new() };

        // 获取相对于resources目录的路径
        let relative_path = new_file.strip_prefix(target_dir).map_err(|e| storage_error(format!("Failed to get relative path: {}", e)))?;

        // 存储新文件名称和路径到数据库
        let model = Resource {
            id: 0, // 自增ID
            name: new_name,
            path: relative_path.to_string_lossy().to_string(),
            mime_type: memi_type,
            size: file_meta_data.len() as i64,
            hash: String::new(),
        };
        let result = Resource::insert(conn, &model).await.map_err(|e| storage_error(format!("Resource insert error: {}", e)))?;
        Ok(result.last_insert_id.as_string().unwrap())
    }

    /// 获取资源文件路径
    ///
    /// 本函数根据提供的资源ID查询数据库，以获取资源的文件路径
    /// 如果数据库连接可用且查询成功，则返回资源的文件路径
    /// 如果查询失败或找不到资源，则返回一个错误
    ///
    /// 参数:
    /// - `id`: 资源的唯一标识符，用于数据库查询
    ///
    /// 返回值:
    /// - `StorageResult<String>`: 一个结果类型，包含资源的文件路径或一个错误
    async fn get_resource(
        &self,
        id: String,
    ) -> StorageResult<String> {
        //查询db 返回文件路径
        let resource = if let Some(conn) = &self.conn {
            resource::Resource::select_by_id(conn, id.parse().unwrap()).await.map_err(|e| storage_error(e.to_string()))?
        } else {
            None
        };
        if resource.is_none() {
            Err(storage_error("File not found by id".to_string()))
        } else {
            Ok(resource.unwrap().path)
        }
    }
}
