use std::sync::Arc;

use async_trait::async_trait;
use mf_macro::impl_command;
use mf_model::node_type::NodeEnum;
use mf_state::{Transaction, transaction::Command};
use mf_transform::TransformResult;
#[derive(<PERSON><PERSON>, <PERSON><PERSON>ult, Debug)]
pub struct MyCommand;
impl MyCommand {
    pub fn new() -> Arc<MyCommand> {
        Arc::new(MyCommand)
    }
}

#[async_trait]
impl Command for MyCommand {
    fn name(&self) -> String {
        "cassie".to_string()
    }
    async fn execute(
        &self,
        tr: &mut Transaction,
    ) -> TransformResult<()> {
        //  数据库的查询
        let doc = tr.doc();
        let root = doc.root();
        let node = tr.schema.nodes.get("DW").unwrap().create(None, None, vec![], None);
        tr.add_node(root.id.clone(), vec![NodeEnum::from(node, vec![])])?;
        Ok(())
    }
}

impl_command!(
    MyCommand1,
    async |tr: &mut Transaction| -> TransformResult<()> {
        let doc = tr.doc();
        let root = doc.root();
        let node = tr.schema.nodes.get("DW").unwrap().create(None, None, vec![], None);
        tr.add_node(root.id.clone(), vec![NodeEnum::from(node, vec![])])?;
        Ok(())
    },
    "MyCommand1"
);
