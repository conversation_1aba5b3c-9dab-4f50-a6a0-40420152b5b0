<?xml version="1.0" encoding="UTF-8"?>
<!-- 单价构成Schema文件 -->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://pricing-dev.oss-cn-hangzhou.aliyuncs.com/moduforge/moduforge-schema.xsd">
    <nodes>
        <node name="djgc" desc="单价构成" content="djgcRowNode+">
            
        </node>
        <node name="djgcRowNode" desc="单价构成行节点" content="">
            <attrs>
                <!-- 模版编码 -->
                <attr name="qfCode" default=""/>
                <!-- 标准 -->
                <attr name="standard" default=""/>
                <!-- 单价构成类型 -->
                <attr name="type" default=""/>
                <!-- 费用代号 -->
                <attr name="code" default=""/>
                <!-- 标准 -->
                <attr name="standard" default=""/>
                <!-- 单价构成类型 -->
                <attr name="type" default=""/>
                <!-- 费用代号 -->
                <attr name="code" default=""/>
                <!-- 计算基数 -->
                <attr name="caculateBase" default=""/>
                <!-- 描述 -->
                <attr name="desc" default=""/>
                <!-- 费率 -->
                <attr name="rate" default=""/>
                <!-- 单价 -->
                <attr name="price" default="0"/>
            </attrs>
        </node>
    </nodes>
</schema>