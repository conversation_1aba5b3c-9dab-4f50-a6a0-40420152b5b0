/*
CREATE TABLE "base_de_library" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "library_code" varchar,
  "library_name" varchar,
  "library_major" varchar,
  "sort_no" decimal,
  "sort" decimal,
  "release_date" varchar,
  "ss_province" varchar,
  "ss_city" varchar,
  "remark" varchar,
  "de_standard_id" varchar,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ('A'),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "description" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeLibrary {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub library_name: Option<String>,
    pub library_major: Option<String>,
    pub sort_no: Option<f64>,
    pub sort: Option<f64>,
    pub release_date: Option<String>,
    pub ss_province: Option<String>,
    pub ss_city: Option<String>,
    pub remark: Option<String>,
    pub de_standard_id: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(BaseDeLibrary {}, "base_de_library");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "library_major".to_string(),
            "release_date".to_string(),
            "ss_province".to_string(),
            "ss_city".to_string(),
            "remark".to_string(),
            "de_standard_id".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "library_major".to_string(),
            "sort_no".to_string(),
            "sort".to_string(),
            "release_date".to_string(),
            "ss_province".to_string(),
            "ss_city".to_string(),
            "remark".to_string(),
            "de_standard_id".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "library_major".to_string(),
            "sort_no".to_string(),
            "sort".to_string(),
            "release_date".to_string(),
            "ss_province".to_string(),
            "ss_city".to_string(),
            "remark".to_string(),
            "de_standard_id".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeLibrary>("base_de_library", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeLibrary>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_library").await?;
    let base_de_library_index = IndexClient::<BaseDeLibrary> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_library_index);
    Ok(search_service)
}
