
use serde::{Deserialize, Serialize};
use mf_derive::Node;


/// 工程项目节点
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "GCXM"]
#[desc = "工程项目"]
#[content = "DXGC+"]
pub struct ProjectNode {
    #[attr]
    pub construct_name: Option<String>,
    
    #[attr]
    pub construct_code: Option<String>,
    
    #[attr]
    pub path: Option<String>,
    
    #[attr]
    pub bidding_type: i32,
    
    #[attr]
    pub ss_province: Option<String>,
    
    #[attr]
    pub ss_province_name: Option<String>,
    
    #[attr]
    pub ss_city: Option<String>,
    
    #[attr]
    pub ss_city_name: Option<String>,
    
    #[attr]
    pub gf_id: Option<String>,
    
    #[attr]
    pub awf_id: Option<String>,
    
    #[attr]
    pub rgf_id: Option<String>,
    
    #[attr]
    pub qd_standard_id: Option<String>,
    
    #[attr]
    pub de_standard_id: Option<String>,
    
    #[attr]
    pub de_standard_release_year: Option<String>,
    
    #[attr]
    pub project_overview: Option<String>,
    
    #[attr]
    pub fddbr: Option<String>,
    
    #[attr]
    pub xml_factory: Option<String>,
    
    #[attr]
    pub construction_unit: Option<String>,
    
    #[attr]
    pub version: Option<String>,
    
    #[attr]
    pub main_rcj_show_flag: Option<bool>,
    
    #[attr]
    pub standard_conversion_show_flag: Option<bool>,
    
    #[attr]
    pub de_gl_tc_flag: Option<String>,
}


/// 单项工程节点
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "DXGC"]
#[desc = "单项工程"]
#[content = "(DWGC|DXGC)+"]
pub struct SingleProjectNode {
    #[attr]
    pub project_code: Option<String>,
    
    #[attr]
    pub project_name: Option<String>,
    
    #[attr]
    pub jzmj: Option<f64>,
    
    #[attr]
    pub ss_province: Option<String>,
    
    #[attr]
    pub ss_city: Option<String>,
    
    #[attr]
    pub total: Option<f64>,
    
    #[attr]
    pub average: Option<f64>,
    
    #[attr]
    pub unitcost: Option<f64>,
    
    #[attr]
    pub bz_date: Option<String>,
    
    #[attr]
    pub gfee: Option<f64>,
    
    #[attr]
    pub safe_fee: Option<f64>,
    
    #[attr]
    pub sbf: Option<f64>,
    
    #[attr]
    pub clde_year: Option<String>,
    
    #[attr]
    pub zbkzj: Option<f64>,
    
    #[attr]
    pub zbj: Option<f64>,
    
    #[attr]
    pub construct_id: Option<String>,
    
    #[attr]
    pub sbf_tax: Option<f64>,
    
    #[attr]
    pub sbf_cost: Option<f64>,
    
    #[attr]
    pub sort_no: Option<i32>,
    
    #[attr]
    pub report_storage_urls: Option<String>,
    
    #[attr]
    pub report_url: Option<String>,
}

/// 单位工程节点
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "DWGC"]
#[desc = "单位工程"]
pub struct UnitProjectNode {
    #[attr]
    pub up_code: Option<String>,
    
    #[attr]
    pub up_name: Option<String>,
    
    #[attr]
    pub uptotal: Option<f64>,
    
    #[attr]
    pub average: Option<f64>,
    
    #[attr]
    pub fbfxhj: Option<f64>,
    
    #[attr]
    pub fbfxrgf: Option<f64>,
    
    #[attr]
    pub fbfxclf: Option<f64>,
    
    #[attr]
    pub fbfxjxf: Option<f64>,
    
    #[attr]
    pub fbfxlr: Option<f64>,
    
    #[attr]
    pub fbfxglf: Option<f64>,
    
    #[attr]
    pub csxhj: Option<f64>,
    
    #[attr]
    pub csxrgf: Option<f64>,
    
    #[attr]
    pub csxclf: Option<f64>,
    
    #[attr]
    pub csxjxf: Option<f64>,
    
    #[attr]
    pub csxglf: Option<f64>,
    
    #[attr]
    pub csxlr: Option<f64>,
    
    #[attr]
    pub djcsxhj: Option<f64>,
    
    #[attr]
    pub djcsxrgf: Option<f64>,
    
    #[attr]
    pub djcsxclf: Option<f64>,
    
    #[attr]
    pub djcsxjxf: Option<f64>,
    
    #[attr]
    pub djcsxglf: Option<f64>,
    
    #[attr]
    pub djcsxlr: Option<f64>,
    
    #[attr]
    pub zjcsxhj: Option<f64>,
    
    #[attr]
    pub zjcsxrgf: Option<f64>,
    
    #[attr]
    pub zjcsxclf: Option<f64>,
    
    #[attr]
    pub zjcsxjxf: Option<f64>,
    
    #[attr]
    pub zjcsxglf: Option<f64>,
    
    #[attr]
    pub zjcsxlr: Option<f64>,
    
    #[attr]
    pub qtxmhj: Option<f64>,
    
    #[attr]
    pub qtxmrgf: Option<f64>,
    
    #[attr]
    pub qtxmclf: Option<f64>,
    
    #[attr]
    pub qtxmjxf: Option<f64>,
    
    #[attr]
    pub qtxmglf: Option<f64>,
    
    #[attr]
    pub gfee: Option<f64>,
    
    #[attr]
    pub safe_fee: Option<f64>,
    
    #[attr]
    pub safe_fl: Option<f64>,
    
    #[attr]
    pub sbf: Option<f64>,
    
    #[attr]
    pub sqgczj: Option<f64>,
    
    #[attr]
    pub jxse: Option<f64>,
    
    #[attr]
    pub xxse: Option<f64>,
    
    #[attr]
    pub zzsynse: Option<f64>,
    
    #[attr]
    pub fjse: Option<f64>,
    
    #[attr]
    pub sj: Option<f64>,
    
    #[attr]
    pub xxsefl: Option<f64>,
    
    #[attr]
    pub fjsffl: Option<f64>,
    
    #[attr]
    pub fy_zb: Option<f64>,
    
    #[attr]
    pub construct_major_type: Option<String>,
    
    #[attr]
    pub sp_id: Option<String>,
    
    #[attr]
    pub status: Option<String>,
    
    #[attr]
    pub create_date: Option<String>,
    
    #[attr]
    pub update_date: Option<String>,
    
    #[attr]
    pub del_flag: Option<bool>,
    
    #[attr]
    pub tenant_id: Option<String>,
    
    #[attr]
    pub sort_no: Option<i32>,
    
    #[attr]
    pub construct_id: Option<String>,
    
    #[attr]
    pub bidding_type: Option<i32>,
    
    #[attr]
    pub import_url: Option<String>,
    
    #[attr]
    pub report_storage_urls: Option<String>,
    
    #[attr]
    pub report_url: Option<String>,
    
    #[attr]
    pub main_de_library: Option<String>,
    
    #[attr]
    pub second_installation_project_name: Option<String>,
    
    #[attr]
    pub rgf_id: Option<String>,
    
    #[attr]
    pub fbfxzcf: Option<f64>,
    
    #[attr]
    pub fbfxzgj: Option<f64>,
    
    #[attr]
    pub djcsxzcf: Option<f64>,
    
    #[attr]
    pub zjcsxzcf: Option<f64>,
    
    #[attr]
    pub qtxmzlje: Option<f64>,
    
    #[attr]
    pub qtxmzygczgj: Option<f64>,
    
    #[attr]
    pub qtxmzcbfwf: Option<f64>,
    
    #[attr]
    pub qtxmjrg: Option<f64>,
    
    #[attr]
    pub unitcost: Option<f64>,
    
    #[attr]
    pub sbfsj: Option<f64>,
    
    #[attr]
    pub sbfsjjg: Option<f64>,
    
    #[attr]
    pub gczj: Option<f64>,
    
    #[attr]
    pub gczjsbsj: Option<f64>,
    
    #[attr]
    pub gczjsbsjjg: Option<f64>,
    
    #[attr]
    pub fixation_security_fee: Option<i32>,
}

/// 项目结构定义工厂
pub struct ProjectStructureFactory;

impl ProjectStructureFactory {
    /// 创建完整项目结构
    pub fn create_project_structure() -> Vec<mf_core::node::Node> {
        let mut project = ProjectNode::node_definition();
        let single = SingleProjectNode::node_definition();
        let unit = UnitProjectNode::node_definition();
        
        project.set_top_node();
        
        vec![project, single, unit]
    }
    
    /// 创建单位工程项目结构
    pub fn create_unit_project_structure() -> Vec<mf_core::node::Node> {
        let mut project = ProjectNode::node_definition();
        let unit = UnitProjectNode::node_definition();
        
        project.set_top_node();
        project.set_content("DWGC*");
        
        vec![project, unit]
    }
}