/*
CREATE TABLE "base_de_cslb_relation" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "is_main" varchar,
  "sgzj_class_name" varchar,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ('A'),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "description" varchar,
  "rate_name" varchar,
  "rate_code" varchar
);
*/
use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeCslbRelation {
    pub sequence_nbr: String,
    pub is_main: Option<String>,
    pub sgzj_class_name: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub rate_name: Option<String>,
    pub rate_code: Option<String>,
}
//crud!(BaseDeCslbRelation {}, "base_de_cslb_relation");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "is_main".to_string(),
            "sgzj_class_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "rate_name".to_string(),
            "rate_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "is_main".to_string(),
            "sgzj_class_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "rate_name".to_string(),
            "rate_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "is_main".to_string(),
            "sgzj_class_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "rate_name".to_string(),
            "rate_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeCslbRelation>("base_de_cslb_relation", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeCslbRelation>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_cslb_relation").await?;
    let base_de_cslb_relation_index = IndexClient::<BaseDeCslbRelation> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_cslb_relation_index);
    Ok(search_service)
}
