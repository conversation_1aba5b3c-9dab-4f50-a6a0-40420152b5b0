/*
CREATE TABLE "base_de_chapter" (
  "sequence_nbr" text(255) NOT NULL,
  "library_code" text(255),
  "library_name" text(255),
  "classify_level4" text(255),
  "classify_level3" text(255),
  "classify_level2" text(255),
  "classify_level1" text(255),
  "sort_no" integer(11),
  "classlevel_split_concat" text(255),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

//crud!(BaseDeChapter {}, "base_de_chapter");

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeChapter {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub library_name: Option<String>,
    pub classify_level4: Option<String>,
    pub classify_level3: Option<String>,
    pub classify_level2: Option<String>,
    pub classify_level1: Option<String>,
    pub sort_no: Option<i32>,
    pub classlevel_split_concat: Option<String>,
}

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "classify_level4".to_string(),
            "classify_level3".to_string(),
            "classify_level2".to_string(),
            "classify_level1".to_string(),
            "classlevel_split_concat".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "classify_level4".to_string(),
            "classify_level3".to_string(),
            "classify_level2".to_string(),
            "classify_level1".to_string(),
            "sort_no".to_string(),
            "classlevel_split_concat".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "classify_level4".to_string(),
            "classify_level3".to_string(),
            "classify_level2".to_string(),
            "classify_level1".to_string(),
            "sort_no".to_string(),
            "classlevel_split_concat".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeChapter>("base_de_chapter", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeChapter>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_chapter").await?;
    let base_de_chapter_index = IndexClient::<BaseDeChapter> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_chapter_index);
    Ok(search_service)
}
