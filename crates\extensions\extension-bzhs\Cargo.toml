[package]
name = "extension-bzhs"
version = { workspace = true }
edition = { workspace = true }
authors = { workspace = true }
description = "BZHS 标准换算 extension"
[dependencies]
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }


shared = { workspace = true }

moduforge-model = { workspace = true }
moduforge-state = { workspace = true }
moduforge-transform = { workspace = true }
moduforge-core = { workspace = true }
moduforge-macros = { workspace = true }