{"$schema": "https://schema.tauri.app/config/2", "productName": "计价软件2", "version": "0.1.0", "identifier": "com.pricedesktop.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../frontend/dist"}, "app": {"withGlobalTauri": true, "windows": [{"title": "计价软件2", "width": 800, "height": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": [], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "nsis": null}}}