[package]
name = "p-desktop"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "p_desktop_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tokio = { workspace = true, features = ["full"] }
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
tauri-plugin-api= {path="../tauri-plugin-api"}
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
anyhow= { workspace = true }
tauri-plugin-devtools = "2.0.0"
clap = { version = "4.0.15", features = ["derive", "env"] }
getset = { workspace = true }
state = { workspace = true }
toml = "0.8.20"
async-trait = { workspace = true }

price-budget-core = { workspace = true }
price-rules = { workspace = true }
price-storage = { workspace = true }

# 引入pagkages 下面的所有库
extension-base-schema = { workspace = true }
extension-djgc = { workspace = true }
extension-bzhs = { workspace = true }
extension-cost-collect = { workspace = true }
extension-fbfx-csxm = { workspace = true }
extension-rcj = { workspace = true }
extension-rcj-collect = { workspace = true }
price-search = { workspace = true }
shared = { workspace = true }


moduforge-model = { workspace = true }
moduforge-state = { workspace = true }
moduforge-transform = { workspace = true }
moduforge-core= { workspace = true }
colored = "3.0.0"
[profile.release]
codegen-units = 1
incremental = true
lto = true
opt-level = 'z' # Optimize for size
