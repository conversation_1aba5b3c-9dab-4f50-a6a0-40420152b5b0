use serde::{Deserialize, Serialize};
use mf_derive::Node;

/// 人材机明细节点
/// 用于表示工程项目中的人工、材料、机械等资源信息
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "rcj"]
#[desc = "人材机明细"]
pub struct RcjNode {
    /// 材料编码
    #[attr]
    pub material_code: String,
    
    /// 标准ID
    #[attr]
    pub standard_id: String,
    
    /// 材料名称
    #[attr]
    pub material_name: String,
    
    /// 规格
    #[attr]
    pub specification: String,
    
    /// 单位
    #[attr]
    pub unit: String,
    
    /// 单位ID
    #[attr]
    pub unit_id: String,
    
    /// 类型（1-人工，2-材料，3-机械）
    #[attr(default = 0)]
    pub kind: i32,
    
    /// 定额库编码
    #[attr]
    pub library_code: String,
    
    /// 类型描述
    #[attr]
    pub type_name: String,
    
    /// 工程ID
    #[attr]
    pub construct_id: String,
    
    /// 是否甲供材料（0-否，1-是）
    #[attr(default = 0)]
    pub if_donor_material: i32,
    
    /// 类型备份
    #[attr(default = 0)]
    pub kind_back_up: i32,
    
    /// 定额ID
    #[attr]
    pub de_id: String,
    
    /// 资源用量
    #[attr(default = 0.0)]
    pub res_qty: f64,
    
    /// 初始资源用量
    #[attr(default = 0.0)]
    pub init_res_qty: f64,
    
    /// 等级标记
    #[attr]
    pub level_mark: String,
    
    /// 总数量
    #[attr(default = 0.0)]
    pub total_number: f64,
    
    /// 总价
    #[attr(default = 0.0)]
    pub total: f64,
    
    /// 人材机明细DTOs
    #[attr]
    pub rcj_details_dtos: String,
    
    /// 标记总和
    #[attr(default = 0.0)]
    pub mark_sum: f64,
    
    /// 参考记录
    #[attr]
    pub reference_record: String,
    
    /// 人材机标志
    #[attr(default = 0)]
    pub rcj_flag: i32,
    
    /// 定额单价
    #[attr(default = 0.0)]
    pub de_price: f64,
    
    /// 市场价
    #[attr(default = 0.0)]
    pub market_price: f64,
    
    /// 是否辅材人材机
    #[attr(default = 0)]
    pub is_fyrcj: i32,
    
    /// 是否可变更
    #[attr(default = 0)]
    pub is_change_ava: i32,
    
    /// 种类SC
    #[attr]
    pub kind_sc: String,
    
    /// 换算系数
    #[attr]
    pub transfer_factor: String,
    
    /// 信息价基期
    #[attr(default = 0.0)]
    pub price_base_journal: f64,
    
    /// 信息价基期含税
    #[attr(default = 0.0)]
    pub price_base_journal_tax: f64,
    
    /// 市场价
    #[attr(default = 0.0)]
    pub price_market: f64,
    
    /// 市场价含税
    #[attr(default = 0.0)]
    pub price_market_tax: f64,
    
    /// 市场价公式
    #[attr(default = 0.0)]
    pub price_market_formula: f64,
    
    /// 市场价含税公式
    #[attr(default = 0.0)]
    pub price_market_tax_formula: f64,
    
    /// 市场价公式结果
    #[attr(default = 0.0)]
    pub market_price_formula: f64,
    
    /// 税率
    #[attr(default = 0.0)]
    pub tax_rate: f64,
    
    /// 是否锁定
    #[attr(default = false)]
    pub is_lock: bool,
    
    /// 格式化系数
    #[attr(default = 0.0)]
    pub formatted_coefficient: f64,
    
    /// 分级编码 Level 1
    #[attr]
    pub level1: String,
    
    /// 分级编码 Level 2
    #[attr]
    pub level2: String,
    
    /// 分级编码 Level 3
    #[attr]
    pub level3: String,
    
    /// 分级编码 Level 4
    #[attr]
    pub level4: String,
    
    /// 分级编码 Level 5
    #[attr]
    pub level5: String,
    
    /// 分级编码 Level 6
    #[attr]
    pub level6: String,
    
    /// 分级编码 Level 7
    #[attr]
    pub level7: String,
    
    /// 机构编码
    #[attr]
    pub agency_code: String,
    
    /// 产品编码
    #[attr]
    pub product_code: String,
    
    /// 排序号
    #[attr(default = 0)]
    pub sort_no: i32,
    
    /// 是否数据税率
    #[attr]
    pub is_data_tax_rate: String,
    
    /// 单项ID
    #[attr]
    pub single_id: String,
    
    /// 初始材料编码
    #[attr]
    pub init_material_code: String,
}

/// 人材机节点工厂
pub struct RcjFactory;

impl RcjFactory {
    /// 创建人材机节点
    pub fn create_rcj_node() -> mf_core::node::Node {
        RcjNode::node_definition()
    }
    
    /// 创建人材机节点结构
    pub fn create_rcj_structure() -> Vec<mf_core::node::Node> {
        vec![Self::create_rcj_node()]
    }
}