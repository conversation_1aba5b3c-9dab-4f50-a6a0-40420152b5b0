use std::{
    ops::{Deref, DerefMut},
    sync::Arc,
};

use mf_core::{ForgeResult, runtime::async_runtime::ForgeAsyncRuntime, extension::Extension, types::Extensions};
use mf_state::{resource::Resource, resource_table::ResourceId};

use price_rules::rules_engine::RulesEngine;
use crate::types::PriceEditorOptions;

/// 计价编辑器
///
/// PriceEditor 是本系统的核心组件，负责管理计价过程中的编辑操作
/// 它包装了 AsyncEditor，提供了价格计算和资源管理功能
///
/// # 功能概述
/// - 封装底层异步编辑器，提供高级计价编辑接口
/// - 管理和访问系统中的各类资源，如规则引擎、存储接口等
/// - 通过 Deref/DerefMut 实现对底层编辑器的透明访问
///
/// # 使用示例
/// ```
/// let options = PriceEditorOptions::new(...);
/// let editor = PriceEditor::create(options).await?;
///
/// // 获取规则引擎
/// let rules_engine = editor.get_rules_engine()?;
///
/// // 访问特定资源
/// let resource = editor.get_resource::<MyResource>(resource_id)?;
/// ```

pub struct PriceEditor {
    /// 内部异步编辑器实例，处理底层编辑操作
    ///
    /// 负责状态管理、撤销/重做操作以及资源跟踪等基础功能
    editor: ForgeAsyncRuntime,

    /// 编辑器配置选项
    ///
    /// 包含创建和运行编辑器所需的各项配置，如存储接口和规则加载器
    options: PriceEditorOptions,
}
unsafe impl Send for PriceEditor {}
unsafe impl Sync for PriceEditor {}

impl PriceEditor {
    /// 创建新的计价编辑器实例
    ///
    /// 该方法将初始化内部的异步编辑器，注册扩展组件，并配置规则引擎
    ///
    /// # 参数
    /// * `options` - 配置编辑器的选项，包含存储接口和规则加载器等核心组件
    ///
    /// # 返回
    /// * `Result<Self, EditorError>` - 成功返回编辑器实例，失败返回错误
    ///
    /// # 错误
    /// 如果异步编辑器创建失败，将返回相应的错误信息
    pub async fn create(mut options: PriceEditorOptions) -> ForgeResult<Self> {
        // 创建规则引擎扩展
        let loader = options.loader.clone();
        let mut extension = Extension::default();
        extension.add_op_fn(Arc::new(move |op_state| {
            op_state.put(RulesEngine::new(loader.clone()));
            Ok(())
        }));

        // 更新编辑器选项
        let mut exts = options.editor_options.get_extensions();
        exts.push(Extensions::E(extension));
        options.editor_options = options.editor_options.set_extensions(exts);

        // 创建异步编辑器
        let editor = ForgeAsyncRuntime::create(options.editor_options.clone()).await?;

        Ok(Self { editor, options })
    }

    /// 获取编辑器配置选项
    ///
    /// 返回当前编辑器使用的配置选项，可用于检查或修改配置
    ///
    /// # 返回
    /// * `&PriceEditorOptions` - 编辑器选项的引用
    pub fn get_options(&self) -> &PriceEditorOptions {
        &self.options
    }

    /// 获取指定类型和ID的资源
    ///
    /// 从编辑器的资源管理器中查找并返回指定类型和ID的资源
    /// 资源可以是任何实现了Resource trait的类型
    ///
    /// # 参数
    /// * `rid` - 资源ID，用于在资源表中定位特定资源
    ///
    /// # 返回
    /// * `Result<Arc<T>, EditorError>` - 成功返回资源的Arc引用，失败返回错误
    ///
    /// # 错误
    /// 如果资源不存在或类型不匹配，将返回NotFound错误
    pub fn get_resource<T: Resource>(
        &self,
        rid: ResourceId,
    ) -> Option<Arc<T>> {
        // 获取编辑器状态
        // 编辑器状态包含所有运行时资源和配置
        let state = self.editor.get_state();

        // 获取资源管理器
        // 资源管理器负责管理系统中所有注册的资源
        let resource_manager = state.resource_manager();

        // 从资源表中获取指定类型和ID的资源
        // 如果资源不存在或类型不匹配，将返回错误
        resource_manager.resource_table.get::<T>(rid)
    }

    /// 获取规则引擎实例
    /// 返回当前编辑器使用的规则引擎，用于执行计价规则和业务逻辑
    /// 规则引擎被存储在编辑器的资源管理器中
    ///
    /// # 返回
    /// * `Result<Arc<RulesEngine>, EditorError>` - 成功返回规则引擎的Arc引用，失败返回错误
    ///
    /// # 错误
    /// 如果规则引擎未初始化或获取失败，将返回相应的错误
    pub fn get_rules_engine(&self) -> ForgeResult<Arc<RulesEngine>> {
        // 获取编辑器状态
        // 编辑器状态包含规则引擎的引用
        let state = self.editor.get_state();

        // 从资源管理器中获取规则引擎
        // 规则引擎被存储为共享资源，可被多个组件访问
        let rules_engine = {
            let resource_manager = state.resource_manager();
            resource_manager.get::<RulesEngine>().ok_or(anyhow::anyhow!("规则引擎未初始化"))?
        };

        Ok(rules_engine)
    }

    /// 导出数据到指定路径
    ///
    /// 该函数用于将数据异步导出到给定的路径如果路径未指定，则使用默认路径
    /// 此函数不对返回值进行处理，任何结果或错误都将被忽略
    ///
    /// # 参数
    ///
    /// * `path` - 一个可选的字符串参数，指定数据导出的目标路径如果为`None`，则使用默认路径
    pub async fn export(
        &self,
        path: Option<String>,
    ) {
        let storage = self.get_options().storage.clone();
        tokio::task::spawn(async move {
            let _ = storage.export(path).await;
        });
    }
}

/// 实现 Deref trait，允许 PriceEditor 透明地解引用为 AsyncEditor
///
/// 这使得可以直接在 PriceEditor 实例上调用 AsyncEditor 的方法
/// 例如，`editor.undo()` 将直接调用底层 AsyncEditor 的 undo 方法
///
/// # 使用示例
/// ```
/// let editor = PriceEditor::create(options).await?;
///
/// // 直接调用 AsyncEditor 的方法
/// editor.undo().await?;
/// editor.redo().await?;
/// ```
impl Deref for PriceEditor {
    type Target = ForgeAsyncRuntime;

    fn deref(&self) -> &Self::Target {
        &self.editor
    }
}

/// 实现 DerefMut trait，允许对 PriceEditor 内部的 AsyncEditor 进行可变访问
///
/// 这使得可以在需要可变引用的场景下直接使用 PriceEditor 调用 AsyncEditor 的方法
/// 例如，`editor.begin_operation()` 将直接调用底层 AsyncEditor 的相应方法
impl DerefMut for PriceEditor {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.editor
    }
}
