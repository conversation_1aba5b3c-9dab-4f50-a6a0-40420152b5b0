# Price-RS Extension Generator

一个专门用于生成 Price-RS 扩展项目模板的命令行工具。

## 🚀 快速开始

### 安装

```bash
# 从源码编译
git clone https://github.com/your-org/price-rs
cd price-rs/tools/extension-generator
cargo install --path .
```

### 基本使用

```bash
# 生成标准扩展项目
price-ext-gen new my-extension --description "我的扩展项目"

# 交互式生成
price-ext-gen new my-extension --interactive

# 生成 API 类型的扩展
price-ext-gen new my-api --extension-type api

# 自定义输出目录
price-ext-gen new my-extension --output ./custom-dir

# 强制覆盖已存在的目录
price-ext-gen new my-extension --force
```

## 📋 功能特性

### ✅ 支持的扩展类型

- **🏗️ Standard**: 包含完整四层架构的标准扩展
- **🌐 API**: 专注于 API 服务的扩展
- **⚡ Core**: 核心业务逻辑扩展（无 Web 接口）
- **📦 Lightweight**: 轻量级扩展（仅数据和业务逻辑层）
- **🔧 Custom**: 自定义扩展（可选择特定层组合）

### 🏗️ 四层架构

每个扩展项目都基于标准的四层架构：

1. **📄 Nodes 层**: 数据结构定义和字段验证
2. **🔌 Plugins 层**: 业务逻辑实现和事务处理
3. **⚡ Commands 层**: CRUD 操作和命令处理
4. **🌐 Router 层**: HTTP API 接口和路由管理

## 📖 详细使用指南

### 命令概览

```bash
# 查看帮助
price-ext-gen --help

# 生成新扩展
price-ext-gen new <NAME> [OPTIONS]

# 列出可用模板
price-ext-gen list [--detailed]

# 验证项目结构
price-ext-gen validate <PATH> [--fix]

# 配置管理
price-ext-gen config <ACTION>
```

### 生成新扩展

#### 基本语法

```bash
price-ext-gen new <NAME> [OPTIONS]
```

#### 选项

- `--description, -d <DESC>`: 扩展描述
- `--output, -o <DIR>`: 输出目录
- `--extension-type <TYPE>`: 扩展类型 (standard|api|core|lightweight|custom)
- `--layers <LAYERS>`: 启用的层 (用逗号分隔)
- `--author <AUTHOR>`: 作者信息
- `--force, -f`: 强制覆盖已存在的目录
- `--interactive, -i`: 交互式模式

#### 示例

```bash
# 标准扩展
price-ext-gen new djgc --description "单价构成扩展"

# API 扩展
price-ext-gen new my-api \
  --extension-type api \
  --description "API 服务扩展" \
  --author "Your Name"

# 自定义层组合
price-ext-gen new my-custom \
  --extension-type custom \
  --layers nodes,plugins,commands \
  --description "自定义扩展"

# 交互式模式
price-ext-gen new my-extension -i
```

### 模板列表

```bash
# 查看所有可用模板
price-ext-gen list

# 查看详细信息
price-ext-gen list --detailed
```

### 项目验证

```bash
# 验证项目结构
price-ext-gen validate ./my-extension

# 验证并自动修复问题
price-ext-gen validate ./my-extension --fix
```

### 配置管理

```bash
# 查看当前配置
price-ext-gen config show

# 设置默认作者
price-ext-gen config set default_author "Your Name"

# 获取配置项
price-ext-gen config get default_author

# 重置配置
price-ext-gen config reset

# 配置向导
price-ext-gen config init
```

## 🎯 生成的项目结构

```
extension-my-extension/
├── Cargo.toml                    # 项目配置
├── README.md                     # 项目文档
└── src/
    ├── lib.rs                   # 主入口文件
    ├── nodes/                   # 📄 数据结构层
    │   ├── mod.rs
    │   ├── my_extension_definitions.rs
    │   └── fields.rs
    ├── plugins/                 # 🔌 业务逻辑层
    │   └── mod.rs
    ├── command/                 # ⚡ 命令操作层
    │   └── mod.rs
    └── router/                  # 🌐 路由接口层
        ├── mod.rs
        ├── handlers.rs
        └── dto.rs
```

## 🔧 开发工作流

### 1. 生成项目

```bash
price-ext-gen new my-extension \
  --description "我的扩展项目" \
  --extension-type standard \
  --interactive
```

### 2. 添加到工作空间

将生成的项目添加到根目录的 `Cargo.toml`:

```toml
[workspace]
members = [
    # ... 其他成员
    "extension-my-extension",
]
```

### 3. 开发和测试

```bash
cd extension-my-extension

# 编译项目
cargo build

# 运行测试
cargo test

# 格式化代码
cargo fmt

# 运行 clippy 检查
cargo clippy
```

### 4. 完善业务逻辑

生成的代码包含 TODO 标记，需要根据实际需求完善：

- **Nodes 层**: 完善数据结构定义和验证规则
- **Plugins 层**: 实现具体的业务逻辑
- **Commands 层**: 实现 CRUD 操作
- **Router 层**: 完善 API 接口和处理器

## 🌟 最佳实践

### 命名规范

- **扩展名称**: 使用 kebab-case，如 `djgc`, `fbfx-csxm`
- **结构体名称**: 使用 PascalCase，如 `DjgcContainerNode`
- **函数名称**: 使用 snake_case，如 `calculate_unit_price`

### 目录组织

```
your-workspace/
├── Cargo.toml                   # 工作空间配置
├── crates/
│   └── extensions/
│       ├── extension-djgc/      # 单价构成扩展
│       ├── extension-rcj/       # 人材机扩展
│       └── extension-my-ext/    # 你的扩展
└── tools/
    └── extension-generator/     # 生成器工具
```

### 开发流程

1. **规划设计**: 确定扩展的功能需求和数据模型
2. **生成模板**: 使用生成器创建基础项目结构
3. **实现业务**: 完善各层的具体实现
4. **编写测试**: 添加单元测试和集成测试
5. **文档编写**: 完善 API 文档和使用说明
6. **集成测试**: 在整个系统中测试扩展功能

## 🛠️ 自定义和扩展

### 自定义模板

1. 创建模板目录：

```bash
mkdir ~/.price-ext-gen/templates
```

2. 配置模板目录：

```bash
price-ext-gen config set template_dir ~/.price-ext-gen/templates
```

3. 添加自定义模板文件（使用 Tera 模板引擎）

### 环境变量

- `PRICE_EXT_GEN_CONFIG`: 配置文件路径
- `PRICE_EXT_GEN_TEMPLATES`: 模板目录路径

## 📚 示例项目

### 简单扩展示例

```bash
# 生成一个简单的计算扩展
price-ext-gen new simple-calc \
  --extension-type core \
  --description "简单计算扩展"
```

### API 服务示例

```bash
# 生成一个完整的 API 服务
price-ext-gen new pricing-api \
  --extension-type api \
  --description "定价服务 API" \
  --author "Development Team"
```

### 自定义扩展示例

```bash
# 生成仅包含数据层和业务层的扩展
price-ext-gen new data-processor \
  --extension-type custom \
  --layers nodes,plugins \
  --description "数据处理扩展"
```

## 🐛 故障排除

### 常见问题

**Q: 生成失败，提示模板错误？**
A: 检查模板文件是否完整，或使用 `--verbose` 参数查看详细错误信息。

**Q: 生成的代码编译失败？**
A: 确保工作空间配置正确，并检查依赖项是否正确配置。

**Q: 无法找到配置文件？**
A: 使用 `price-ext-gen config init` 初始化配置。

### 调试模式

```bash
# 启用详细日志
price-ext-gen --verbose new my-extension

# 查看配置信息
price-ext-gen config show

# 验证项目结构
price-ext-gen validate ./my-extension
```

## 🤝 贡献指南

欢迎贡献代码！请参考以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🔗 相关链接

- [Price-RS 主项目](https://github.com/your-org/price-rs)
- [架构设计文档](../../../claudedocs/extension-architecture-comprehensive-analysis.md)
- [ModuForge-RS 框架](https://github.com/your-org/moduforge-rs)