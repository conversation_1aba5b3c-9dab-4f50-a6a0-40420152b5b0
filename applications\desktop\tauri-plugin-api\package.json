{"name": "tauri-plugin-api-api", "version": "0.1.0", "author": "You", "description": "", "type": "module", "types": "./dist-js/index.d.ts", "main": "./dist-js/index.cjs", "module": "./dist-js/index.js", "exports": {"types": "./dist-js/index.d.ts", "import": "./dist-js/index.js", "require": "./dist-js/index.cjs"}, "files": ["dist-js", "README.md"], "scripts": {"build": "rollup -c", "prepublishOnly": "pnpm build", "pretest": "pnpm build"}, "dependencies": {"@tauri-apps/api": "2"}, "devDependencies": {"@rollup/plugin-typescript": "^11.1.6", "rollup": "^4.9.6", "typescript": "^5.3.3", "tslib": "^2.6.2"}}