/*
CREATE TABLE "base_jxpb" (
  "sequence_nbr" text(64) NOT NULL,
  "rcj_id" text(64) NOT NULL,
  "jx_code" text(255),
  "jx_name" text(255),
  "material_code" text(50),
  "kind" integer(255),
  "material_name" text(100),
  "specification" text(500),
  "unit" text(32),
  "res_qty" real(16,6),
  "de_price" real(16,6),
  "market_price" real(16,6),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  "tax_removal" real(16,6),
  "library_code" text(255),
  "kind_sc" text(255),
  "transfer_factor" text(255),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseJxpb {
    pub sequence_nbr: String,
    pub rcj_id: String,
    pub jx_code: Option<String>,
    pub jx_name: Option<String>,
    pub material_code: Option<String>,
    pub kind: Option<i32>,
    pub material_name: Option<String>,
    pub specification: Option<String>,
    pub unit: Option<String>,
    pub res_qty: Option<f64>,
    pub de_price: Option<f64>,
    pub market_price: Option<f64>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
    pub tax_removal: Option<f64>,
    pub library_code: Option<String>,
    pub kind_sc: Option<String>,
    pub transfer_factor: Option<String>,
}
//crud!(BaseJxpb {}, "base_jxpb");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "rcj_id".to_string(),
            "jx_code".to_string(),
            "jx_name".to_string(),
            "material_code".to_string(),
            "kind".to_string(),
            "material_name".to_string(),
            "specification".to_string(),
            "unit".to_string(),
            "res_qty".to_string(),
            "de_price".to_string(),
            "market_price".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "tax_removal".to_string(),
            "library_code".to_string(),
            "kind_sc".to_string(),
            "transfer_factor".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "rcj_id".to_string(),
            "jx_code".to_string(),
            "jx_name".to_string(),
            "material_code".to_string(),
            "kind".to_string(),
            "material_name".to_string(),
            "specification".to_string(),
            "unit".to_string(),
            "res_qty".to_string(),
            "de_price".to_string(),
            "market_price".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "tax_removal".to_string(),
            "library_code".to_string(),
            "kind_sc".to_string(),
            "transfer_factor".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "rcj_id".to_string(),
            "jx_code".to_string(),
            "jx_name".to_string(),
            "material_code".to_string(),
            "kind".to_string(),
            "material_name".to_string(),
            "specification".to_string(),
            "unit".to_string(),
            "res_qty".to_string(),
            "de_price".to_string(),
            "market_price".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "tax_removal".to_string(),
            "library_code".to_string(),
            "kind_sc".to_string(),
            "transfer_factor".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseJxpb>("base_jxpb", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseJxpb>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_jxpb").await?;
    let base_jxpb_index = IndexClient::<BaseJxpb> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_jxpb_index);
    Ok(search_service)
}
