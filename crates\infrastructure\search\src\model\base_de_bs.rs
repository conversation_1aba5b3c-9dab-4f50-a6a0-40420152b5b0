/*
CREATE TABLE "base_de_bs" (
  "sequence_nbr" text(19),
  "classify_level1" text(255),
  "classify_level2" text(255),
  "classify_level3" text(255),
  "classify_level4" text(255),
  "de_id" text(18),
  "de_code" text(255),
  "de_name" text(255),
  "unit" text(255),
  "bs_type" integer(11),
  "up_floor" integer(11),
  "yk_high" integer(11),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  "library_code" text(255)
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeBs {
    pub sequence_nbr: Option<String>,
    pub classify_level1: Option<String>,
    pub classify_level2: Option<String>,
    pub classify_level3: Option<String>,
    pub classify_level4: Option<String>,
    pub de_id: Option<String>,
    pub de_code: Option<String>,
    pub de_name: Option<String>,
    pub unit: Option<String>,
    pub bs_type: Option<i32>,
    pub up_floor: Option<i32>,
    pub yk_high: Option<i32>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
    pub library_code: Option<String>,
}
//crud!(BaseDeBs {}, "base_de_bs");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "classify_level1".to_string(),
            "classify_level2".to_string(),
            "classify_level3".to_string(),
            "classify_level4".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "unit".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "library_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "classify_level1".to_string(),
            "classify_level2".to_string(),
            "classify_level3".to_string(),
            "classify_level4".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "unit".to_string(),
            "bs_type".to_string(),
            "up_floor".to_string(),
            "yk_high".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "library_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "classify_level1".to_string(),
            "classify_level2".to_string(),
            "classify_level3".to_string(),
            "classify_level4".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "unit".to_string(),
            "bs_type".to_string(),
            "up_floor".to_string(),
            "yk_high".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "library_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeBs>("base_de_bs", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeBs>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_bs").await?;
    let base_de_bs_index = IndexClient::<BaseDeBs> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_bs_index);
    Ok(search_service)
}
