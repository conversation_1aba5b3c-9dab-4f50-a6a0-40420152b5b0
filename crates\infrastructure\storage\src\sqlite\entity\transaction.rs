use rbatis::{crud, executor::Executor, table_sync::SqliteTableM<PERSON>per, <PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct Transaction {
    pub id: i64,
    pub state_id: i64,
    pub steps: Vec<u8>,
}

crud!(Transaction {}, "transactions");

pub async fn do_sync_table_transaction(conn: &dyn Executor) {
    let table = Transaction { id: 0, state_id: 0, steps: "blob".to_string().into() };
    let _ = RBatis::sync(conn, &SqliteTableMapper {}, &table, "transactions").await;
}
