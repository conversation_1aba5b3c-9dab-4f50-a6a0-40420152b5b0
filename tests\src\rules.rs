/* use std::sync::Arc;

use price_rules::{
    graph::{DGraph, DGraphConfig},
    model::{DContent, DNode, DNodeKind, Expression, ExpressionNodeContent, FunctionContent, TransformAttributes, TransformExecutionMode},
};

pub fn get_decision_graph() -> DGraph {
    let content = get_decision_graph_content();
    DGraph::try_new(DGraphConfig { trace: true, iteration: 1, max_depth: 10, content: Arc::new(content) }).unwrap()
}
pub fn get_decision_graph_content() -> DContent {
    // 创建节点
    let input_node = DNode { id: "1".to_string(), name: "input".to_string(), kind: DNodeKind::InputNode, depends: vec![] };

    let function_node = DNode {
        id: "f1".to_string(),
        name: "a".to_string(),
        kind: DNodeKind::FunctionNode {
            content: FunctionContent {
                source: r#"
                    export let handler = (input) => {
                        return {value:input.value * 2};
                    }
                "#
                .to_string(),
            },
        },
        depends: vec!["1".to_string()],
    };

    let expression_node = DNode {
        id: "e1".to_string(),
        name: "e".to_string(),
        kind: DNodeKind::ExpressionNode {
            content: ExpressionNodeContent {
                expressions: vec![
                    Expression { id: "11".to_string(), key: "b".to_string(), value: "value + 10".to_string() },
                    Expression { id: "c11".to_string(), key: "c".to_string(), value: "$.b + 10".to_string() },
                    Expression { id: "d11".to_string(), key: "d".to_string(), value: "$.c*10".to_string() },
                ],
                transform_attributes: TransformAttributes {
                    input_field: None,
                    output_path: None,
                    execution_mode: TransformExecutionMode::Single,
                    pass_through: true,
                },
            },
        },
        depends: vec!["f1".to_string()],
    };

    let output_node =
        DNode { id: "output1".to_string(), name: "Output Node".to_string(), kind: DNodeKind::OutputNode, depends: vec!["e1".to_string()] };

    // 创建 DContent

    DContent { nodes: vec![Arc::new(input_node), Arc::new(function_node), Arc::new(expression_node), Arc::new(output_node)] }
}
 */