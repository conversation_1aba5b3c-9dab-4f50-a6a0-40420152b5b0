[package]
name = "price-budget-core"
version = { workspace = true }
edition = { workspace = true }
authors = { workspace = true }

[dependencies]
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }

rayon = { workspace = true }
tempfile = { workspace = true }
bincode = { workspace = true }
dashmap = { workspace = true }   # 高性能并发哈希表
crossbeam = { workspace = true } # 高级并发原语
zstd = { workspace = true }     # 高性能压缩算法
memmap2 = { workspace = true }   # 内存映射文件
crossbeam-deque = { workspace = true } # 高性能工作窃取队列
crossbeam-channel = { workspace = true } # 高性能并发通道
num_cpus = { workspace = true } # CPU核心数检测
async-channel={workspace=true}
async-trait={workspace=true}

price-storage = {workspace=true}
price-rules = {workspace=true}
price-common = {workspace=true}
extension-fbfx-csxm-interface = { workspace = true }
rust_decimal = { version = "1.33", features = ["serde_json"] }

moduforge-state = { workspace = true }
moduforge-model = { workspace = true }
moduforge-core = { workspace = true }
moduforge-macros = { workspace = true }





