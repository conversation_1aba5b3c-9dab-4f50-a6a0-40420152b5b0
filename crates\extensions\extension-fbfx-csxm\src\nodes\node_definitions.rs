use serde::{Deserialize, Serialize};
use mf_derive::Node;

/// 分部节点
/// 用于表示工程项目的分部信息
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "fb"]
#[desc = "分部"]
#[content = "(fb|qd)*"]
pub struct FbNode {
    #[attr]
    pub project_code: String,//项目编码
    
    #[attr]
    pub unit: String,//单位
    
    #[attr]
    pub project_name: String,//项目名称
    
    #[attr]
    pub type_name: String,//类型
    
    #[attr]
    pub project_attr: String,//项目特征
    
    #[attr]
    pub quantity: String,//工程量
    
    #[attr]
    pub quantity_expression: String,//工程量表达式
    
    #[attr(default = 0.0)]
    pub sbf_price: f64,//设备费单价
    
    #[attr(default = 0.0)]
    pub sbf_total: f64,//设备费合价
    
    #[attr(default = 0.0)]
    pub zgf_price: f64,//暂估单价
    
    #[attr(default = 0.0)]
    pub zgf_total: f64,//暂估合价
    
    #[attr(default = 0.0)]
    pub zjf_price: f64,//直接费单价
    
    #[attr(default = 0.0)]
    pub zjf_total: f64,//直接费合价
    
    #[attr(default = 0.0)]
    pub rfee_price: f64,//人工费单价
    
    #[attr(default = 0.0)]
    pub rfee_total: f64,//人工费合价
    
    #[attr(default = 0.0)]
    pub cfee_price: f64,//材料费单价
    
    #[attr(default = 0.0)]
    pub cfee_total: f64,//材料费合价
    
    #[attr(default = 0.0)]
    pub jfee_price: f64,//机械费单价
    
    #[attr(default = 0.0)]
    pub jfee_total: f64,//机械费合价
    
    #[attr(default = 0.0)]
    pub total_rfee: f64,//人工费合价
    
    #[attr(default = 0.0)]
    pub total_cfee: f64,//材料费合价
    
    #[attr(default = 0.0)]
    pub total_jfee: f64,//机械费合价
    
    #[attr(default = 0.0)]
    pub total_profit_fee: f64,//利润费合价
    
    #[attr(default = 0.0)]
    pub total_manager_fee: f64,//管理费合价
    
    #[attr(default = 0.0)]
    pub total_zcfee: f64,//主材费合价
    
    #[attr(default = 0.0)]
    pub price: f64,//单价
    
    #[attr(default = 0.0)]
    pub total: f64,//工程造价合价
    
    #[attr]
    pub gf_price: f64,//规费单价
    
    #[attr(default = 0.0)]
    pub gf_total: f64,//规费合价
    
    #[attr(default = 0.0)]
    pub scgjsyf_price: f64,//生产工具使用费单价
    
    #[attr(default = 0.0)]
    pub scgjsyf_total: f64,//生产工具使用费合价
    
    #[attr(default = 0.0)]
    pub fhddglzjf_price: f64,//繁华地段管理增加费单价
    
    #[attr(default = 0.0)]
    pub fhddglzjf_total: f64,//繁华地段管理增加费合价
    
    #[attr(default = 0.0)]
    pub gjfhf_price: f64,//冬季防寒费单价
    
    #[attr(default = 0.0)]
    pub gjfhf_total: f64,//冬季防寒费合价
    
    #[attr(default = 0.0)]
    pub sdghzjf_price: f64,//山地管护增加费单价
    
    #[attr(default = 0.0)]
    pub sdghzjf_total: f64,//山地管护增加费合价
    
    #[attr(default = 0.0)]
    pub lssgaqfhcsf_price: f64,//绿色施工安全防护措施费单价
    
    #[attr(default = 0.0)]
    pub lssgaqfhcsf_total: f64,//绿色施工安全防护措施费合价
    
    #[attr(default = 0.0)]
    pub jxse_price: f64,//进项税额单价
    
    #[attr(default = 0.0)]
    pub jxse_total: f64,//进项税额合价
    
    #[attr(default = 0.0)]
    pub xxse_price: f64,//销项税额单价
    
    #[attr(default = 0.0)]
    pub xxse_total: f64,//销项税额合价
    
    #[attr(default = 0.0)]
    pub zzsynse_price: f64,//增值税应纳税额单价
    
    #[attr(default = 0.0)]
    pub zzsynse_total: f64,//增值税应纳税额合价
    
    #[attr(default = 0.0)]
    pub fjse_price: f64,//附加税费单价
    
    #[attr(default = 0.0)]
    pub fjse_total: f64,//附加税费合价
    
    #[attr(default = 0.0)]
    pub sqgczj_price: f64,//税前工程造价单价
    
    #[attr(default = 0.0)]
    pub sqgczj_total: f64,//税前工程造价合价
    
    #[attr(default = 0.0)]
    pub fxfy_price: f64,//风险费用单价
    
    #[attr(default = 0.0)]
    pub fxfy_total: f64,//风险费用合价
    
    #[attr(default = 0.0)]
    pub sj_price: f64,//税金单价
    
    #[attr(default = 0.0)]
    pub sj_total: f64,//税金合价

}


/// 清单节点
/// 用于表示工程项目的清单信息
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "qd"]
#[desc = "清单"]
#[content = "(de|dercj)*"]
pub struct QdNode {
    #[attr]
    pub project_code: String,//项目编码
    
    #[attr]
    pub unit: String,//单位
    
    #[attr]
    pub project_name: String,//项目名称
    
    #[attr]
    pub type_name: String,//类型
    
    #[attr]
    pub project_attr: String,//项目特征
    
    #[attr]
    pub quantity: String,//工程量
    
    #[attr]
    pub quantity_expression: String,//工程量表达式
    
    #[attr]
    pub sbf_price: f64,//设备费单价
    
    #[attr]
    pub sbf_total: f64,//设备费合价
    
    #[attr]
    pub zgf_price: f64,//暂估单价
    
    #[attr]
    pub zgf_total: f64,//暂估合价
    
    #[attr]
    pub zjf_price: f64,//直接费单价
    
    #[attr]
    pub zjf_total: f64,//直接费合价
    
    #[attr]
    pub rfee_price: f64,//人工费单价
    
    #[attr]
    pub rfee_total: f64,//人工费合价
    
    #[attr]
    pub cfee_price: f64,//材料费单价
    
    #[attr]
    pub cfee_total: f64,//材料费合价
    
    #[attr]
    pub jfee_price: f64,//机械费单价
    
    #[attr]
    pub jfee_total: f64,//机械费合价
    
    #[attr]
    pub total_rfee: f64,//人工费合价
    
    #[attr]
    pub total_cfee: f64,//材料费合价
    
    #[attr]
    pub total_jfee: f64,//机械费合价
    
    #[attr]
    pub total_profit_fee: f64,//利润费合价
    
    #[attr]
    pub total_manager_fee: f64,//管理费合价
    
    #[attr]
    pub total_zcfee: f64,//主材费合价       
    
    #[attr]
    pub price: f64,//单价
    
    #[attr]
    pub total: f64,//工程造价合价
    
    #[attr]
    pub gf_price: f64,//规费单价
    
    #[attr]
    pub gf_total: f64,//规费合价
    
    #[attr]
    pub scgjsyf_price: f64,//生产工具使用费单价
    
    #[attr]
    pub scgjsyf_total: f64,//生产工具使用费合价
    
    #[attr]
    pub fhddglzjf_price: f64,//繁华地段管理增加费单价
    
    #[attr]
    pub fhddglzjf_total: f64,//繁华地段管理增加费合价
    
    #[attr]
    pub gjfhf_price: f64,//冬季防寒费单价
    
    #[attr]
    pub gjfhf_total: f64,//冬季防寒费合价
    
    #[attr]
    pub sdghzjf_price: f64,//山地管护增加费单价
    
    #[attr]
    pub sdghzjf_total: f64,//山地管护增加费合价
    
    #[attr]
    pub lssgaqfhcsf_price: f64,//绿色施工安全防护措施费单价   
    
    #[attr]
    pub lssgaqfhcsf_total: f64,//绿色施工安全防护措施费合价
    
    #[attr]
    pub jxse_price: f64,//进项税额单价
    
    #[attr]
    pub jxse_total: f64,//进项税额合价
    
    #[attr]
    pub xxse_price: f64,//销项税额单价
    
    #[attr]
    pub xxse_total: f64,//销项税额合价
    
    #[attr]
    pub zzsynse_price: f64,//增值税应纳税额单价
    
    #[attr]
    pub zzsynse_total: f64,//增值税应纳税额合价
    
    #[attr]
    pub fjse_price: f64,//附加税费单价
    
    #[attr]
    pub fjse_total: f64,//附加税费合价
    
    #[attr]
    pub sqgczj_price: f64,//税前工程造价单价
    
    #[attr]
    pub sqgczj_total: f64,//税前工程造价合价
    
    #[attr]
    pub fxfy_price: f64,//风险费用单价  
    
    #[attr]
    pub fxfy_total: f64,//风险费用合价
    
    #[attr]
    pub sj_price: f64,//税金单价
    
    #[attr]
    pub sj_total: f64,//税金合价
}


/// 定额节点
/// 用于表示工程项目的定额信息
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "de"]
#[desc = "定额"]
pub struct DeNode {
    #[attr]
    pub project_code: String,//项目编码
    
    #[attr]
    pub unit: String,//单位
    
    #[attr]
    pub project_name: String,//项目名称
    
    #[attr]
    pub type_name: String,//类型
    
    #[attr]
    pub project_attr: String,//项目特征
    
    #[attr]
    pub quantity: String,//工程量
    
    #[attr]
    pub quantity_expression: String,//工程量表达式
    
    #[attr]
        pub sbf_price: f64,//设备费单价
    
    #[attr]
    pub sbf_total: f64,//设备费合价
    
    #[attr]
    pub zgf_price: f64,//暂估单价
    
    #[attr]
    pub zgf_total: f64,//暂估合价
    
    #[attr]
    pub zjf_price: f64,//直接费单价
    
    #[attr]
    pub zjf_total: f64,//直接费合价
    
    #[attr]
    pub rfee_price: f64,//人工费单价
    
    #[attr]
    pub rfee_total: f64,//人工费合价
    
    #[attr]
    pub cfee_price: f64,//材料费单价
    
    #[attr]
    pub cfee_total: f64,//材料费合价
    
    #[attr]
    pub jfee_price: f64,//机械费单价
    
    #[attr]
    pub jfee_total: f64,//机械费合价
    
    #[attr]
    pub total_rfee: f64,//人工费合价
    
    #[attr]
    pub total_cfee: f64,//材料费合价
    
    #[attr]
    pub total_jfee: f64,//机械费合价
    
    #[attr]
    pub total_profit_fee: f64,//利润费合价
    
    #[attr]
    pub total_manager_fee: f64,//管理费合价     
    
    #[attr]
    pub total_zcfee: f64,//主材费合价
    
    #[attr]
    pub price: f64,//单价
    
    #[attr]
    pub total: f64,//工程造价合价
    
    #[attr]
    pub gf_price: f64,//规费单价
    
    #[attr]
    pub gf_total: f64,//规费合价
    
    #[attr]
    pub scgjsyf_price: f64,//生产工具使用费单价
    
    #[attr]
    pub scgjsyf_total: f64,//生产工具使用费合价
    
    #[attr]
    pub fhddglzjf_price: f64,//繁华地段管理增加费单价
    
    #[attr]
    pub fhddglzjf_total: f64,//繁华地段管理增加费合价
    
    #[attr]
    pub gjfhf_price: f64,//冬季防寒费单价       
    
    #[attr]
    pub gjfhf_total: f64,//冬季防寒费合价
    
    #[attr]
    pub sdghzjf_price: f64,//山地管护增加费单价
    
    #[attr]
    pub sdghzjf_total: f64,//山地管护增加费合价
    
    #[attr]
    pub lssgaqfhcsf_price: f64,//绿色施工安全防护措施费单价
    
    #[attr]
    pub lssgaqfhcsf_total: f64,//绿色施工安全防护措施费合价
    
    #[attr]
    pub jxse_price: f64,//进项税额单价
    
    #[attr]
    pub jxse_total: f64,//进项税额合价
    
    #[attr]
    pub xxse_price: f64,//销项税额单价
    
    #[attr]
    pub xxse_total: f64,//销项税额合价
    
    #[attr]
    pub zzsynse_price: f64,//增值税应纳税额单价
    
    #[attr]
    pub zzsynse_total: f64,//增值税应纳税额合价
    
    #[attr]
    pub fjse_price: f64,//附加税费单价
    
    #[attr]
    pub fjse_total: f64,//附加税费合价
    
    #[attr]
    pub sqgczj_price: f64,//税前工程造价单价
    
    #[attr]
    pub sqgczj_total: f64,//税前工程造价合价
    
    #[attr]
    pub fxfy_price: f64,//风险费用单价
    
    #[attr]
    pub fxfy_total: f64,//风险费用合价
    
    #[attr]
    pub sj_price: f64,//税金单价
    
    #[attr]
    pub sj_total: f64,//税金合价
}


/// 定额人材机节点
/// 用于表示定额下的人材机信息
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "dercj"]
#[desc = "定额_人材机"]
pub struct DeRcjNode {
    #[attr]
    pub project_code: String,
    
    #[attr]
    pub unit: String,
    
    #[attr]
    pub project_name: String,
    
    #[attr]
    pub type_name: String,
    
    #[attr]
    pub project_attr: String,
    
    #[attr]
    pub quantity: String,
    
    #[attr]
    pub quantity_expression: String,
    
    #[attr]
    pub sbf_price: f64,
    
    #[attr]
    pub sbf_total: f64,
    
    #[attr]
    pub zgf_price: f64,
    
    #[attr]
    pub zgf_total: f64,
    
    #[attr]
    pub zjf_price: f64,
    
    #[attr]
    pub zjf_total: f64,
    
    #[attr]
    pub rfee_price: f64,
    
    #[attr]
    pub rfee_total: f64,
    
    #[attr]
    pub cfee_price: f64,
    
    #[attr]
    pub cfee_total: f64,
    
    #[attr]
    pub jfee_price: f64,
    
    #[attr]
    pub jfee_total: f64,
    
    #[attr]
    pub total_rfee: f64,
    
    #[attr]
    pub total_cfee: f64,
    
    #[attr]
    pub total_jfee: f64,
    
    #[attr]
    pub total_profit_fee: f64,
    
    #[attr]
    pub total_manager_fee: f64,
    
    #[attr]
    pub total_zcfee: f64,
    
    #[attr]
    pub price: f64,
    
    #[attr]
    pub total: f64,
    
    #[attr]
    pub gf_price: f64,
    
    #[attr]
    pub gf_total: f64,
    
    #[attr]
    pub scgjsyf_price: f64,
    
    #[attr]
    pub scgjsyf_total: f64,
    
    #[attr]
    pub fhddglzjf_price: f64,
    
    #[attr]
    pub fhddglzjf_total: f64,
    
    #[attr]
    pub gjfhf_price: f64,
    
    #[attr]
    pub gjfhf_total: f64,
    
    #[attr]
    pub sdghzjf_price: f64,
    
    #[attr]
    pub sdghzjf_total: f64,
    
    #[attr]
    pub lssgaqfhcsf_price: f64,
    
    #[attr]
    pub lssgaqfhcsf_total: f64,
    
    #[attr]
    pub jxse_price: f64,
    
    #[attr]
    pub jxse_total: f64,
    
    #[attr]
    pub xxse_price: f64,
    
    #[attr]
    pub xxse_total: f64,
    
    #[attr]
    pub zzsynse_price: f64,
    
    #[attr]
    pub zzsynse_total: f64,
    
    #[attr]
    pub fjse_price: f64,
    
    #[attr]
    pub fjse_total: f64,
    
    #[attr]
    pub sqgczj_price: f64,
    
    #[attr]
    pub sqgczj_total: f64,
    
    #[attr]
    pub fxfy_price: f64,
    
    #[attr]
    pub fxfy_total: f64,
    
    #[attr]
    pub sj_price: f64,
    
    #[attr]
    pub sj_total: f64,
}


/// 分部分项节点
/// 用于表示分部分项的顶层结构
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "fbfx"]
#[desc = "分部分项"]
#[content = "(fb|qd)+"]
pub struct FbfxNode {
    #[attr]
    pub section_name: String,
    
    #[attr]
    pub section_code: String,
    
    #[attr]
    pub total_amount: f64,
    
    #[attr]
    pub total_labor_cost: f64,
    
    #[attr]
    pub total_material_cost: f64,
    
    #[attr]
    pub total_machinery_cost: f64,
}

/// 措施项目节点
/// 用于表示措施项目的顶层结构
#[derive(Node, Debug, Clone, Serialize, Deserialize)]
#[node_type = "csxm"]
#[desc = "措施项目"]
#[content = "(fb|qd)+"]
pub struct CsxmNode {
    #[attr]
    pub measure_name: String,
    
    #[attr]
    pub measure_code: String,
    
    #[attr]
    pub measure_type: String,
    
    #[attr]
    pub total_amount: f64,
    
    #[attr]
    pub total_labor_cost: f64,
    
    #[attr]
    pub total_material_cost: f64,
    
    #[attr]
    pub total_machinery_cost: f64,
}

/// 分部分项和措施项目工厂
pub struct FbfxCsxmFactory;

impl FbfxCsxmFactory {
    /// 创建分部分项节点结构
    pub fn create_fbfx_structure() -> Vec<mf_core::node::Node> {
        let fbfx = FbfxNode::node_definition();
        let fb = FbNode::node_definition();
        let qd = QdNode::node_definition();
        let de = DeNode::node_definition();
        let de_rcj = DeRcjNode::node_definition();
        
        vec![
            fbfx,
            fb,
            qd,
            de,
            de_rcj,
        ]
    }
    
    /// 创建措施项目节点结构
    pub fn create_csxm_structure() -> Vec<mf_core::node::Node> {
        let csxm = CsxmNode::node_definition();
        let fb = FbNode::node_definition();
        let qd = QdNode::node_definition();
        let de = DeNode::node_definition();
        let de_rcj = DeRcjNode::node_definition();
        
        vec![
            csxm,
            fb,
            qd,
            de,
            de_rcj,
        ]
    }
    
    /// 创建完整的分部分项和措施项目结构
    pub fn create_complete_structure() -> Vec<mf_core::node::Node> {
        let mut nodes = Self::create_fbfx_structure();
        nodes.extend(Self::create_csxm_structure());
        nodes
    }
}