# 工程造价计价软件详情业务说明

## 1. 控制台项目创建详细业务

### 1.1 地区选择业务逻辑

**地区选择数据结构**
```
地区配置表 (region_config)
├── region_id (地区唯一标识，如130100表示石家庄)
├── region_name (地区名称，如"河北省石家庄市")  
├── parent_region_id (上级地区ID，130000表示河北省)
├── region_level (层级：1省份/2地市/3区县)
├── available_standards (可用标准库列表JSON)
├── default_valuation_table (默认估价表ID)
└── is_active (是否启用)
```

**标准库自动加载规则**

- 选择河北省任意地市 → 自动加载GB50500-2013、GB/T50500-2024清单标准
- 选择河北省任意地市 → 自动加载河北12定额、河北22定额标准  
- 选择石家庄/沧州/保定/秦皇岛 → 额外加载对应地区估价表
- 选择其他地市 → 仅加载省站统一规则，不显示估价表选项

### 1.2 文件类型选择业务差异

**招标文件创建**
- **业务特点**：编制招标控制价，作为投标报价上限
- **数据要求**：必须按照现行定额标准计算，不允许价格调整
- **报表格式**：生成招标文件要求的标准格式报表
- **审核流程**：需要内部审核和造价咨询机构审核两级流程

**投标文件创建**  
- **业务特点**：投标人编制报价，在控制价范围内竞争
- **数据要求**：可适度调整材料价格和人工费，但需要合理性说明
- **报表格式**：生成投标文件要求的报价表格式
- **商务策略**：支持不平衡报价策略和风险费用考虑

**单位工程文件创建**
- **业务特点**：单独的单位工程造价编制，适用于独立核算
- **数据要求**：完整的工程量清单和组价计算
- **应用场景**：工程变更、专项工程、分期建设项目
- **数据关联**：可作为总项目的子模块，也可独立存在

**定额工料机法文件创建**
- **业务特点**：按照传统定额计价模式，不使用清单计价
- **数据结构**：直接使用定额项目，不需要清单项目层
- **适用场景**：政府投资项目、内部成本控制、历史项目对比
- **计算逻辑**：定额直接费+间接费+利润+税金的传统计算模式

### 1.3 费用计算模式业务差异

**全费用单价模式业务规则**
```
清单项目综合单价包含内容：
├── 人工费 (定额人工消耗量 × 人工单价)
├── 材料费 (定额材料消耗量 × 材料单价) 
├── 机械费 (定额机械消耗量 × 机械台班费)
├── 企业管理费 (直接费 × 管理费率)
├── 利润 (直接费 × 利润率)  
├── 措施费分摊 (按分摊比例计入单价)
├── 安文费分摊 (安全文明施工费分摊)
├── 规费 (仅12定额标准包含)
└── 增值税 (全部费用 × 9%税率)

项目总造价 = Σ(清单工程量 × 全费用综合单价)
```

**清单计价模式业务规则**
```  
清单项目综合单价包含内容：
├── 人工费 (定额人工消耗量 × 人工单价)
├── 材料费 (定额材料消耗量 × 材料单价)
├── 机械费 (定额机械消耗量 × 机械台班费) 
├── 企业管理费 (直接费 × 管理费率)
└── 利润 (直接费 × 利润率)

项目总造价计算：
分部分项工程费 = Σ(清单工程量 × 清单综合单价)
措施项目费 = 分部分项工程费 × 措施费综合费率
其他项目费 = 零星工作费 + 计日工费 + 总承包服务费
规费 = (分部分项+措施+其他) × 规费费率
增值税 = (分部分项+措施+其他+规费) × 9%
工程总造价 = 分部分项+措施+其他+规费+增值税
```

## 2. 工作台页面业务功能

### 2.1 窗体结构业务设计

**顶部系统功能区**
```
软件Logo | 保存 | 撤销重做 | 成果文件路径显示 | 用户信息 | 窗体控制
```
- **保存功能**：自动保存+手动保存，显示最后保存时间
- **撤销重做**：支持最近50步操作的撤销重做
- **文件路径显示**：显示当前项目完整路径和文件名
- **用户信息**：显示当前用户、权限级别、在线状态

**功能识别标识区**
- **业务类型标识**：概算/预算/结算/审核，影响可用功能菜单
- **文件类型标识**：招标/投标/单位工程/工料机法，影响数据结构和计算规则
- **标准版本显示**：当前使用的清单标准、定额标准版本信息

### 2.2 主菜单功能业务逻辑

**文件菜单业务功能**
- **项目操作**：新建、打开、另存为、关闭项目
- **转换功能**：预算转结算、概算转预算、标准版本升级
- **设置管理**：项目设置、用户偏好、标准库路径设置
- **备份恢复**：项目备份、自动恢复、版本历史管理

**编制菜单业务功能**  
- **页面内容切换**：根据选择的工程结构层级动态加载编制界面
- **业务功能区**：显示当前页签对应的业务操作按钮
- **数据验证**：实时数据校验和业务规则检查
- **批量操作**：批量导入、批量调价、批量计算功能

**报表菜单业务功能**
- **报表模板库**：根据文件类型显示对应的报表模板列表
- **数据渲染**：根据实际工作台数据实时渲染报表内容
- **格式调整**：支持报表行列结构的自定义调整
- **父子关系设计**：支持主表明细表的层级报表结构设计

**电子标菜单业务功能**
- **格式导出**：支持广联达、同望、神机妙算等主流厂商XML格式
- **数据校验**：导出前进行数据完整性和格式规范性校验  
- **电子签章**：集成CA证书进行电子签章和数据加密
- **平台对接**：支持河北省公共资源交易平台等主流招投标平台

### 2.3 工程结构树业务管理

**层级业务规则**
```
工程项目层级 (根节点)
├── 业务属性：项目总体信息、总投资、建设周期
├── 显示内容：工程概况、造价分析、取费表、人材机汇总
├── 汇总规则：所有下级单项和单位工程的数据汇总
└── 操作权限：项目经理级别用户可编辑

单项工程层级 (中间节点)  
├── 业务属性：单项工程信息、分项投资、技术特点
├── 显示内容：工程概况、造价分析、取费表、人材机汇总
├── 汇总规则：所有下级单位工程的数据汇总
├── 嵌套支持：支持子单项工程，最多3级嵌套
└── 操作权限：专业负责人级别用户可编辑

单位工程层级 (叶子节点)
├── 业务属性：单位工程详细信息、专业类型、技术参数
├── 显示内容：工程概况、造价分析、取费表、分部分项、措施项目、人材机汇总、其他项目、费用汇总
├── 数据生产：实际清单项目和定额数据的录入层级
└── 操作权限：造价工程师级别用户可编辑
```

**节点状态管理**
- **进度状态**：未开始(灰色)、进行中(蓝色)、已完成(绿色)、异常(红色)
- **锁定状态**：编辑中(锁定图标)、审核中(审核图标)、已确认(确认图标)
- **统计信息**：每个节点显示包含的清单项目数、总金额、完成进度

## 3. 分部分项编制详细业务

### 3.1 编辑区业务结构

**上半部分：清单定额数据表格**
```
表格列结构业务定义：
├── 序号 (自动递增，不可编辑)
├── 项目编码 (12位清单编码，支持选择器)
├── 类型 (单位工程/分部/清单/定额，自动识别)
├── 项目名称 (根据编码自动带出，可修改)
├── 项目特征 (详细规格描述，影响组价选择)
├── 规格型号 (技术参数，影响材料选择)
├── 单位 (计量单位，与编码关联)
├── 工程量表达式 (支持公式，如"50*20*0.3")
├── 单价 (综合单价，自动计算可调整)
├── 合价 (工程量×单价，自动计算)
├── 综合单价 (区分全费用/非全费用单价模式)
├── 综合合价 (区分全费用/非全费用单价模式)
├── 取费专业 (一类/二类/三类工程，影响费率)
├── 单价构成文件 (关联的单价构成模板)
└── 施工组织措施类别 (影响措施费计取)
```

**数据层级展示规则**
- **单位工程行**：显示单位工程汇总数据，背景色区分，不可删除
- **分部行**：显示分部工程汇总，支持最多四级分部，可展开折叠
- **清单行**：显示具体清单项目数据，白色背景，可编辑
- **定额行**：显示定额子目数据，浅灰背景，缩进显示

### 3.2 下半部分明细区业务功能

**人材机明细页签**
```
人材机明细表 (labor_material_machine_detail)
├── 资源类型 (人工P/材料M/机械T)
├── 资源编码 (标准库统一编码)
├── 资源名称 (人工工种、材料品种、机械型号)
├── 规格型号 (详细技术参数)
├── 计量单位 (工日、m³、台班等)
├── 定额消耗量 (定额标准规定的消耗量)
├── 调整消耗量 (根据实际情况调整后的消耗量)
├── 基础价格 (定额标准基础价格)
├── 市场价格 (当期市场信息价)
├── 实际价格 (实际采用的计算价格)
└── 小计金额 (调整消耗量×实际价格)
```

**业务操作规则**
- 选中清单行：显示该清单项目关联的所有定额的人材机汇总
- 选中定额行：显示该定额的具体人材机消耗明细，支持编辑
- 价格调整：支持单项调整和批量调整，需记录调整原因
- 消耗量调整：支持含量调整，自动重新计算单价

**单价构成页签**
- **费用构成展示**：人工费、材料费、机械费、管理费、利润等分项
- **费率显示**：当前适用的各项费率标准和取费依据  
- **计算过程**：显示从基础费用到综合单价的完整计算过程
- **调整记录**：记录所有人工调整的历史和原因

**标准换算页签**  
- **换算规则库**：显示适用的标准换算规则和系数
- **自定义换算**：支持用户自定义换算规则和系数
- **换算历史**：记录换算操作的历史和依据
- **影响分析**：显示换算操作对价格的影响程度

**换算信息页签**
- **换算依据**：显示换算的标准依据和条文引用
- **换算说明**：详细的换算原因和技术说明
- **审核状态**：换算操作的审核状态和审核意见
- **关联影响**：显示换算对其他相关项目的影响

**特征及内容页签**
- **项目特征描述**：详细的项目技术特征和施工要求
- **设计变更影响**：记录设计变更对项目特征的影响
- **施工条件说明**：影响组价选择的施工条件描述
- **风险因素识别**：识别影响造价的风险因素

**工程量明细页签**
- **计算过程记录**：工程量计算的详细过程和公式
- **图纸来源**：工程量数据的图纸依据和图号索引
- **测量记录**：现场实测数据记录(结算阶段使用)
- **变更跟踪**：工程量变更的历史记录和原因

### 3.3 数据录入业务流程

**清单项目录入流程**
1. **编码录入**：手动输入或选择器选择12位清单编码
2. **信息自动带出**：根据编码从清单标准库带出项目名称、单位
3. **特征描述**：根据实际工程情况填写项目特征描述
4. **工程量录入**：直接录入数值或使用公式计算器
5. **验证提示**：实时校验编码格式、工程量合理性

**定额子目配置流程**
1. **智能匹配**：根据清单编码自动推荐适用的定额子目
2. **定额选择**：从推荐列表中选择或手动搜索定额
3. **含量确认**：确认定额消耗量，支持根据实际情况调整
4. **组价计算**：自动计算该定额子目的费用贡献
5. **人材机关联**：自动带出定额的人材机消耗明细数据

## 4. 业务数据关联关系

### 4.1 标准库业务关联

**清单定额关联矩阵**
```
清单标准 × 定额标准兼容性：
GB50500-2013 × 河北12定额 = 完全兼容
GB50500-2013 × 河北22定额 = 完全兼容  
GB/T50500-2024 × 河北22定额 = 完全兼容
GB/T50500-2024 × 河北12定额 = 不兼容 (技术标准不匹配)
```

**估价表业务关联**
```
定额标准 × 估价表关联：
河北22定额 × 省站规则 = 默认标准，无价格调整
河北22定额 × 沧州估价表 = 人工费调整+5%，材料费地方价
河北22定额 × 保定估价表 = 人工费调整+3%，机械费调整+2%
河北22定额 × 秦皇岛估价表 = 综合调整系数1.08
河北12定额 × 任何估价表 = 不适用 (12定额使用省站统一规则)
```

### 4.2 项目数据业务关联

**工程结构业务关联**
```
项目数据层级关联：
工程项目 (1) → 单项工程 (N) → 单位工程 (N) → 分部工程(N) → 清单项目(N) → 定额子目(N)

数据汇总关联：  
定额子目费用 → 汇总至清单项目合价
清单项目合价 → 汇总至分部工程费用
分部工程费用 → 汇总至单位工程费用
单位工程费用 → 汇总至单项工程费用  
单项工程费用 → 汇总至工程项目总造价
```

**费用计算业务关联**
```
基础数据关联：
人材机基础价格 → 定额子目单价计算
定额子目单价 → 清单综合单价计算  
清单综合单价 → 分部分项工程费汇总
分部分项工程费 → 措施费、规费、税金计算基数

费率标准关联：
企业资质等级 → 管理费率、利润费率
工程类别等级 → 取费专业、费率调整系数
地区标准 → 地区调整系数、特殊费用标准
```

### 4.3 业务状态流转关联

**编制状态业务流转**
```
项目状态流转：
新建 → 编制中 → 内审 → 外审 → 确认 → 归档

数据锁定关联：
编制状态 = 允许所有编辑操作
审核状态 = 仅允许查看和批注  
确认状态 = 数据完全锁定，需解锁后才能修改
归档状态 = 只读状态，仅供查询和报表生成
```

**用户权限业务关联**
- **项目经理**：项目级别的创建、配置、状态管控权限
- **专业负责人**：单项工程级别的编辑和审核权限  
- **造价工程师**：单位工程和清单定额的编辑录入权限
- **审核人员**：所有层级的查看权限和审核批注权限

## 5. 关键业务场景

### 5.1 新建项目完整业务场景

**场景一：招标文件编制**
```
业务流程：
1. 控制台选择河北省石家庄市
2. 选择预算业务类型  
3. 选择招标文件创建
4. 弹窗填写：
   - 项目名称：石家庄市某住宅小区工程
   - 项目编码：SJZ2025001 (自动生成)
   - 建设单位：石家庄某房地产公司
   - 清单标准：GB50500-2013  
   - 定额标准：河北22定额
   - 估价表：省站规则
   - 计价模式：清单计价模式
5. 系统校验：13版清单+22版定额+清单计价模式 = 兼容
6. 创建项目文件结构，跳转工作台
```

**场景二：投标文件编制**
```
业务流程：  
1. 控制台选择河北省沧州市
2. 选择预算业务类型
3. 选择投标文件创建
4. 弹窗填写：
   - 项目名称：沧州市某工业厂房工程
   - 项目编码：CZ2025002
   - 建设单位：沧州某制造企业
   - 清单标准：GB/T50500-2024
   - 定额标准：河北22定额  
   - 估价表：沧州估价表
   - 计价模式：全费用单价模式
5. 系统校验：24版清单+22版定额+沧州估价表+全费用单价 = 兼容
6. 加载沧州地区价格调整系数，创建项目
```

### 5.2 分部分项编制业务场景

**场景一：建筑工程清单录入**
```
操作流程：
1. 选中"建筑工程"单位工程节点
2. 切换到"分部分项"页签
3. 在编辑区上半部分新增清单行
4. 录入清单编码：010101001 (挖基础土方)
5. 系统自动带出：项目名称、计量单位(m³)
6. 填写项目特征：机械挖土，平均深度2.5m
7. 录入工程量：1500 (或使用计算器：50*20*1.5)
8. 双击定额编码列，调用定额选择器
9. 选择推荐定额：A1-1 挖基础土方
10. 系统自动计算综合单价并显示在单价列
11. 在下半部分人材机明细页签查看费用构成明细
```

**场景二：定额组价调整**  
```
调整流程：
1. 选中已配置定额的清单行
2. 在人材机明细页签中选择需要调整的材料
3. 修改材料价格：水泥从450元/t调整为480元/t
4. 选择调整原因：市场价格波动  
5. 系统重新计算：
   - 定额子目单价：原85.6元/m³ → 新87.2元/m³
   - 清单综合单价：原125.8元/m³ → 新128.1元/m³
   - 清单合价：原188,700元 → 新192,150元
6. 价格调整记录自动保存到换算信息页签
7. 触发上级汇总数据重新计算
```

### 5.3 费用汇总业务场景

**全费用单价模式汇总**
```
计算业务流程：
1. 获取所有清单项目的综合合价
2. 分部分项工程费 = Σ(清单工程量 × 全费用综合单价)
3. 措施项目费 = 已包含在综合单价中，不重复计算
4. 其他项目费 = 单独计算的零星工作等费用
5. 工程总造价 = 分部分项工程费 + 其他项目费
```

**清单计价模式汇总**
```
计算业务流程：
1. 分部分项工程费 = Σ(清单工程量 × 清单综合单价)
2. 措施项目费计算：
   - 技术措施费 = 分部分项工程费 × 技术措施费率
   - 通用措施费 = 分部分项工程费 × 通用措施费率  
   - 安全文明施工费 = 分部分项工程费 × 安文费率
3. 其他项目费 = 零星工作 + 计日工 + 总承包服务费
4. 规费计算 (仅12定额)：
   - 社会保障费 = (分部分项+措施+其他) × 社保费率
   - 住房公积金 = (分部分项+措施+其他) × 公积金费率
5. 增值税 = (分部分项+措施+其他+规费) × 9%
6. 工程总造价 = 分部分项+措施+其他+规费+增值税
```

## 6. 数据校验业务规则

### 6.1 编码规范校验

**清单编码校验规则**
- **格式校验**：必须为12位数字，格式为XXX XXX XXX XXX
- **标准校验**：编码必须存在于选定的清单标准库中
- **层级校验**：前6位确定分部分项类别，后6位为具体项目
- **唯一性校验**：同一单位工程内不允许重复编码

**定额编码校验规则**  
- **格式校验**：符合河北省定额编码规则，如A1-1-1格式
- **专业匹配**：定额专业必须与清单项目专业一致
- **版本校验**：定额编码必须存在于选定的定额标准版本中
- **适用性校验**：定额适用条件必须与项目特征匹配

### 6.2 工程量校验规则

**数值合理性校验**
- **范围校验**：工程量必须大于0，不能为负数或空值
- **精度校验**：小数位数不超过4位，符合计量精度要求
- **量纲校验**：工程量单位必须与清单项目单位一致
- **异常预警**：工程量异常偏大(>均值3倍)或偏小(<均值0.1倍)时预警

**逻辑关系校验**
- **关联项目校验**：砌体工程量与砂浆工程量的逻辑关系
- **构造关系校验**：基础与上部结构工程量的合理性关系
- **专业配合校验**：建筑与安装工程量的配合关系
- **时序关系校验**：不同施工阶段工程量的时序逻辑

### 6.3 价格异常校验规则

**价格区间校验**
- **历史价格对比**：与历史同类项目价格进行对比分析
- **地区价格对比**：与同地区类似项目价格水平对比
- **市场价格对比**：与当期材料信息价进行对比校验
- **预警阈值**：价格波动超过±15%时给出预警提示

**构成比例校验**
- **人材机比例**：人工费、材料费、机械费的合理构成比例
- **费用比例**：管理费、利润占直接费的比例合理性
- **专业对比**：不同专业工程的单方造价合理性对比
- **规模效应**：不同工程规模的造价指标合理性分析

## 7. 业务异常处理

### 7.1 标准兼容性异常

**兼容性冲突处理**
- **版本不匹配**：清单标准与定额标准版本发布时间不匹配
- **技术标准冲突**：新版清单标准与旧版定额的技术条文冲突
- **地区适用性**：估价表与定额标准的地区适用范围不一致
- **处理策略**：提供替代方案建议，记录兼容性风险，用户确认后继续

### 7.2 数据完整性异常

**缺失数据处理**
- **清单缺少定额**：清单项目未配置定额子目时的提示和处理
- **定额缺少价格**：定额子目缺少人材机价格时的补充机制
- **工程量为空**：工程量未录入时的标识和批量检查功能
- **特征描述不完整**：项目特征描述不完整时的提醒和模板推荐

### 7.3 计算异常处理

**计算错误处理**
- **除零错误**：工程量为0时避免单价计算异常
- **溢出错误**：超大金额数据的处理和精度保持  
- **循环依赖**：费用计算中的循环依赖检测和断链处理
- **精度丢失**：多次计算累积误差的控制和修正

---

**文档版本**：V1.0  
**编制日期**：2025年8月28日  
**关联文档**：业务框架.md、交互说明.md  
**适用范围**：工程造价计价软件详细业务设计