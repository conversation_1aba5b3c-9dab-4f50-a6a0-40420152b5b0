use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};
#[derive(Debug, Serialize, Deserialize)]
pub struct BaseAnzhuangRate {
    pub sequence_nbr: String,
    pub fee_code: Option<String>,
    pub fee_name: Option<String>,
    pub library_code: Option<String>,
    pub class_level1: Option<String>,
    pub class_level2: Option<String>,
    pub de_code: Option<String>,
    pub de_name: Option<String>,
    pub allocation_method: Option<String>,
    pub calculate_base: Option<String>,
    pub rate: Option<String>,
    pub r_rate: Option<String>,
    pub c_rate: Option<String>,
    pub j_rate: Option<String>,
    pub relation_list: Option<String>,
    pub relation_list_id: Option<String>,
    pub creat_date: Option<String>,
    pub layer_interval: Option<String>,
    pub height_range: Option<String>,
    pub is_default: Option<String>,
    pub sort_number: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseAnzhuangRate {}, "base_anzhuang_rate");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "fee_code".to_string(),
            "fee_name".to_string(),
            "library_code".to_string(),
            "class_level1".to_string(),
            "class_level2".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "allocation_method".to_string(),
            "calculate_base".to_string(),
            "rate".to_string(),
            "r_rate".to_string(),
            "c_rate".to_string(),
            "j_rate".to_string(),
            "relation_list".to_string(),
            "relation_list_id".to_string(),
            "creat_date".to_string(),
            "layer_interval".to_string(),
            "height_range".to_string(),
            "is_default".to_string(),
            "sort_number".to_string(),
            "rec_user_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "fee_code".to_string(),
            "fee_name".to_string(),
            "library_code".to_string(),
            "class_level1".to_string(),
            "class_level2".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "allocation_method".to_string(),
            "calculate_base".to_string(),
            "rate".to_string(),
            "r_rate".to_string(),
            "c_rate".to_string(),
            "j_rate".to_string(),
            "relation_list".to_string(),
            "relation_list_id".to_string(),
            "creat_date".to_string(),
            "layer_interval".to_string(),
            "height_range".to_string(),
            "is_default".to_string(),
            "sort_number".to_string(),
            "rec_user_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "fee_code".to_string(),
            "fee_name".to_string(),
            "library_code".to_string(),
            "class_level1".to_string(),
            "class_level2".to_string(),
            "de_code".to_string(),
            "de_name".to_string(),
            "allocation_method".to_string(),
            "calculate_base".to_string(),
            "rate".to_string(),
            "r_rate".to_string(),
            "c_rate".to_string(),
            "j_rate".to_string(),
            "relation_list".to_string(),
            "relation_list_id".to_string(),
            "creat_date".to_string(),
            "layer_interval".to_string(),
            "height_range".to_string(),
            "is_default".to_string(),
            "sort_number".to_string(),
            "rec_user_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseAnzhuangRate>("base_anzhuang_rate", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseAnzhuangRate>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_anzhuang_rate").await?;
    let base_anzhuang_rate_index = IndexClient::<BaseAnzhuangRate> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_anzhuang_rate_index);
    Ok(search_service)
}
