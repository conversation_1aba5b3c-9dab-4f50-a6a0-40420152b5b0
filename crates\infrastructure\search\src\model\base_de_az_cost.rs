/*
CREATE TABLE "base_de_az_cost" (
  "sequence_nbr" varchar PRIMARY KEY NOT NULL,
  "de_code" varchar,
  "cg_name" varchar,
  "major" varchar,
  "storey" varchar,
  "meter" varchar,
  "is_default" integer,
  "rec_user_code" varchar,
  "rec_status" varchar DEFAULT ('A'),
  "rec_date" varchar,
  "extend1" varchar,
  "extend2" varchar,
  "extend3" varchar,
  "description" varchar
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeAzCost {
    pub sequence_nbr: String,
    pub de_code: Option<String>,
    pub cg_name: Option<String>,
    pub major: Option<String>,
    pub storey: Option<String>,
    pub meter: Option<String>,
    pub is_default: Option<i32>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}

//crud!(BaseDeAzCost {}, "base_de_az_cost");

pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "de_code".to_string(),
            "cg_name".to_string(),
            "major".to_string(),
            "storey".to_string(),
            "meter".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "de_code".to_string(),
            "cg_name".to_string(),
            "major".to_string(),
            "storey".to_string(),
            "meter".to_string(),
            "is_default".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "de_code".to_string(),
            "cg_name".to_string(),
            "major".to_string(),
            "storey".to_string(),
            "meter".to_string(),
            "is_default".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeAzCost>("base_de_az_cost", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeAzCost>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_az_cost").await?;
    let base_de_az_cost_index = IndexClient::<BaseDeAzCost> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_az_cost_index);
    Ok(search_service)
}
