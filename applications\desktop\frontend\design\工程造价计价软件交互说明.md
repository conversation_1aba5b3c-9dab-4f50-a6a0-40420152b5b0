# 工程造价计价软件交互说明

## 1. 控制台交互设计

### 1.1 启动界面交互流程

**界面布局交互**
```
控制台主界面 (1200×800像素)
┌────────────────────────────────────────┐
│  河北省工程造价计价软件 V1.0             │
│  ┌──────────────────────────────────┐   │
│  │        地区选择区域               │   │
│  │  省份：[河北省 ▼] 地市：[选择 ▼]  │   │
│  └──────────────────────────────────┘   │
│  ┌──────────────────────────────────┐   │
│  │        业务类型选择               │   │
│  │  ○概算 ●预算 ○结算 ○审核         │   │
│  └──────────────────────────────────┘   │
│  ┌──────────────────────────────────┐   │
│  │        最近项目列表               │   │
│  │  📁 某住宅项目_SJZ2025001         │   │
│  │  📁 某商业项目_BD2025002          │   │
│  └──────────────────────────────────┘   │
│                                      │
│     [新建项目]  [打开项目]  [设置]     │
└────────────────────────────────────────┘
```

**交互行为定义**
- **地区下拉联动**：选择省份后自动加载对应地市列表
- **业务类型影响**：选择不同业务类型，新建按钮文字相应变化
- **最近项目预览**：显示最近5个项目，双击直接打开
- **快捷操作**：Ctrl+N新建项目，Ctrl+O打开项目

### 1.2 项目创建向导交互

**第一步：项目基础信息录入**
```
项目信息录入弹窗 (600×500像素)
┌─────────────── 新建项目 ───────────────┐
│  步骤 1/3：项目基础信息                │
│                                       │
│  项目名称：[________________________]  │ ← 必填，实时字数统计
│            (最多100个字符)              │
│                                       │  
│  项目编码：[________________________]  │ ← 选填，[自动生成]按钮
│            □ 自动生成编码               │
│                                       │
│  建设单位：[________________________]  │ ← 必填，支持历史记录
│                                       │
│  项目描述：[________________________]  │ ← 选填，多行文本
│            [________________________]  │
│            [________________________]  │
│                                       │
│  [上一步]           [下一步]  [取消]    │
└───────────────────────────────────────┘
```

**第二步：标准配置选择**
```
标准配置弹窗 (700×600像素)  
┌─────────────── 新建项目 ───────────────┐
│  步骤 2/3：标准配置选择                │
│                                       │
│  文件类型：●招标文件 ○投标文件          │
│           ○单位工程 ○工料机法          │
│                                       │
│  清单标准：[GB50500-2013    ▼]         │
│                                       │
│  定额标准：[河北22定额      ▼]         │ ← 根据清单选择联动
│                                       │
│  估价表：  [省站规则        ▼]         │ ← 22定额时显示
│                                       │
│  计价模式：●全费用单价 ○清单计价        │
│                                       │
│  ┌─── 兼容性检查结果 ───┐              │
│  │ ✓ 标准组合兼容        │              │
│  │ ⚠ 建议使用22定额      │              │
│  └─────────────────────┘              │
│                                       │
│  [上一步]           [下一步]  [取消]    │
└───────────────────────────────────────┘
```

**第三步：创建确认**
```
确认信息弹窗 (600×450像素)
┌─────────────── 新建项目 ───────────────┐
│  步骤 3/3：确认创建                    │
│                                       │
│  项目信息：某住宅小区工程               │
│  项目编码：SJZ2025001                  │  
│  地区：    河北省石家庄市               │
│  标准：    13版清单+22版定额            │
│  模式：    全费用单价模式               │
│                                       │
│  ┌─── 将要创建的文件结构 ───┐          │
│  │ 📁 SJZ2025001某住宅小区工程/        │
│  │  ├─📄 project.meta                │
│  │  ├─📄 standards.config            │
│  │  ├─📁 data/                       │
│  │  └─📁 reports/                    │
│  └─────────────────────────────────┘  │
│                                       │
│  创建进度：[████████░░] 80%            │
│                                       │
│  [上一步]           [创建]  [取消]      │
└───────────────────────────────────────┘
```

## 2. 工作台交互设计

### 2.1 主界面交互布局

**整体窗体交互 (全屏,最小1440×900)**
```
┌─────────────────────────工作台主界面─────────────────────────┐
│ ┌─系统功能栏─┐                              ┌─窗体控制─┐ │
│ │📱软件Logo │💾保存│↶撤销│📁C:\项目\某住宅小区.gcj│👤用户▼│─□✕│ │
│ └─────────────┘                              └─────────┘ │
│ ┌─项目识别栏─────────────────────────────────────────────┐ │
│ │ 📊预算项目 | 🏗️招标文件 | 🏢单位工程 | 📋13版清单+22版定额  │ │
│ └─────────────────────────────────────────────────────┘ │
│ ┌─主菜单栏─────────────────────────────────────────────┐ │
│ │ 📁文件 | ✏️编制 | 📊报表 | 💻电子标 | ⚙️工具 | ❓帮助     │ │
│ └─────────────────────────────────────────────────────┘ │
│ ┌────────────────────主工作区────────────────────────────┐ │
│ │┌──结构树──┐┌─────编辑区─────┐┌──属性面板──┐           │ │
│ ││📁工程项目││                ││            │           │ │
│ ││├📂单项1  ││   [动态内容]    ││  [上下文]   │           │ │
│ ││├📂单项2  ││                ││  [相关信息] │           │ │
│ ││└📄单位1  ││                ││            │           │ │
│ │└─────────┘└───────────────┘└────────────┘           │ │
│ └─────────────────────────────────────────────────────┘ │
│ ┌─状态栏─────────────────────────────────────────────────┐ │
│ │ 就绪 | 最后保存:14:30 | 共1200项 | 总造价:¥2,850万      │ │
│ └─────────────────────────────────────────────────────┘ │
└───────────────────────────────────────────────────────┘
```

### 2.2 结构树交互设计

**树形控件交互行为**
```
工程结构树交互规范：
├── 点击节点：选中并加载对应编辑内容
├── 双击节点：重命名编辑模式  
├── 右键菜单：
│   ├── 新增下级 (单项工程可新增子单项或单位工程)
│   ├── 删除节点 (单位工程行不可删除)
│   ├── 重命名 
│   ├── 复制粘贴
│   └── 属性设置
├── 拖拽操作：支持同级节点间拖拽排序
└── 展开折叠：节点展开状态记忆，支持全部展开/折叠
```

**节点状态视觉交互**
- **编辑状态**：🔓正在编辑 (蓝色边框闪烁)
- **锁定状态**：🔒已锁定 (灰色显示，禁用操作)
- **异常状态**：❌数据异常 (红色感叹号，悬浮显示错误信息)
- **完成状态**：✅已完成 (绿色对勾，数据校验通过)

**数据统计显示**
```
节点显示格式：
📁 工程项目名称 (12项, ¥2850万)
├─📂 单项1 (5项, ¥1200万)  
│  └─📄 单位1 (5项, ¥1200万)
└─📂 单项2 (7项, ¥1650万)
   ├─📄 单位2 (3项, ¥800万)
   └─📄 单位3 (4项, ¥850万)
```

### 2.3 编辑区交互设计

**页签切换交互**
- **工程项目选中时**：工程概况 | 造价分析 | 取费表 | 人材机汇总
- **单项工程选中时**：工程概况 | 造价分析 | 取费表 | 人材机汇总  
- **单位工程选中时**：工程概况 | 造价分析 | 取费表 | 分部分项 | 措施项目 | 人材机汇总 | 其他项目 | 费用汇总

**页签内容动态加载**
- 切换页签时保存当前页签的编辑状态
- 新页签内容根据数据情况动态渲染
- 页签标题显示数据统计 (如"分部分项(125项)")

## 3. 分部分项编辑交互

### 3.1 表格编辑交互设计

**表格整体布局 (编辑区上半部分)**
```
分部分项编辑表格 (宽度自适应，高度400px)
┌─┬─────┬────┬──────────┬─────────┬─────┬──────┬─────┬─────┐
│#│编码  │类型│项目名称   │项目特征  │单位 │工程量 │单价 │合价 │
├─┼─────┼────┼──────────┼─────────┼─────┼──────┼─────┼─────┤
│1│      │单位│住宅楼工程  │5层砖混   │     │      │汇总 │汇总 │ ← 单位工程行
│2│0101  │分部│土石方工程  │          │     │      │汇总 │汇总 │ ← 分部行
│3│010101│清单│平整场地    │机械平整  │m²  │1200  │8.5  │10200│ ← 清单行  
│4│A1-1  │定额│推土机平整  │功率59kW  │100m²│12    │85   │1020 │ ← 定额行
│5│A1-2  │定额│人工修整    │人工精平  │m²  │1200  │6.5  │7800 │ ← 定额行
└─┴─────┴────┴──────────┴─────────┴─────┴──────┴─────┴─────┘
```

**单元格编辑交互规范**
- **双击编辑**：双击单元格进入编辑模式，ESC取消，Enter确认
- **Tab键导航**：Tab移动到下一单元格，Shift+Tab移动到上一单元格
- **编码选择器**：双击清单编码/定额编码列调用对应的标准库选择器
- **公式编辑器**：双击工程量列调用工程量计算器
- **数据验证**：失焦时实时验证数据格式和业务规则

**行操作交互规范**
- **插入行**：选中行后右键"插入行"，根据上下文判断插入类型
- **删除行**：Delete键或右键"删除行"，单位工程行不允许删除
- **复制粘贴**：Ctrl+C/V支持单行或多行复制，保持层级关系
- **拖拽排序**：支持同级行间拖拽调整顺序，跨级拖拽不允许

### 3.2 标准库选择器交互

**清单标准库选择器**
```
清单选择器弹窗 (900×600像素)
┌─────────────── 清单项目选择 ───────────────┐
│ 搜索：[土方_______________] [🔍搜索]       │
│ ┌─分类树─┐ ┌─────清单项目列表─────┐      │
│ │📂土石方│ │编码    │项目名称│单位│特征  │      │
│ │📂地基  │ │010101001│平整场地│m² │     │      │
│ │📂桩基  │ │010101002│挖基坑  │m³ │     │      │
│ │📂砌体  │ │010101003│土方回填│m³ │     │      │
│ └───────┘ └─────────────────────┘      │
│                                       │
│ 已选项目：平整场地 (010101001)           │
│ 项目特征：机械平整，厚度300mm             │ ← 可编辑
│                                       │
│ [确定]  [取消]  [清除]  [模糊匹配]      │
└───────────────────────────────────────┘
```

**定额标准库选择器**
```
定额选择器弹窗 (1000×700像素)
┌─────────────── 定额项目选择 ───────────────┐
│ 当前清单：010101001 平整场地               │
│ 推荐定额：[智能匹配] [手动选择] [历史记录] │
│                                         │
│ ┌─────────推荐定额列表─────────┐        │
│ │☑ A1-1   推土机平整场地  100m²│        │  ← 智能推荐，默认选中
│ │☐ A1-2   人工平整场地    m²   │        │  ← 可补充选择
│ │☐ A1-3   碾压机碾压     100m² │        │
│ └─────────────────────────────┘        │
│                                         │
│ ┌─────────定额详细信息─────────┐        │
│ │ 定额编码：A1-1                       │
│ │ 定额名称：推土机平整场地              │  
│ │ 工作内容：推土、平整、压实            │
│ │ 人工：0.033工日  材料：无             │
│ │ 机械：0.067台班  单价：85元/100m²    │
│ └─────────────────────────────────────┘│
│                                         │
│ [确定选择]  [取消]  [查看详情]  [收藏]   │
└─────────────────────────────────────────┘
```

### 3.3 工程量计算器交互

**计算器界面交互**
```
工程量计算器 (500×400像素)
┌─────────── 工程量计算 ───────────┐
│ 清单项目：010101001 平整场地      │
│ 当前工程量：1200 m²              │
│                                 │
│ ┌─────公式编辑区─────┐          │
│ │ 长度 × 宽度                  │          │
│ │ = 60 × 20                   │          │  
│ │ = 1200                      │          │
│ └─────────────────────┘          │
│                                 │
│ ┌─变量定义─┐ ┌─常用公式─┐       │
│ │长度:60 m │ │矩形面积  │       │
│ │宽度:20 m │ │圆形面积  │       │
│ │深度:    │ │体积计算  │       │
│ └─────────┘ └─────────┘       │
│                                 │
│ ┌─计算历史─────────────┐         │
│ │14:30 长×宽=60×20=1200         │
│ │14:25 (长+宽)×2×高=...        │
│ └─────────────────────┘         │
│                                 │
│ [应用结果] [保存公式] [清除] [关闭] │
└─────────────────────────────────┘
```

**交互操作规范**
- **实时计算**：在公式编辑区输入表达式，实时显示计算结果
- **变量引用**：支持在公式中引用已定义的变量，如"长度×宽度×深度"
- **公式保存**：常用公式可保存到个人公式库，支持分类管理
- **历史记录**：保存最近20次计算历史，支持重复调用

### 3.4 明细区交互设计

**人材机明细页签交互**
```
人材机明细表格 (编辑区下半部分)
┌─┬────┬──────────┬─────┬────┬─────┬─────┬─────┬─────┐
│#│类型│资源名称   │规格 │单位│定额量│调整量│基价 │合计 │
├─┼────┼──────────┼─────┼────┼─────┼─────┼─────┼─────┤
│1│P人工│普通工     │     │工日│0.033│0.035│120  │4.2  │ ← 可编辑调整量和价格
│2│M材料│无         │     │    │     │     │     │     │
│3│T机械│推土机     │59kW │台班│0.067│0.067│260  │17.4 │
└─┴────┴──────────┴─────┴────┴─────┴─────┴─────┴─────┘
```

**交互操作规范**
- **单元格编辑**：双击"调整量"和"基价"列进入编辑模式
- **批量操作**：选中多行支持批量调价，如"所有人工费+10%"
- **价格链接**：点击材料名称链接到材料信息价查询
- **影响分析**：修改数据后实时显示对上级费用的影响金额

**单价构成页签交互**
```
单价构成分析 (树形结构显示)
├─📊 综合单价: 125.8 元/m²
   ├─💰 直接费: 95.2 元 (75.7%)
   │  ├─👷 人工费: 4.2 元 (3.3%)
   │  ├─🧱 材料费: 0 元 (0%)  
   │  └─🚜 机械费: 17.4 元 (13.8%)
   ├─🏢 企业管理费: 5.7 元 (4.5%) [费率6%]
   ├─📈 利润: 3.5 元 (2.8%) [费率3.5%]
   └─💸 税金: 11.3 元 (9%) [增值税9%]
```

## 4. 数据验证交互设计

### 4.1 实时验证交互

**错误提示交互方式**
- **字段级验证**：失焦时显示字段旁红色感叹号+悬浮错误信息
- **行级验证**：整行数据异常时行背景变为浅红色
- **汇总级验证**：汇总数据异常时在状态栏显示错误统计
- **批量验证**：提供"检查全部"按钮，统一显示所有错误列表

**验证结果展示**
```
数据验证面板 (右侧属性面板)
┌─── 数据验证结果 ───┐
│ ❌ 发现 3 个错误     │
│                     │
│ 第5行：工程量不能为0  │ [定位]
│ 第12行：编码格式错误  │ [定位]  
│ 第18行：单价异常偏高  │ [定位]
│                     │
│ ⚠️ 发现 2 个警告     │
│                     │  
│ 第8行：材料价格偏高   │ [查看]
│ 第15行：工程量偏大    │ [查看]
│                     │
│ [修复全部] [忽略警告] │
└─────────────────────┘
```

### 4.2 批量操作交互

**批量选择交互**
- **全选操作**：Ctrl+A选择当前页签所有数据行
- **区间选择**：Shift+Click选择连续区间
- **多选操作**：Ctrl+Click进行不连续多选
- **筛选选择**：基于条件筛选后批量选择符合条件的行

**批量修改弹窗交互**
```
批量修改弹窗 (600×400像素)
┌─────────── 批量修改 ───────────┐
│ 选中行数：25 行                │
│                               │
│ 修改字段：[项目特征 ▼]         │ ← 下拉选择可批量修改的字段
│                               │  
│ 修改方式：                     │
│ ●替换为：[________________]     │
│ ○追加到：[________________]     │  
│ ○乘以系数：[___] (如1.05)      │
│ ○按比例调整：增加[__]%          │
│                               │
│ ┌─── 预览结果 ───┐            │
│ │第3行: 原值→新值             │
│ │第5行: 原值→新值             │  
│ │...                         │
│ └─────────────────┘            │
│                               │
│ ☑ 记录修改原因                 │
│ 原因：[市场价格调整___________] │
│                               │
│ [预览] [应用] [取消]           │
└─────────────────────────────────┘
```

### 3.3 数据关联交互

**定额插入交互流程**
```
定额插入交互步骤：
1. 双击清单行的定额编码列
   ↓
2. 弹出定额选择器，显示智能推荐定额
   ↓  
3. 用户确认选择定额项目
   ↓
4. 系统自动执行：
   - 插入定额行到清单下级
   - 带出人材机明细数据到底部明细区
   - 自动计算清单综合单价
   - 刷新上级汇总数据
   ↓
5. 界面更新：
   - 表格增加定额行，自动调整行高
   - 明细区切换到"人材机明细"页签
   - 属性面板显示定额详细信息
```

**数据联动更新交互**
- **向上传播**：定额数据变更→清单单价更新→分部费用更新→单位工程费用更新
- **向下传播**：工程量调整→定额工程量按含量分摊→人材机用量重算
- **平级影响**：相关清单项目间的逻辑关系检查和提示
- **实时反馈**：数据变更后0.5秒内完成计算，1秒内更新界面显示

## 5. 高级交互功能

### 5.1 智能提示交互

**编码输入智能提示**
- **模糊匹配**：输入部分编码自动显示匹配项目列表
- **历史记录**：优先显示当前项目中已使用的编码
- **常用推荐**：根据项目类型推荐常用的清单项目
- **快速过滤**：支持拼音简拼过滤，如"tsfgc"匹配"土石方工程"

**关联数据自动补全**
- **项目名称补全**：根据编码自动补全标准项目名称
- **特征模板推荐**：根据项目类型推荐特征描述模板
- **单位自动带出**：编码确定后自动带出对应计量单位
- **工程量参考**：显示类似项目的工程量参考值

### 5.2 数据对比交互

**历史数据对比**
```
对比分析面板 (属性面板)
┌─── 历史数据对比 ───┐
│ 对比项目：         │
│ [某类似住宅项目▼]   │
│                   │
│ 当前项目 vs 对比项目│
│ 土方工程：         │
│ 8.5元   vs  7.8元  │ ↑ 8.9% 📊
│                   │  
│ 砌体工程：         │
│ 156元   vs  162元  │ ↓ 3.7% 📊
│                   │
│ [详细对比] [导出]  │
└───────────────────┘
```

**版本对比交互**
- **版本选择**：从历史版本列表中选择要对比的版本
- **差异高亮**：变更数据用不同颜色高亮显示
- **变更统计**：统计新增、修改、删除的项目数量
- **影响分析**：分析版本间差异对总造价的影响

### 5.3 协同编辑交互

**多用户协同状态提示**
```
协同状态显示：
├─ 用户头像 + 姓名显示当前编辑用户
├─ 编辑锁定：正在编辑的行显示锁定图标  
├─ 冲突提示：多人编辑冲突时红色提示
└─ 版本同步：自动同步其他用户的更新
```

**冲突解决交互**
```
编辑冲突解决弹窗
┌─────── 编辑冲突 ───────┐
│ 检测到数据冲突：        │
│                        │
│ 当前值：125.8 元/m²     │
│ 服务器值：128.3 元/m²   │  
│ 修改人：张工 (14:25)    │
│                        │
│ 处理方式：              │
│ ○ 使用当前值 (覆盖)     │
│ ● 使用服务器值 (放弃)   │
│ ○ 手动合并             │
│                        │
│ [确定] [取消] [查看详情] │
└────────────────────────┘
```

## 6. 报表生成交互

### 6.1 报表模板选择交互

**报表列表界面**
```
报表生成界面 (主编辑区)
┌─────────── 报表模板库 ───────────┐
│ 📊 招标文件报表                   │
│  ├─ 招标工程量清单                │ [生成] [预览]
│  ├─ 招标控制价汇总表              │ [生成] [预览]  
│  ├─ 措施项目清单                  │ [生成] [预览]
│  └─ 主要材料表                   │ [生成] [预览]
│                                  │
│ 📈 造价分析报表                   │
│  ├─ 单方造价指标分析              │ [生成] [预览]
│  ├─ 专业造价构成分析              │ [生成] [预览]  
│  └─ 主要工程量指标               │ [生成] [预览]
│                                  │
│ 🔧 自定义报表                     │
│  ├─ + 新建报表模板                │
│  └─ 📝 我的报表模板 (3个)         │
└──────────────────────────────────┘
```

### 6.2 报表设计交互

**报表设计器界面**
```
报表设计器 (全屏模式)
┌─工具栏─┬─────────────报表设计区─────────────┬─属性栏─┐
│📄新建  │                                    │        │
│💾保存  │  ┌─表头─────────────────┐          │ 字体   │
│🖨️预览  │  │    工程量清单汇总表    │          │ 大小   │
│        │  └─────────────────────┘          │ 颜色   │
│📊插入表│  ┌─表体─────────────────┐          │        │
│📋插入字│  │序号│编码  │名称│单价│合价│      │ 对齐   │
│🎨格式  │  │  1 │010101│... │... │... │      │ 边框   │
│        │  └─────────────────────┘          │ 背景   │
│        │  ┌─表尾─────────────────┐          │        │
│        │  │         合计：￥285万   │          │ 数据   │
│        │  └─────────────────────┘          │ 来源   │
└────────┴────────────────────────────────────┴────────┘
```

## 7. 性能优化交互

### 7.1 大数据量交互优化

**虚拟滚动交互**
- **分页加载**：表格数据超过500行时启用虚拟滚动
- **快速定位**：提供行号快速跳转功能
- **搜索定位**：支持编码、名称的快速搜索定位
- **视窗优化**：只渲染可见区域数据，提升滚动流畅度

**异步计算交互**
```
计算进度提示：
┌─── 正在计算 ───┐
│ 计算进度：      │
│ [████████░░] 80%│  
│ 预计完成：5秒   │
│ [取消计算]      │
└───────────────┘
```

### 7.2 自动保存交互

**保存状态交互提示**
- **自动保存指示**：状态栏显示"自动保存中..."，保存完成后显示时间
- **未保存标识**：文件名后显示"*"表示有未保存修改
- **保存冲突**：检测到文件被其他程序修改时的冲突提示
- **恢复机制**：异常退出后重启时提示恢复未保存数据

## 8. 用户体验优化

### 8.1 快捷操作交互

**键盘快捷键**
- **Ctrl+S**：保存当前项目  
- **Ctrl+Z/Y**：撤销/重做操作
- **Ctrl+F**：查找项目内容
- **Ctrl+H**：查找替换
- **F2**：重命名选中节点
- **Delete**：删除选中行(单位工程行除外)
- **Insert**：在当前位置插入新行
- **Ctrl+D**：复制当前行到下一行

**鼠标手势**
- **滚轮缩放**：Ctrl+滚轮调整表格字体大小
- **中键点击**：在新标签页打开节点内容  
- **拖拽复制**：按住Ctrl拖拽实现复制粘贴
- **框选操作**：鼠标框选支持连续区域选择

### 8.2 个性化设置交互

**界面布局自定义**
- **面板宽度调整**：拖拽分隔线调整结构树、编辑区、属性面板宽度
- **工具栏自定义**：右键工具栏支持显示/隐藏特定按钮
- **快捷按钮**：支持将常用功能添加到快速访问工具栏
- **主题切换**：支持浅色/深色主题切换

**用户偏好设置**
```
用户设置弹窗 (500×600像素)
┌─────── 用户偏好设置 ───────┐
│ 📋 常规设置              │
│  ☑ 启动时打开最近项目     │
│  ☑ 自动保存 (间隔: [5]分钟) │
│  ☑ 退出前提示保存        │
│  ☐ 显示计算过程详情      │
│                         │
│ 🎨 界面设置              │
│  主题: ○浅色 ●深色 ○自动 │
│  字体大小: [12 ▼] pt    │
│  表格行高: [正常 ▼]     │
│  显示网格线: ☑         │
│                         │
│ 📊 数据设置              │
│  小数位数: [4] 位       │
│  千分位分隔符: ☑       │
│  货币符号: [¥ ▼]       │
│  日期格式: [YYYY-MM-DD▼] │
│                         │
│ ⌨️ 快捷键设置            │
│  [自定义快捷键...]      │
│                         │
│ [应用] [重置] [取消]     │
└─────────────────────────┘
```

### 8.3 帮助引导交互

**新手引导交互**
```
引导遮罩交互 (首次使用时)
┌─────────────────工作台─────────────────┐
│ [模糊背景]                             │
│     ┌─── 欢迎使用! ───┐               │
│     │                 │  ◄─── 高亮区域  │
│     │ 这是项目结构树， │               │
│     │ 点击节点查看对应 │               │  
│     │ 的工程内容       │               │
│     │                 │               │
│     │ [下一步] [跳过]  │               │
│     └─────────────────┘               │
│                                       │
└───────────────────────────────────────┘
```

**智能提示交互**
- **操作提示**：首次使用特定功能时显示操作提示气泡
- **快捷提示**：右下角显示相关快捷键提示
- **进度引导**：复杂操作提供步骤进度指示
- **错误帮助**：遇到错误时提供相关帮助文档链接

## 9. 移动适配交互 (预留)

### 9.1 响应式布局交互

**平板适配 (1024×768)**
- **折叠面板**：属性面板可折叠隐藏，增加编辑区空间
- **触控优化**：增大点击热区，支持手势操作
- **简化菜单**：合并部分功能到更少的菜单项中
- **滑动操作**：支持左右滑动切换页签

**手机查看模式 (375×667)**
- **只读模式**：手机端仅支持项目查看和简单数据录入
- **垂直布局**：结构树和编辑区垂直排列
- **底部导航**：主要功能放置在底部导航栏
- **手势导航**：支持滑动返回、上拉刷新等手势

### 9.2 离线同步交互

**离线工作提示**
```
离线状态栏提示：
🔴 离线工作模式 | 数据将在联网后同步 | [立即同步]
```

**同步冲突解决**
- **冲突列表**：显示需要解决的数据冲突项目
- **合并向导**：提供分步骤的冲突解决向导
- **备份恢复**：同步失败时提供数据备份恢复选项

## 10. 验收标准

### 10.1 交互响应性能标准

**操作响应时间要求**
- **界面切换**：页签切换响应时间 ≤ 300ms
- **数据录入**：单元格编辑响应时间 ≤ 100ms  
- **计算更新**：数据变更后计算完成时间 ≤ 1000ms
- **文件保存**：项目保存完成时间 ≤ 2000ms
- **报表生成**：标准报表生成时间 ≤ 5000ms

**数据处理能力要求**
- **表格滚动**：1000行数据滚动流畅度 ≥ 30fps
- **搜索响应**：10000条数据搜索响应时间 ≤ 500ms
- **批量操作**：500行数据批量修改完成时间 ≤ 3000ms
- **并发编辑**：支持≥5人同时在线编辑不冲突

### 10.2 用户体验标准

**界面易用性标准**
- [ ] 新用户无需培训能在10分钟内完成基本项目创建
- [ ] 常用操作路径不超过3次点击
- [ ] 所有交互元素符合Windows UI设计规范
- [ ] 错误提示信息准确易懂，提供解决建议
- [ ] 支持键盘快捷键完成90%常用操作

**数据准确性标准**
- [ ] 数据录入实时校验准确率100%
- [ ] 计算结果与手工计算误差≤0.01元
- [ ] 自动保存数据完整性100%，无数据丢失
- [ ] 协同编辑数据同步准确性≥99.9%
- [ ] 报表数据与编辑数据一致性100%

### 10.3 兼容性标准

**系统兼容性要求**
- [ ] Windows 10/11系统完全兼容
- [ ] 1920×1080及以上分辨率完美显示
- [ ] 4GB及以上内存环境流畅运行
- [ ] 支持主流打印机正确打印报表
- [ ] 兼容常用办公软件数据导入导出

**标准库兼容性要求**  
- [ ] 河北省清单定额标准100%支持
- [ ] 标准库更新后向前兼容历史项目
- [ ] 支持国标清单标准2013版和2024版
- [ ] 支持河北省12版和22版定额标准
- [ ] 估价表规则准确应用到计算结果

## 11. 异常场景交互处理

### 11.1 网络异常交互

**网络连接中断处理**
```
网络异常提示：
┌─── 网络连接异常 ───┐
│ ⚠️ 检测到网络中断    │
│                    │
│ 当前操作：          │
│ ● 继续离线工作      │
│ ○ 等待网络恢复      │
│ ○ 重试连接          │
│                    │
│ 注意：离线期间的修改│
│ 将在网络恢复后同步  │
│                    │  
│ [确定] [设置]       │
└────────────────────┘
```

### 11.2 数据异常交互

**数据损坏恢复交互**
```
数据恢复向导：
┌─── 数据异常恢复 ───┐
│ 检测到数据异常：    │
│ • 项目文件损坏      │
│ • 部分数据缺失      │
│                    │
│ 恢复选项：          │
│ ○ 从最近备份恢复    │
│   (2024-08-28 14:30) │
│ ○ 从自动保存恢复    │  
│   (2024-08-28 14:35) │
│ ○ 重新创建项目      │
│                    │
│ [开始恢复] [取消]   │
└────────────────────┘
```

### 11.3 系统资源异常

**内存不足处理交互**
- **资源监控**：实时监控内存使用情况，达到80%时预警
- **数据压缩**：自动压缩历史数据，释放内存空间
- **功能降级**：关闭非必要功能，如实时预览、自动计算等
- **优雅降级**：提示用户保存数据并重启软件

## 12. 可访问性交互设计

### 12.1 键盘导航支持

**Tab键导航顺序**
```
Tab导航路径：
主菜单 → 工具栏 → 结构树 → 编辑区表格 → 明细区 → 属性面板 → 状态栏
```

**Focus焦点视觉反馈**
- **高对比度边框**：当前焦点元素显示2px蓝色边框
- **焦点指示器**：复杂控件内部显示焦点位置指示
- **跳过链接**：提供"跳过导航"快捷链接到主内容区

### 12.2 屏幕阅读器支持

**语义化标记**
- **ARIA标签**：为复杂交互控件添加适当的ARIA属性
- **标题层级**：正确使用H1-H6标题层级标记内容结构
- **表格标题**：数据表格正确标记表头和数据关联关系
- **状态描述**：动态内容变化时提供状态描述

### 12.3 视觉辅助功能

**高对比度模式**
- **颜色对比**：确保文字与背景对比度≥4.5:1
- **状态区分**：不仅依赖颜色，同时使用图标、形状区分状态
- **焦点可见性**：高对比度模式下焦点指示器仍然清晰可见

**字体缩放支持**
- **相对尺寸**：使用相对尺寸单位，支持系统字体缩放
- **布局适应**：字体放大时界面布局自动调整，不出现重叠
- **最小尺寸**：确保最小可点击区域≥44×44像素

---

**文档版本**：V1.0  
**编制日期**：2025年8月28日  
**关联文档**：业务框架.md、详情业务说明.md  
**适用范围**：工程造价计价软件用户交互设计  
**验收标准**：所有交互功能必须通过可用性测试和无障碍访问测试