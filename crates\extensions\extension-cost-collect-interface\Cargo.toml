[package]
name = "extension-cost-collect-interface"
version = { workspace = true }
edition = { workspace = true }
authors = { workspace = true }
description = "费用计取 extension"


[dependencies]
# 基础依赖
serde = { workspace = true }
serde_json = { workspace = true }
async-trait = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
chrono = { workspace = true, features = ["serde"] }

# 高性能集合
dashmap = { workspace = true }

# 公共类型库依赖
price-common = { workspace = true }
moduforge-model = { workspace = true }
moduforge-state = { workspace = true }
