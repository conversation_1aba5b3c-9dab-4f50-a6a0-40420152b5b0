/*
CREATE TABLE "base_fee_file_project" (
  "sequence_nbr" text(20) NOT NULL,
  "library_code" text(255),
  "library_name" text(255),
  "code" text(255),
  "qf_code" text(255),
  "qf_name" text(255),
  "project_type" text(50),
  "unit_project_name" text(50),
  "remark" text(255),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseFeeFileProject {
    pub sequence_nbr: String,
    pub library_code: Option<String>,
    pub library_name: Option<String>,
    pub code: Option<String>,
    pub qf_code: Option<String>,
    pub qf_name: Option<String>,
    pub project_type: Option<String>,
    pub unit_project_name: Option<String>,
    pub remark: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
}
//crud!(BaseFeeFileProject {}, "base_fee_file_project");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "code".to_string(),
            "qf_code".to_string(),
            "qf_name".to_string(),
            "project_type".to_string(),
            "unit_project_name".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "code".to_string(),
            "qf_code".to_string(),
            "qf_name".to_string(),
            "project_type".to_string(),
            "unit_project_name".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "library_name".to_string(),
            "code".to_string(),
            "qf_code".to_string(),
            "qf_name".to_string(),
            "project_type".to_string(),
            "unit_project_name".to_string(),
            "remark".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseFeeFileProject>("base_fee_file_project", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseFeeFileProject>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_fee_file_project").await?;
    let base_fee_file_project_index = IndexClient::<BaseFeeFileProject> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_fee_file_project_index);
    Ok(search_service)
}
