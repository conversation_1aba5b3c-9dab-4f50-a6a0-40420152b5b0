[package]
description = "预算项目"
edition = {workspace = true}
name = "price-budget"
version = {workspace = true}

[dependencies]
anyhow = {workspace = true}
async-trait = {workspace = true}
axum = {workspace = true}
bytes = {workspace = true}
chrono = {workspace = true}
dashmap = {workspace = true}
serde = {workspace = true}
serde_json = {workspace = true}
state = {workspace = true}
thiserror = {workspace = true}
tokio = {workspace = true}
tower-service = {workspace = true}
tower= {workspace = true}
validator = {workspace = true}

moduforge-core = {workspace = true}
moduforge-macros = {workspace = true}
moduforge-model = {workspace = true}
moduforge-state = {workspace = true}
moduforge-transform = {workspace = true}
price-budget-core = {workspace = true}
price-rules = {workspace = true}
price-search = {workspace = true}
price-storage = {workspace = true}
price-web = {workspace = true}
rbs = {workspace = true}
shared = {workspace = true}

# 引入pagkages 下面的所有库
extension-base-schema = {workspace = true}
extension-bzhs = {workspace = true}
extension-cost-collect = {workspace = true}
extension-djgc = {workspace = true}
extension-fbfx-csxm = {workspace = true}
extension-rcj = {workspace = true}
extension-rcj-collect = {workspace = true}
