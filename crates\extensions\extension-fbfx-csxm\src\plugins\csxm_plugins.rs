use std::sync::Arc;

// 措施项目插件
use mf_state::plugin::{PluginMetadata, PluginTrait};
use mf_state::transaction::Transaction;
use mf_state::state::State;
use mf_state::error::StateResult;

/// 措施项目插件
/// 用于处理措施项目的插入 新增 删除 修改
/// 更新完 当前数据的单条计算
#[derive(Debug)]
pub struct CsxmPlugin;
#[async_trait::async_trait]
impl PluginTrait for CsxmPlugin {
    fn metadata(&self) -> PluginMetadata {
        PluginMetadata {
            name: "csxm".to_string(),
            version: "1.0.0".to_string(),
            description: "措施项目插件".to_string(),
            author: "moduforge".to_string(),
            dependencies: vec![],
            conflicts: vec![],
            state_fields: vec![],
            tags: vec![],
        }
    }
    async fn append_transaction(
        &self,
        _trs: &[Transaction],
        _old_state: &State,
        _new_state: &State, 
    ) -> StateResult<Option<Transaction>> {
        // todo
        Ok(None)
    }
    async fn filter_transaction(
        &self,
        _tr: &Transaction,
        _state: &State,
    ) -> bool {
        // todo
        true
    }
}

