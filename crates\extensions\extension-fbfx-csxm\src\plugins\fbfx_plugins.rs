use std::sync::Arc;

// 分部分项插件
use mf_state::plugin::{PluginMetadata, PluginTrait};
use mf_state::transaction::Transaction;
use mf_state::state::State;
use mf_state::error::StateResult;


/// 分部分项插件
/// 用于处理分部分项的插入 新增 删除 修改
/// 更新完 当前数据的单条计算
#[derive(Debug)]
pub struct FbfxPlugin;
#[async_trait::async_trait]
impl PluginTrait for FbfxPlugin {
    fn metadata(&self) -> PluginMetadata {
        PluginMetadata {
            name: "fbfx".to_string(),
            version: "1.0.0".to_string(),
            description: "分部分项插件".to_string(),
            author: "moduforge".to_string(),
            dependencies: vec![],
            conflicts: vec![],
            state_fields: vec![],
            tags: vec![],
        }
    }
    async fn append_transaction(
        &self,
        _trs: &[Transaction],
        _old_state: &State,
        _new_state: &State,
    ) -> StateResult<Option<Transaction>> {
        // todo
        Ok(None)
    }
    async fn filter_transaction(
        &self,
        _tr: &Transaction,
        _state: &State,
    ) -> bool {
        // todo
        true
    }
}


