/*
CREATE TABLE "sys_user" (
  "sequence_nbr" integer(20) NOT NULL,
  "agency_code" text(64),
  "product_code" text(64),
  "username" text(64) NOT NULL,
  "password" text(255),
  "salt" text(255),
  "phone" text(20),
  "avatar" text(255),
  "dept_id" text(32),
  "lock_flag" text(1),
  "del_flag" text(1),
  "wx_openid" text(32),
  "mini_openid" text(32),
  "qq_openid" text(32),
  "xili_openid" text(32),
  "tenant_id" integer(11) NOT NULL,
  "is_full" integer(2) NOT NULL,
  "agency_type" text(64),
  "rec_user_code" text(32),
  "rec_date" integer(20),
  "rec_status" text(4),
  "extend1" text(255),
  "extend2" text(255),
  "extend3" text(255),
  "description" text(255),
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct SysUser {
    pub sequence_nbr: String,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
    pub username: String,
    pub password: Option<String>,
    pub salt: Option<String>,
    pub phone: Option<String>,
    pub avatar: Option<String>,
    pub dept_id: Option<String>,
    pub lock_flag: Option<String>,
    pub del_flag: Option<String>,
    pub wx_openid: Option<String>,
    pub mini_openid: Option<String>,
    pub qq_openid: Option<String>,
    pub xili_openid: Option<String>,
    pub tenant_id: Option<i32>,
    pub is_full: Option<i32>,
    pub agency_type: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
}
//crud!(SysUser {}, "sys_user");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "agency_code".to_string(),
            "product_code".to_string(),
            "username".to_string(),
            "password".to_string(),
            "salt".to_string(),
            "phone".to_string(),
            "avatar".to_string(),
            "dept_id".to_string(),
            "lock_flag".to_string(),
            "del_flag".to_string(),
            "wx_openid".to_string(),
            "mini_openid".to_string(),
            "qq_openid".to_string(),
            "xili_openid".to_string(),
            "tenant_id".to_string(),
            "is_full".to_string(),
            "agency_type".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "agency_code".to_string(),
            "product_code".to_string(),
            "username".to_string(),
            "password".to_string(),
            "salt".to_string(),
            "phone".to_string(),
            "avatar".to_string(),
            "dept_id".to_string(),
            "lock_flag".to_string(),
            "del_flag".to_string(),
            "wx_openid".to_string(),
            "mini_openid".to_string(),
            "qq_openid".to_string(),
            "xili_openid".to_string(),
            "tenant_id".to_string(),
            "is_full".to_string(),
            "agency_type".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "agency_code".to_string(),
            "product_code".to_string(),
            "username".to_string(),
            "password".to_string(),
            "salt".to_string(),
            "phone".to_string(),
            "avatar".to_string(),
            "dept_id".to_string(),
            "lock_flag".to_string(),
            "del_flag".to_string(),
            "wx_openid".to_string(),
            "mini_openid".to_string(),
            "qq_openid".to_string(),
            "xili_openid".to_string(),
            "tenant_id".to_string(),
            "is_full".to_string(),
            "agency_type".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<SysUser>("sys_user", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<SysUser>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("sys_user").await?;
    let base_list_index = IndexClient::<SysUser> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
