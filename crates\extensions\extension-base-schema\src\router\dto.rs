use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// 分页查询参数
#[derive(Debug, Deserialize)]
pub struct PaginationQuery {
    pub page: Option<u32>,
    pub size: Option<u32>,
    pub sort: Option<String>,
    pub order: Option<String>, // asc, desc
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            size: Some(20),
            sort: Some("created_at".to_string()),
            order: Some("desc".to_string()),
        }
    }
}

/// 分页响应
#[derive(Debug, Serialize)]
pub struct PagedResponse<T> {
    pub items: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub size: u32,
    pub pages: u32,
}

/// 项目创建请求
#[derive(Debug, Deserialize)]
pub struct CreateProjectRequest {
    pub construct_name: String,
    pub construct_code: String,
    pub path: Option<String>,
    pub bidding_type: Option<i32>,
    pub ss_province: Option<String>,
    pub ss_province_name: Option<String>,
    pub ss_city: Option<String>,
    pub ss_city_name: Option<String>,
    pub construction_unit: Option<String>,
    pub project_overview: Option<String>,
}

/// 项目更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateProjectRequest {
    pub construct_name: Option<String>,
    pub construct_code: Option<String>,
    pub path: Option<String>,
    pub bidding_type: Option<i32>,
    pub ss_province: Option<String>,
    pub ss_province_name: Option<String>,
    pub ss_city: Option<String>,
    pub ss_city_name: Option<String>,
    pub construction_unit: Option<String>,
    pub project_overview: Option<String>,
}

/// 项目响应
#[derive(Debug, Serialize)]
pub struct ProjectResponse {
    pub id: Uuid,
    pub construct_name: String,
    pub construct_code: String,
    pub path: Option<String>,
    pub bidding_type: i32,
    pub ss_province: Option<String>,
    pub ss_province_name: Option<String>,
    pub ss_city: Option<String>,
    pub ss_city_name: Option<String>,
    pub construction_unit: Option<String>,
    pub project_overview: Option<String>,
    pub created_at: String,
    pub updated_at: String,
}

/// 单项工程创建请求
#[derive(Debug, Deserialize)]
pub struct CreateSingleProjectRequest {
    pub project_code: String,
    pub project_name: String,
    pub jzmj: Option<String>, // 建筑面积
    pub ss_province: Option<String>,
    pub ss_city: Option<String>,
    pub project_id: Uuid,
}

/// 单项工程响应
#[derive(Debug, Serialize)]
pub struct SingleProjectResponse {
    pub id: Uuid,
    pub project_code: String,
    pub project_name: String,
    pub jzmj: Option<String>,
    pub total: Option<f64>,
    pub average: Option<String>,
    pub unit_cost: Option<f64>,
    pub project_id: Uuid,
    pub created_at: String,
    pub updated_at: String,
}

/// 单位工程创建请求
#[derive(Debug, Deserialize)]
pub struct CreateUnitProjectRequest {
    pub up_code: String,
    pub up_name: String,
    pub average: Option<String>, // 建筑面积
    pub construct_major_type: Option<String>, // 专业类型
    pub single_project_id: Uuid,
}

/// 单位工程响应
#[derive(Debug, Serialize)]
pub struct UnitProjectResponse {
    pub id: Uuid,
    pub up_code: String,
    pub up_name: String,
    pub uptotal: Option<f64>, // 合计金额
    pub average: Option<String>, // 建筑面积
    pub fbfxhj: Option<f64>, // 分部分项合计
    pub csxhj: Option<f64>, // 措施项目合计
    pub construct_major_type: Option<String>,
    pub single_project_id: Uuid,
    pub created_at: String,
    pub updated_at: String,
}

/// 项目结构响应
#[derive(Debug, Serialize)]
pub struct ProjectStructureResponse {
    pub project: ProjectResponse,
    pub single_projects: Vec<SingleProjectWithUnits>,
}

/// 带单位工程的单项工程
#[derive(Debug, Serialize)]
pub struct SingleProjectWithUnits {
    #[serde(flatten)]
    pub single_project: SingleProjectResponse,
    pub unit_projects: Vec<UnitProjectResponse>,
}

/// 计算单位工程请求
#[derive(Debug, Deserialize)]
pub struct CalculateUnitProjectRequest {
    pub calculation_type: String, // fbfx, csxm, qtxm
    pub force_recalculate: Option<bool>,
}

/// 计算结果响应
#[derive(Debug, Serialize)]
pub struct CalculationResponse {
    pub unit_project_id: Uuid,
    pub calculation_type: String,
    pub results: HashMap<String, f64>,
    pub calculated_at: String,
}

/// 导出项目请求
#[derive(Debug, Deserialize)]
pub struct ExportProjectRequest {
    pub format: String, // xml, excel, pdf
    pub include_details: Option<bool>,
    pub template_id: Option<String>,
}

/// 导出结果响应
#[derive(Debug, Serialize)]
pub struct ExportResponse {
    pub file_url: String,
    pub file_name: String,
    pub file_size: u64,
    pub export_time: String,
}

/// 扩展信息响应
#[derive(Debug, Serialize)]
pub struct ExtensionInfoResponse {
    pub name: String,
    pub version: String,
    pub description: String,
    pub supported_nodes: Vec<String>,
    pub capabilities: Vec<String>,
}

/// 健康检查响应
#[derive(Debug, Serialize)]
pub struct HealthResponse {
    pub status: String,
    pub timestamp: String,
    pub version: String,
}