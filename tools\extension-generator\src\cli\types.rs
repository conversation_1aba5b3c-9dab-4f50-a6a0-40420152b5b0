use clap::{Subcommand, ValueEnum};
use serde::{Deserialize, Serialize};
use std::fmt;

/// 扩展类型
#[derive(ValueEnum, Clone, Debug, Serialize, Deserialize)]
pub enum ExtensionType {
    /// 标准扩展（包含所有四层）
    Standard,
    /// 轻量级扩展（仅包含Nodes和Plugins）
    Lightweight,
    /// API扩展（包含Nodes、Plugins、Commands、Router）
    Api,
    /// 核心扩展（仅包含Nodes和Plugins，无Web接口）
    Core,
    /// 自定义扩展
    Custom,
}

impl ExtensionType {
    pub fn default_layers(&self) -> Vec<LayerType> {
        match self {
            ExtensionType::Standard => vec![
                LayerType::Nodes, 
                LayerType::Plugins, 
                LayerType::Commands, 
                LayerType::Router
            ],
            ExtensionType::Lightweight => vec![
                LayerType::Nodes, 
                LayerType::Plugins
            ],
            ExtensionType::Api => vec![
                LayerType::Nodes, 
                LayerType::Plugins, 
                LayerType::Commands, 
                LayerType::Router
            ],
            ExtensionType::Core => vec![
                LayerType::Nodes, 
                LayerType::Plugins, 
                LayerType::Commands
            ],
            ExtensionType::Custom => vec![],
        }
    }
    
    pub fn description(&self) -> &str {
        match self {
            ExtensionType::Standard => "标准扩展，包含完整的四层架构",
            ExtensionType::Lightweight => "轻量级扩展，仅包含数据层和业务逻辑层",
            ExtensionType::Api => "API扩展，包含完整的Web API支持",
            ExtensionType::Core => "核心扩展，包含业务逻辑但无Web接口",
            ExtensionType::Custom => "自定义扩展，可选择特定的层组合",
        }
    }
}

impl fmt::Display for ExtensionType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ExtensionType::Standard => write!(f, "standard"),
            ExtensionType::Lightweight => write!(f, "lightweight"),
            ExtensionType::Api => write!(f, "api"),
            ExtensionType::Core => write!(f, "core"),
            ExtensionType::Custom => write!(f, "custom"),
        }
    }
}

/// 组件层类型
#[derive(ValueEnum, Clone, Debug, Serialize, Deserialize, PartialEq)]
pub enum LayerType {
    /// 节点定义层（数据结构）
    Nodes,
    /// 插件逻辑层（业务逻辑）
    Plugins,
    /// 命令操作层（CRUD操作）
    Commands,
    /// 路由接口层（HTTP接口）
    Router,
}

impl LayerType {
    pub fn description(&self) -> &str {
        match self {
            LayerType::Nodes => "节点定义层 - 定义数据结构和字段",
            LayerType::Plugins => "插件逻辑层 - 实现业务逻辑和事务处理",
            LayerType::Commands => "命令操作层 - 提供CRUD操作接口",
            LayerType::Router => "路由接口层 - 提供HTTP API接口",
        }
    }
    
    pub fn directory_name(&self) -> &str {
        match self {
            LayerType::Nodes => "nodes",
            LayerType::Plugins => "plugins",
            LayerType::Commands => "command",
            LayerType::Router => "router",
        }
    }
    
    pub fn is_web_related(&self) -> bool {
        matches!(self, LayerType::Router)
    }
}

impl fmt::Display for LayerType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            LayerType::Nodes => write!(f, "nodes"),
            LayerType::Plugins => write!(f, "plugins"),
            LayerType::Commands => write!(f, "commands"),
            LayerType::Router => write!(f, "router"),
        }
    }
}

/// 配置操作类型
#[derive(Subcommand, Clone, Debug)]
pub enum ConfigAction {
    /// 显示当前配置
    Show,
    /// 设置配置项
    Set {
        /// 配置键
        key: String,
        /// 配置值
        value: String,
    },
    /// 获取配置项
    Get {
        /// 配置键
        key: String,
    },
    /// 重置配置到默认值
    Reset,
    /// 配置向导
    Init,
}