use serde::{Deserialize, Serialize};
use mf_derive::Node;

/// {{ description }}容器节点
/// 用于表示整个{{ description }}结构的顶层容器
#[derive(Node, Debug, <PERSON>lone, Serialize, Deserialize)]
#[node_type = "{{ names.kebab }}"]
#[desc = "{{ description }}"]
#[content = "{{ names.camel }}RowNode+"]
pub struct {{ node_prefix }}ContainerNode {
    #[attr]
    pub value: String,
}

/// {{ description }}行节点
/// 用于表示{{ description }}中的每一行数据
#[derive(Node, Debug, <PERSON>lone, Serialize, Deserialize)]
#[node_type = "{{ names.camel }}RowNode"]
#[desc = "{{ description }}行节点"]
pub struct {{ node_prefix }}RowNode {
    /// 编码
    #[attr]
    pub code: String,
    
    /// 名称
    #[attr]
    pub name: String,
    
    /// 描述
    #[attr]
    pub description: String,
    
    /// 数量
    #[attr(default = 1.0)]
    pub quantity: f64,
    
    /// 单价
    #[attr(default = 0.0)]
    pub unit_price: f64,
    
    /// 合价
    #[attr(default = 0.0)]
    pub total_price: f64,
    
    /// 备注
    #[attr]
    pub remarks: String,
}

impl {{ node_prefix }}RowNode {
    /// 创建新的{{ description }}行节点
    pub fn new(code: String, name: String, description: String) -> Self {
        Self {
            code,
            name,
            description,
            quantity: 1.0,
            unit_price: 0.0,
            total_price: 0.0,
            remarks: String::new(),
        }
    }
    
    /// 计算合价
    pub fn calculate_total(&mut self) {
        self.total_price = self.quantity * self.unit_price;
    }
    
    /// 设置数量
    pub fn set_quantity(&mut self, quantity: f64) {
        self.quantity = quantity;
        self.calculate_total();
    }
    
    /// 设置单价
    pub fn set_unit_price(&mut self, unit_price: f64) {
        self.unit_price = unit_price;
        self.calculate_total();
    }
}

/// {{ description }}工厂
/// 用于创建{{ description }}相关节点
pub struct {{ node_prefix }}Factory;

impl {{ node_prefix }}Factory {
    /// 创建容器节点
    pub fn create_container(value: String) -> {{ node_prefix }}ContainerNode {
        {{ node_prefix }}ContainerNode { value }
    }
    
    /// 创建行节点
    pub fn create_row(code: String, name: String, description: String) -> {{ node_prefix }}RowNode {
        {{ node_prefix }}RowNode::new(code, name, description)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_{{ names.snake }}_container_node() {
        let container = {{ node_prefix }}Factory::create_container("test".to_string());
        assert_eq!(container.value, "test");
    }

    #[test]
    fn test_{{ names.snake }}_row_node() {
        let mut row = {{ node_prefix }}Factory::create_row(
            "001".to_string(),
            "测试项目".to_string(),
            "测试描述".to_string(),
        );
        
        assert_eq!(row.code, "001");
        assert_eq!(row.name, "测试项目");
        assert_eq!(row.description, "测试描述");
        assert_eq!(row.quantity, 1.0);
        assert_eq!(row.unit_price, 0.0);
        assert_eq!(row.total_price, 0.0);
        
        // 测试价格计算
        row.set_quantity(2.0);
        row.set_unit_price(100.0);
        assert_eq!(row.total_price, 200.0);
    }
}