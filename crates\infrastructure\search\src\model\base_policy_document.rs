/*
CREATE TABLE "base_policy_document" (
  "sequence_nbr" text NOT NULL,
  "area_id" integer(20) NOT NULL,
  "city_name" text(30),
  "pricesource" text(255),
  "name" text(255) NOT NULL,
  "sketch" text(255),
  "release_date" text(12),
  "execute_date" text(12),
  "file_type" integer(4),
  "file_date" text(255),
  "file_url" text(1024),
  "remark" text(255),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" integer(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  "zhyg_level1" real(16,2),
  "zhyg_level2" real(16,2),
  "zhyg_level3" real(16,2)
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};
#[derive(Debug, Serialize, Deserialize)]
pub struct BasePolicyDocument {
    pub sequence_nbr: String,
    pub area_id: Option<i32>,
    pub city_name: Option<String>,
    pub pricesource: Option<String>,
    pub name: Option<String>,
    pub sketch: Option<String>,
    pub release_date: Option<f64>,
    pub execute_date: Option<f64>,
    pub file_type: Option<f64>,
    pub file_date: Option<f64>,
    pub file_url: Option<f64>,
    pub remark: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
    pub zhyg_level1: Option<f64>,
    pub zhyg_level2: Option<f64>,
    pub zhyg_level3: Option<f64>,
}
//crud!(BasePolicyDocument {}, "base_policy_document");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "area_id".to_string(),
            "city_name".to_string(),
            "pricesource".to_string(),
            "name".to_string(),
            "sketch".to_string(),
            "release_date".to_string(),
            "execute_date".to_string(),
            "file_type".to_string(),
            "file_date".to_string(),
            "file_url".to_string(),
            "remark".to_string(),
            "zhyg_level1".to_string(),
            "zhyg_level2".to_string(),
            "zhyg_level3".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "area_id".to_string(),
            "city_name".to_string(),
            "pricesource".to_string(),
            "name".to_string(),
            "sketch".to_string(),
            "release_date".to_string(),
            "execute_date".to_string(),
            "file_type".to_string(),
            "file_date".to_string(),
            "file_url".to_string(),
            "remark".to_string(),
            "zhyg_level1".to_string(),
            "zhyg_level2".to_string(),
            "zhyg_level3".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "area_id".to_string(),
            "city_name".to_string(),
            "pricesource".to_string(),
            "name".to_string(),
            "sketch".to_string(),
            "release_date".to_string(),
            "execute_date".to_string(),
            "file_type".to_string(),
            "file_date".to_string(),
            "file_url".to_string(),
            "remark".to_string(),
            "zhyg_level1".to_string(),
            "zhyg_level2".to_string(),
            "zhyg_level3".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<BasePolicyDocument>("base_policy_document", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BasePolicyDocument>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_policy_document").await?;
    let base_list_index = IndexClient::<BasePolicyDocument> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
