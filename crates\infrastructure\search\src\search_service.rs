use crate::meili::client::BasicSearchService;

use super::model::base_anwen_rate::{BaseAnwenRate, build_service as build_anwen_rate_service, create_index as build_anwen_rate_index};
use super::model::base_anwen_rate_2022::{
    BaseAnwenRate2022, build_service as build_anwen_rate_2022_service, create_index as build_anwen_rate_2022_index,
};
use super::model::base_anzhuang_rate::{BaseAnzhuangRate, build_service as build_anzhuang_rate_service};
use super::model::base_anzhuang_rate_2022::{
    BaseAnzhuangRate2022, build_service as build_anzhuang_rate_2022_service, create_index as build_anzhuang_rate_2022_index,
};
use super::model::base_bzhs_clpb::{BaseBzhsClpb, build_service as build_bzhs_clpb_service, create_index as build_bzhs_clpb_index};
use super::model::base_bzhs_clpb_2022::{
    BaseBzhsClpb2022, build_service as build_bzhs_clpb_2022_service, create_index as build_bzhs_clpb_2022_index,
};
use super::model::base_clpb::{BaseClpb, build_service as build_clpb_service, create_index as build_clpb_index};
use super::model::base_cost_code::{BaseCostCode, build_service as build_cost_code_service, create_index as build_cost_code_index};
use super::model::base_cost_code_2022::{
    BaseCostCode2022, build_service as build_cost_code_2022_service, create_index as build_cost_code_2022_index,
};
use super::model::base_cost_summary::{BaseCostSummary, build_service as build_cost_summary_service, create_index as build_cost_summary_index};
use super::model::base_cost_summary_2022::{
    BaseCostSummary2022, build_service as build_cost_summary_2022_service, create_index as build_cost_summary_2022_index,
};
use super::model::base_cslb::{BaseCslb, build_service as build_cslb_service, create_index as build_cslb_index};
use super::model::base_cslb_2022::{BaseCslb2022, build_service as build_cslb_2022_service, create_index as build_cslb_2022_index};
use super::model::base_de::{BaseDe, build_service as build_de_service, create_index as build_de_index};
use super::model::base_de_2022::{BaseDe2022, build_service as build_de_2022_service, create_index as build_de_2022_index};
use super::model::base_de_2022_bs::{BaseDe2022Bs, build_service as build_de_2022_bs_service, create_index as build_de_2022_bs_index};
use super::model::base_de_2022_bs_standard::{
    BaseDe2022BsStandard, build_service as build_de_2022_bs_standard_service, create_index as build_de_2022_bs_standard_index,
};
use super::model::base_de_awf_relation::{
    BaseDeAwfRelation, build_service as build_de_awf_relation_service, create_index as build_de_awf_relation_index,
};
use super::model::base_de_awf_relation_2022::{
    BaseDeAwfRelation2022, build_service as build_de_awf_relation_2022_service, create_index as build_de_awf_relation_2022_index,
};
use super::model::base_de_az_cost::{BaseDeAzCost, build_service as build_de_az_cost_service, create_index as build_de_az_cost_index};
use super::model::base_de_az_cost_2022::{
    BaseDeAzCost2022, build_service as build_de_az_cost_2022_service, create_index as build_de_az_cost_2022_index,
};
use super::model::base_de_bs::{BaseDeBs, build_service as build_de_bs_service, create_index as build_de_bs_index};
use super::model::base_de_bs_standard::{
    BaseDeBsStandard, build_service as build_de_bs_standard_service, create_index as build_de_bs_standard_index,
};
use super::model::base_de_chapter::{BaseDeChapter, build_service as build_de_chapter_service, create_index as build_de_chapter_index};
use super::model::base_de_chapter_2022::{
    BaseDeChapter2022, build_service as build_de_chapter_2022_service, create_index as build_de_chapter_2022_index,
};
use super::model::base_de_chapter_description::{
    BaseDeChapterDescription, build_service as build_de_chapter_description_service, create_index as build_de_chapter_description_index,
};
use super::model::base_de_chapter_description_2022::{
    BaseDeChapterDescription2022, build_service as build_de_chapter_description_2022_service, create_index as build_de_chapter_description_2022_index,
};
use super::model::base_de_cslb_relation::{
    BaseDeCslbRelation, build_service as build_de_cslb_relation_service, create_index as build_de_cslb_relation_index,
};
use super::model::base_de_cslb_relation_2022::{
    BaseDeCslbRelation2022, build_service as build_de_cslb_relation_2022_service, create_index as build_de_cslb_relation_2022_index,
};
use super::model::base_de_fwxs_cg_relation::{
    BaseDeFwxsCgRelation, build_service as build_de_fwxs_cg_relation_service, create_index as build_de_fwxs_cg_relation_index,
};
use super::model::base_de_fwxs_cg_relation_2022::{
    BaseDeFwxsCgRelation2022, build_service as build_de_fwxs_cg_relation_2022_service, create_index as build_de_fwxs_cg_relation_2022_index,
};
use super::model::base_de_gtf_major_relation::{
    BaseDeGtfMajorRelation, build_service as build_de_gtf_major_relation_service, create_index as build_de_gtf_major_relation_index,
};
use super::model::base_de_gtf_major_relation_2022::{
    BaseDeGtfMajorRelation2022, build_service as build_de_gtf_major_relation_2022_service, create_index as build_de_gtf_major_relation_2022_index,
};
use super::model::base_de_job_content::{
    BaseDeJobContent, build_service as build_de_job_content_service, create_index as build_de_job_content_index,
};
use super::model::base_de_job_content_2022::{
    BaseDeJobContent2022, build_service as build_de_job_content_2022_service, create_index as build_de_job_content_2022_index,
};
use super::model::base_de_library::{BaseDeLibrary, build_service as build_de_library_service, create_index as build_de_library_index};
use super::model::base_de_library_2022::{
    BaseDeLibrary2022, build_service as build_de_library_2022_service, create_index as build_de_library_2022_index,
};
use super::model::base_de_rcj_2022_bs::{BaseDeRcj2022Bs, build_service as build_de_rcj_2022_bs_service, create_index as build_de_rcj_2022_bs_index};
use super::model::base_de_rcj_bs::{BaseDeRcjBs, build_service as build_de_rcj_bs_service, create_index as build_de_rcj_bs_index};
use super::model::base_de_rcj_relation::{
    BaseDeRcjRelation, build_service as build_de_rcj_relation_service, create_index as build_de_rcj_relation_index,
};
use super::model::base_de_rcj_relation_2022::{
    BaseDeRcjRelation2022, build_service as build_de_rcj_relation_2022_service, create_index as build_de_rcj_relation_2022_index,
};
use super::model::base_de_relation::{BaseDeRelation, build_service as build_de_relation_service, create_index as build_de_relation_index};
use super::model::base_de_relation_2022::{
    BaseDeRelation2022, build_service as build_de_relation_2022_service, create_index as build_de_relation_2022_index,
};
use super::model::base_de_relation_variable_coefficient::{
    BaseDeRelationVariableCoefficient, build_service as build_de_relation_variable_coefficient_service,
    create_index as build_de_relation_variable_coefficient_index,
};
use super::model::base_de_relation_variable_coefficient_2022::{
    BaseDeRelationVariableCoefficient2022, build_service as build_de_relation_variable_coefficient_2022_service,
    create_index as build_de_relation_variable_coefficient_2022_index,
};
use super::model::base_de_relation_variable_describe::{
    BaseDeRelationVariableDescribe, build_service as build_de_relation_variable_describe_service,
    create_index as build_de_relation_variable_describe_index,
};
use super::model::base_de_relation_variable_describe_2022::{
    BaseDeRelationVariableDescribe2022, build_service as build_de_relation_variable_describe_2022_service,
    create_index as build_de_relation_variable_describe_2022_index,
};
use super::model::base_de_rule_relation::{
    BaseDeRuleRelation, build_service as build_de_rule_relation_service, create_index as build_de_rule_relation_index,
};
use super::model::base_de_zs_cg_relation::{
    BaseDeZsCgRelation, build_service as build_de_zs_cg_relation_service, create_index as build_de_zs_cg_relation_index,
};
use super::model::base_de_zs_cg_relation_2022::{
    BaseDeZsCgRelation2022, build_service as build_de_zs_cg_relation_2022_service, create_index as build_de_zs_cg_relation_2022_index,
};
use super::model::base_fee_file::{BaseFeeFile, build_service as build_fee_file_service, create_index as build_fee_file_index};
use super::model::base_fee_file_2022::{BaseFeeFile2022, build_service as build_fee_file_2022_service, create_index as build_fee_file_2022_index};
use super::model::base_fee_file_project::{
    BaseFeeFileProject, build_service as build_fee_file_project_service, create_index as build_fee_file_project_index,
};
use super::model::base_fee_file_project_2022::{
    BaseFeeFileProject2022, build_service as build_fee_file_project_2022_service, create_index as build_fee_file_project_2022_index,
};
use super::model::base_fee_file_relation::{
    BaseFeeFileRelation, build_service as build_fee_file_relation_service, create_index as build_fee_file_relation_index,
};
use super::model::base_fee_file_relation_2022::{
    BaseFeeFileRelation2022, build_service as build_fee_file_relation_2022_service, create_index as build_fee_file_relation_2022_index,
};
use super::model::base_gsj_rate::{BaseGsjRate, build_service as build_gsj_rate_service, create_index as build_gsj_rate_index};
use super::model::base_gsj_rate_2022::{BaseGsjRate2022, build_service as build_gsj_rate_2022_service, create_index as build_gsj_rate_2022_index};
use super::model::base_jxpb::{BaseJxpb, build_service as build_jxpb_service, create_index as build_jxpb_index};
use super::model::base_list::{BaseList, build_service as build_list_service, create_index as build_list_index};
use super::model::base_list_2022::{BaseList2022, build_service as build_list_2022_service, create_index as build_list_2022_index};
use super::model::base_list_calc_rule::{
    BaseListCalcRule, build_service as build_base_list_calc_rule_service, create_index as build_base_list_calc_rule_index,
};
use super::model::base_list_calc_rule_2022::{
    BaseListCalcRule2022, build_service as build_base_list_calc_rule_2022_service, create_index as build_base_list_calc_rule_2022_index,
};
use super::model::base_list_de_standard::{
    BaseListDeStandard, build_service as build_base_list_de_standard_service, create_index as build_base_list_de_standard_index,
};
use super::model::base_list_de_standard_2022::{
    BaseListDeStandard2022, build_service as build_base_list_de_standard_2022_service, create_index as build_base_list_de_standard_2022_index,
};
use super::model::base_list_feature::{
    BaseListFeature, build_service as build_base_list_feature_service, create_index as build_base_list_feature_index,
};
use super::model::base_list_feature_2022::{
    BaseListFeature2022, build_service as build_base_list_feature_2022_service, create_index as build_base_list_feature_2022_index,
};
use super::model::base_list_feature_down_pull_menu_order::{
    BaseListFeatureDownPullMenuOrder, build_service as build_base_list_feature_down_pull_menu_order_service,
    create_index as build_base_list_feature_down_pull_menu_order_index,
};
use super::model::base_list_feature_down_pull_menu_order_2022::{
    BaseListFeatureDownPullMenuOrder2022, build_service as build_base_list_feature_down_pull_menu_order_2022_service,
    create_index as build_base_list_feature_down_pull_menu_order_2022_index,
};
use super::model::base_list_job_content::{
    BaseListJobContent, build_service as build_base_list_job_content_service, create_index as build_base_list_job_content_index,
};
use super::model::base_list_job_content_2022::{
    BaseListJobContent2022, build_service as build_base_list_job_content_2022_service, create_index as build_base_list_job_content_2022_index,
};
use super::model::base_list_job_content_quota::{
    BaseListJobContentQuota, build_service as build_base_list_job_content_quota_service, create_index as build_base_list_job_content_quota_index,
};
use super::model::base_list_job_content_quota_2022::{
    BaseListJobContentQuota2022, build_service as build_base_list_job_content_quota_2022_service,
    create_index as build_base_list_job_content_quota_2022_index,
};
use super::model::base_list_library::{
    BaseListLibrary, build_service as build_base_list_library_service, create_index as build_base_list_library_index,
};
use super::model::base_list_library_2022::{
    BaseListLibrary2022, build_service as build_base_list_library_2022_service, create_index as build_base_list_library_2022_index,
};
use super::model::base_manage_rate::{BaseManageRate, build_service as build_base_manage_rate_service, create_index as build_base_manage_rate_index};
use super::model::base_manage_rate_2022::{
    BaseManageRate2022, build_service as build_base_manage_rate_2022_service, create_index as build_base_manage_rate_2022_index,
};
use super::model::base_policy_document::{
    BasePolicyDocument, build_service as build_base_policy_document_service, create_index as build_base_policy_document_index,
};
use super::model::base_policy_document_2022::{
    BasePolicyDocument2022, build_service as build_base_policy_document_2022_service, create_index as build_base_policy_document_2022_index,
};
use super::model::base_rate::{BaseRate, build_service as build_base_rate_service, create_index as build_base_rate_index};
use super::model::base_rate_2022::{BaseRate2022, build_service as build_base_rate_2022_service, create_index as build_base_rate_2022_index};
use super::model::base_rcj::{BaseRcj, build_service as build_base_rcj_service, create_index as build_base_rcj_index};
use super::model::base_rcj_2022::{BaseRcj2022, build_service as build_base_rcj_2022_service, create_index as build_base_rcj_2022_index};
use super::model::base_rcj_class_level::{
    BaseRcjClassLevel, build_service as build_base_rcj_class_level_service, create_index as build_base_rcj_class_level_index,
};
use super::model::base_rule_details_full::{
    BaseRuleDetailsFull, build_service as build_base_rule_details_full_service, create_index as build_base_rule_details_full_index,
};
use super::model::base_rule_details_full_2022::{
    BaseRuleDetailsFull2022, build_service as build_base_rule_details_full_2022_service, create_index as build_base_rule_details_full_2022_index,
};
use super::model::base_rule_file_details::{
    BaseRuleFileDetails, build_service as build_base_rule_file_details_service, create_index as build_base_rule_file_details_index,
};
use super::model::base_rule_file_details_2022::{
    BaseRuleFileDetails2022, build_service as build_base_rule_file_details_2022_service, create_index as build_base_rule_file_details_2022_index,
};
use super::model::base_speciality_de_fee_relation::{
    BaseSpecialityDeFeeRelation, build_service as build_base_speciality_de_fee_relation_service,
    create_index as build_base_speciality_de_fee_relation_index,
};
use super::model::base_speciality_de_fee_relation_2022::{
    BaseSpecialityDeFeeRelation2022, build_service as build_base_speciality_de_fee_relation_2022_service,
    create_index as build_base_speciality_de_fee_relation_2022_index,
};
use super::model::base_specialty_measures::{
    BaseSpecialtyMeasures, build_service as build_base_specialty_measures_service, create_index as build_base_specialty_measures_index,
};
use super::model::base_tax_reform_documents::{
    BaseTaxReformDocuments, build_service as build_base_tax_reform_documents_service, create_index as build_base_tax_reform_documents_index,
};
use super::model::base_tax_reform_documents_2022::{
    BaseTaxReformDocuments2022, build_service as build_base_tax_reform_documents_2022_service,
    create_index as build_base_tax_reform_documents_2022_index,
};
use super::model::base_tax_reform_file::{
    BaseTaxReformFile, build_service as build_base_tax_reform_file_service, create_index as build_base_tax_reform_file_index,
};
use super::model::base_tax_reform_file_2022::{
    BaseTaxReformFile2022, build_service as build_base_tax_reform_file_2022_service, create_index as build_base_tax_reform_file_2022_index,
};
use super::model::base_unit_project_type::{
    BaseUnitProjectType, build_service as build_base_unit_project_type_service, create_index as build_base_unit_project_type_index,
};
use super::model::base_unit_project_type_2022::{
    BaseUnitProjectType2022, build_service as build_base_unit_project_type_2022_service, create_index as build_base_unit_project_type_2022_index,
};
use super::model::conversion_info::{ConversionInfo, build_service as build_conversion_info_service, create_index as build_conversion_info_index};
use super::model::sys_dictionary::{SysDictionary, build_service as build_sys_dictionary_service, create_index as build_sys_dictionary_index};
use super::model::sys_user::{SysUser, build_service as build_sys_user_service, create_index as build_sys_user_index};
use super::model::upc_template::{UpcTemplate, build_service as build_upc_template_service, create_index as build_upc_template_index};

// Macro to simplify creating search services
macro_rules! create_service_entry {
    ($service_name:ident, $build_fn:expr) => {
        let $service_name = $build_fn().await?;
    };
}

// Macro to simplify creating indices
macro_rules! create_indices {
    ($($index_fn:expr),*) => {
        $(
            $index_fn().await?;
        )*
    };
}

pub struct PriceSearchService {
    pub anwen_rate_service: BasicSearchService<BaseAnwenRate>,
    pub anwen_rate_2022_service: BasicSearchService<BaseAnwenRate2022>,
    pub anzhuang_rate_service: BasicSearchService<BaseAnzhuangRate>,
    pub anzhuang_rate_2022_service: BasicSearchService<BaseAnzhuangRate2022>,
    pub bzhs_clpb_service: BasicSearchService<BaseBzhsClpb>,
    pub bzhs_clpb_2022_service: BasicSearchService<BaseBzhsClpb2022>,
    pub clpb_service: BasicSearchService<BaseClpb>,
    pub cost_code_service: BasicSearchService<BaseCostCode>,
    pub cost_code_2022_service: BasicSearchService<BaseCostCode2022>,
    pub cost_summary_service: BasicSearchService<BaseCostSummary>,
    pub cost_summary_2022_service: BasicSearchService<BaseCostSummary2022>,
    pub cslb_service: BasicSearchService<BaseCslb>,
    pub cslb_2022_service: BasicSearchService<BaseCslb2022>,
    pub de_service: BasicSearchService<BaseDe>,
    pub de_2022_service: BasicSearchService<BaseDe2022>,
    pub de_2022_bs_service: BasicSearchService<BaseDe2022Bs>,
    pub de_2022_bs_standard_service: BasicSearchService<BaseDe2022BsStandard>,
    pub de_awf_relation_service: BasicSearchService<BaseDeAwfRelation>,
    pub de_awf_relation_2022_service: BasicSearchService<BaseDeAwfRelation2022>,
    pub de_az_cost_service: BasicSearchService<BaseDeAzCost>,
    pub de_az_cost_2022_service: BasicSearchService<BaseDeAzCost2022>,
    pub de_bs_service: BasicSearchService<BaseDeBs>,
    pub de_bs_standard_service: BasicSearchService<BaseDeBsStandard>,
    pub de_chapter_service: BasicSearchService<BaseDeChapter>,
    pub de_chapter_2022_service: BasicSearchService<BaseDeChapter2022>,
    pub de_chapter_description_service: BasicSearchService<BaseDeChapterDescription>,
    pub de_chapter_description_2022_service: BasicSearchService<BaseDeChapterDescription2022>,
    pub de_cslb_relation_service: BasicSearchService<BaseDeCslbRelation>,
    pub de_cslb_relation_2022_service: BasicSearchService<BaseDeCslbRelation2022>,
    pub de_fwxs_cg_relation_service: BasicSearchService<BaseDeFwxsCgRelation>,
    pub de_fwxs_cg_relation_2022_service: BasicSearchService<BaseDeFwxsCgRelation2022>,
    pub de_gtf_major_relation_service: BasicSearchService<BaseDeGtfMajorRelation>,
    pub de_gtf_major_relation_2022_service: BasicSearchService<BaseDeGtfMajorRelation2022>,
    pub de_job_content_service: BasicSearchService<BaseDeJobContent>,
    pub de_job_content_2022_service: BasicSearchService<BaseDeJobContent2022>,
    pub de_library_service: BasicSearchService<BaseDeLibrary>,
    pub de_library_2022_service: BasicSearchService<BaseDeLibrary2022>,
    pub de_rcj_2022_bs_service: BasicSearchService<BaseDeRcj2022Bs>,
    pub de_rcj_bs_service: BasicSearchService<BaseDeRcjBs>,
    pub de_rcj_relation_service: BasicSearchService<BaseDeRcjRelation>,
    pub de_rcj_relation_2022_service: BasicSearchService<BaseDeRcjRelation2022>,
    pub de_relation_service: BasicSearchService<BaseDeRelation>,
    pub de_relation_2022_service: BasicSearchService<BaseDeRelation2022>,
    pub de_relation_variable_coefficient_service: BasicSearchService<BaseDeRelationVariableCoefficient>,
    pub de_relation_variable_coefficient_2022_service: BasicSearchService<BaseDeRelationVariableCoefficient2022>,
    pub de_relation_variable_describe_service: BasicSearchService<BaseDeRelationVariableDescribe>,
    pub de_relation_variable_describe_2022_service: BasicSearchService<BaseDeRelationVariableDescribe2022>,
    pub de_rule_relation_service: BasicSearchService<BaseDeRuleRelation>,
    pub de_zs_cg_relation_service: BasicSearchService<BaseDeZsCgRelation>,
    pub de_zs_cg_relation_2022_service: BasicSearchService<BaseDeZsCgRelation2022>,
    pub fee_file_service: BasicSearchService<BaseFeeFile>,
    pub fee_file_2022_service: BasicSearchService<BaseFeeFile2022>,
    pub fee_file_project_service: BasicSearchService<BaseFeeFileProject>,
    pub fee_file_project_2022_service: BasicSearchService<BaseFeeFileProject2022>,
    pub fee_file_relation_service: BasicSearchService<BaseFeeFileRelation>,
    pub fee_file_relation_2022_service: BasicSearchService<BaseFeeFileRelation2022>,
    pub gsj_rate_service: BasicSearchService<BaseGsjRate>,
    pub gsj_rate_2022_service: BasicSearchService<BaseGsjRate2022>,
    pub jxpb_service: BasicSearchService<BaseJxpb>,
    pub base_list_service: BasicSearchService<BaseList>,
    pub base_list_2022_service: BasicSearchService<BaseList2022>,
    pub base_list_calc_rule_service: BasicSearchService<BaseListCalcRule>,
    pub base_list_calc_rule_2022_service: BasicSearchService<BaseListCalcRule2022>,
    pub base_list_de_standard_service: BasicSearchService<BaseListDeStandard>,
    pub base_list_de_standard_2022_service: BasicSearchService<BaseListDeStandard2022>,
    pub base_list_feature_service: BasicSearchService<BaseListFeature>,
    pub base_list_feature_2022_service: BasicSearchService<BaseListFeature2022>,
    pub base_list_feature_down_pull_menu_order_service: BasicSearchService<BaseListFeatureDownPullMenuOrder>,
    pub base_list_feature_down_pull_menu_order_2022_service: BasicSearchService<BaseListFeatureDownPullMenuOrder2022>,
    pub base_list_job_content_service: BasicSearchService<BaseListJobContent>,
    pub base_list_job_content_2022_service: BasicSearchService<BaseListJobContent2022>,
    pub base_list_job_content_quota_service: BasicSearchService<BaseListJobContentQuota>,
    pub base_list_job_content_quota_2022_service: BasicSearchService<BaseListJobContentQuota2022>,
    pub base_list_library_service: BasicSearchService<BaseListLibrary>,
    pub base_list_library_2022_service: BasicSearchService<BaseListLibrary2022>,
    pub base_manage_rate_service: BasicSearchService<BaseManageRate>,
    pub base_manage_rate_2022_service: BasicSearchService<BaseManageRate2022>,
    pub base_policy_document_service: BasicSearchService<BasePolicyDocument>,
    pub base_policy_document_2022_service: BasicSearchService<BasePolicyDocument2022>,
    pub base_rate_service: BasicSearchService<BaseRate>,
    pub base_rate_2022_service: BasicSearchService<BaseRate2022>,
    pub base_rcj_service: BasicSearchService<BaseRcj>,
    pub base_rcj_2022_service: BasicSearchService<BaseRcj2022>,
    pub base_rcj_class_level_service: BasicSearchService<BaseRcjClassLevel>,
    pub base_rule_details_full_service: BasicSearchService<BaseRuleDetailsFull>,
    pub base_rule_details_full_2022_service: BasicSearchService<BaseRuleDetailsFull2022>,
    pub base_rule_file_details_service: BasicSearchService<BaseRuleFileDetails>,
    pub base_rule_file_details_2022_service: BasicSearchService<BaseRuleFileDetails2022>,
    pub base_speciality_de_fee_relation_service: BasicSearchService<BaseSpecialityDeFeeRelation>,
    pub base_speciality_de_fee_relation_2022_service: BasicSearchService<BaseSpecialityDeFeeRelation2022>,
    pub base_specialty_measures_service: BasicSearchService<BaseSpecialtyMeasures>,
    pub base_tax_reform_documents_service: BasicSearchService<BaseTaxReformDocuments>,
    pub base_tax_reform_documents_2022_service: BasicSearchService<BaseTaxReformDocuments2022>,
    pub base_tax_reform_file_service: BasicSearchService<BaseTaxReformFile>,
    pub base_tax_reform_file_2022_service: BasicSearchService<BaseTaxReformFile2022>,
    pub base_unit_project_type_service: BasicSearchService<BaseUnitProjectType>,
    pub base_unit_project_type_2022_service: BasicSearchService<BaseUnitProjectType2022>,
    pub conversion_info_service: BasicSearchService<ConversionInfo>,
    pub sys_dictionary_service: BasicSearchService<SysDictionary>,
    pub sys_user_service: BasicSearchService<SysUser>,
    pub upc_template_service: BasicSearchService<UpcTemplate>,
}

impl PriceSearchService {
    pub async fn create_all() -> anyhow::Result<()> {
        create_indices!(
            build_anwen_rate_index,
            build_anwen_rate_2022_index,
            build_anzhuang_rate_2022_index,
            build_bzhs_clpb_index,
            build_bzhs_clpb_2022_index,
            build_clpb_index,
            build_cost_code_index,
            build_cost_code_2022_index,
            build_cost_summary_index,
            build_cost_summary_2022_index,
            build_cslb_index,
            build_cslb_2022_index,
            build_de_index,
            build_de_2022_index,
            build_de_2022_bs_index,
            build_de_2022_bs_standard_index,
            build_de_awf_relation_index,
            build_de_awf_relation_2022_index,
            build_de_az_cost_index,
            build_de_az_cost_2022_index,
            build_de_bs_index,
            build_de_bs_standard_index,
            build_de_chapter_index,
            build_de_chapter_2022_index,
            build_de_chapter_description_index,
            build_de_chapter_description_2022_index,
            build_de_cslb_relation_index,
            build_de_cslb_relation_2022_index,
            build_de_fwxs_cg_relation_index,
            build_de_fwxs_cg_relation_2022_index,
            build_de_gtf_major_relation_index,
            build_de_gtf_major_relation_2022_index,
            build_de_job_content_index,
            build_de_job_content_2022_index,
            build_de_library_index,
            build_de_library_2022_index,
            build_de_rcj_2022_bs_index,
            build_de_rcj_bs_index,
            build_de_rcj_relation_index,
            build_de_rcj_relation_2022_index,
            build_de_relation_index,
            build_de_relation_2022_index,
            build_de_relation_variable_coefficient_index,
            build_de_relation_variable_coefficient_2022_index,
            build_de_relation_variable_describe_index,
            build_de_relation_variable_describe_2022_index,
            build_de_rule_relation_index,
            build_de_zs_cg_relation_index,
            build_de_zs_cg_relation_2022_index,
            build_fee_file_index,
            build_fee_file_2022_index,
            build_fee_file_project_index,
            build_fee_file_project_2022_index,
            build_fee_file_relation_index,
            build_fee_file_relation_2022_index,
            build_gsj_rate_index,
            build_gsj_rate_2022_index,
            build_jxpb_index,
            build_list_index,
            build_list_2022_index,
            build_base_list_calc_rule_index,
            build_base_list_calc_rule_2022_index,
            build_base_list_de_standard_index,
            build_base_list_de_standard_2022_index,
            build_base_list_feature_index,
            build_base_list_feature_2022_index,
            build_base_list_feature_down_pull_menu_order_index,
            build_base_list_feature_down_pull_menu_order_2022_index,
            build_base_list_job_content_index,
            build_base_list_job_content_2022_index,
            build_base_list_job_content_quota_index,
            build_base_list_job_content_quota_2022_index,
            build_base_list_library_index,
            build_base_list_library_2022_index,
            build_base_manage_rate_index,
            build_base_manage_rate_2022_index,
            build_base_policy_document_index,
            build_base_policy_document_2022_index,
            build_base_rate_index,
            build_base_rate_2022_index,
            build_base_rcj_index,
            build_base_rcj_2022_index,
            build_base_rcj_class_level_index,
            build_base_rule_details_full_index,
            build_base_rule_details_full_2022_index,
            build_base_rule_file_details_index,
            build_base_rule_file_details_2022_index,
            build_base_speciality_de_fee_relation_index,
            build_base_speciality_de_fee_relation_2022_index,
            build_base_specialty_measures_index,
            build_base_tax_reform_documents_index,
            build_base_tax_reform_documents_2022_index,
            build_base_tax_reform_file_index,
            build_base_tax_reform_file_2022_index,
            build_base_unit_project_type_index,
            build_base_unit_project_type_2022_index,
            build_conversion_info_index,
            build_sys_dictionary_index,
            build_sys_user_index,
            build_upc_template_index
        );

        Ok(())
    }

    pub async fn create() -> anyhow::Result<Self> {
        create_service_entry!(anwen_rate_service, build_anwen_rate_service);
        create_service_entry!(anwen_rate_2022_service, build_anwen_rate_2022_service);
        create_service_entry!(anzhuang_rate_service, build_anzhuang_rate_service);
        create_service_entry!(anzhuang_rate_2022_service, build_anzhuang_rate_2022_service);
        create_service_entry!(bzhs_clpb_service, build_bzhs_clpb_service);
        create_service_entry!(bzhs_clpb_2022_service, build_bzhs_clpb_2022_service);
        create_service_entry!(clpb_service, build_clpb_service);
        create_service_entry!(cost_code_service, build_cost_code_service);
        create_service_entry!(cost_code_2022_service, build_cost_code_2022_service);
        create_service_entry!(cost_summary_service, build_cost_summary_service);
        create_service_entry!(cost_summary_2022_service, build_cost_summary_2022_service);
        create_service_entry!(cslb_service, build_cslb_service);
        create_service_entry!(cslb_2022_service, build_cslb_2022_service);
        create_service_entry!(de_service, build_de_service);
        create_service_entry!(de_2022_service, build_de_2022_service);
        create_service_entry!(de_2022_bs_service, build_de_2022_bs_service);
        create_service_entry!(de_2022_bs_standard_service, build_de_2022_bs_standard_service);
        create_service_entry!(de_awf_relation_service, build_de_awf_relation_service);
        create_service_entry!(de_awf_relation_2022_service, build_de_awf_relation_2022_service);
        create_service_entry!(de_az_cost_service, build_de_az_cost_service);
        create_service_entry!(de_az_cost_2022_service, build_de_az_cost_2022_service);
        create_service_entry!(de_bs_service, build_de_bs_service);
        create_service_entry!(de_bs_standard_service, build_de_bs_standard_service);
        create_service_entry!(de_chapter_service, build_de_chapter_service);
        create_service_entry!(de_chapter_2022_service, build_de_chapter_2022_service);
        create_service_entry!(de_chapter_description_service, build_de_chapter_description_service);
        create_service_entry!(de_chapter_description_2022_service, build_de_chapter_description_2022_service);
        create_service_entry!(de_cslb_relation_service, build_de_cslb_relation_service);
        create_service_entry!(de_cslb_relation_2022_service, build_de_cslb_relation_2022_service);
        create_service_entry!(de_fwxs_cg_relation_service, build_de_fwxs_cg_relation_service);
        create_service_entry!(de_fwxs_cg_relation_2022_service, build_de_fwxs_cg_relation_2022_service);
        create_service_entry!(de_gtf_major_relation_service, build_de_gtf_major_relation_service);
        create_service_entry!(de_gtf_major_relation_2022_service, build_de_gtf_major_relation_2022_service);
        create_service_entry!(de_job_content_service, build_de_job_content_service);
        create_service_entry!(de_job_content_2022_service, build_de_job_content_2022_service);
        create_service_entry!(de_library_service, build_de_library_service);
        create_service_entry!(de_library_2022_service, build_de_library_2022_service);
        create_service_entry!(de_rcj_2022_bs_service, build_de_rcj_2022_bs_service);
        create_service_entry!(de_rcj_bs_service, build_de_rcj_bs_service);
        create_service_entry!(de_rcj_relation_service, build_de_rcj_relation_service);
        create_service_entry!(de_rcj_relation_2022_service, build_de_rcj_relation_2022_service);
        create_service_entry!(de_relation_service, build_de_relation_service);
        create_service_entry!(de_relation_2022_service, build_de_relation_2022_service);
        create_service_entry!(de_relation_variable_coefficient_service, build_de_relation_variable_coefficient_service);
        create_service_entry!(de_relation_variable_coefficient_2022_service, build_de_relation_variable_coefficient_2022_service);
        create_service_entry!(de_relation_variable_describe_service, build_de_relation_variable_describe_service);
        create_service_entry!(de_relation_variable_describe_2022_service, build_de_relation_variable_describe_2022_service);
        create_service_entry!(de_rule_relation_service, build_de_rule_relation_service);
        create_service_entry!(de_zs_cg_relation_service, build_de_zs_cg_relation_service);
        create_service_entry!(de_zs_cg_relation_2022_service, build_de_zs_cg_relation_2022_service);
        create_service_entry!(fee_file_service, build_fee_file_service);
        create_service_entry!(fee_file_2022_service, build_fee_file_2022_service);
        create_service_entry!(fee_file_project_service, build_fee_file_project_service);
        create_service_entry!(fee_file_project_2022_service, build_fee_file_project_2022_service);
        create_service_entry!(fee_file_relation_service, build_fee_file_relation_service);
        create_service_entry!(fee_file_relation_2022_service, build_fee_file_relation_2022_service);
        create_service_entry!(gsj_rate_service, build_gsj_rate_service);
        create_service_entry!(gsj_rate_2022_service, build_gsj_rate_2022_service);
        create_service_entry!(jxpb_service, build_jxpb_service);
        create_service_entry!(base_list_service, build_list_service);
        create_service_entry!(base_list_2022_service, build_list_2022_service);
        create_service_entry!(base_list_calc_rule_service, build_base_list_calc_rule_service);
        create_service_entry!(base_list_calc_rule_2022_service, build_base_list_calc_rule_2022_service);
        create_service_entry!(base_list_de_standard_service, build_base_list_de_standard_service);
        create_service_entry!(base_list_de_standard_2022_service, build_base_list_de_standard_2022_service);
        create_service_entry!(base_list_feature_service, build_base_list_feature_service);
        create_service_entry!(base_list_feature_2022_service, build_base_list_feature_2022_service);
        create_service_entry!(base_list_feature_down_pull_menu_order_service, build_base_list_feature_down_pull_menu_order_service);
        create_service_entry!(base_list_feature_down_pull_menu_order_2022_service, build_base_list_feature_down_pull_menu_order_2022_service);
        create_service_entry!(base_list_job_content_service, build_base_list_job_content_service);
        create_service_entry!(base_list_job_content_2022_service, build_base_list_job_content_2022_service);
        create_service_entry!(base_list_job_content_quota_service, build_base_list_job_content_quota_service);
        create_service_entry!(base_list_job_content_quota_2022_service, build_base_list_job_content_quota_2022_service);
        create_service_entry!(base_list_library_service, build_base_list_library_service);
        create_service_entry!(base_list_library_2022_service, build_base_list_library_2022_service);
        create_service_entry!(base_manage_rate_service, build_base_manage_rate_service);
        create_service_entry!(base_manage_rate_2022_service, build_base_manage_rate_2022_service);
        create_service_entry!(base_policy_document_service, build_base_policy_document_service);
        create_service_entry!(base_policy_document_2022_service, build_base_policy_document_2022_service);
        create_service_entry!(base_rate_service, build_base_rate_service);
        create_service_entry!(base_rate_2022_service, build_base_rate_2022_service);
        create_service_entry!(base_rcj_service, build_base_rcj_service);
        create_service_entry!(base_rcj_2022_service, build_base_rcj_2022_service);
        create_service_entry!(base_rcj_class_level_service, build_base_rcj_class_level_service);
        create_service_entry!(base_rule_details_full_service, build_base_rule_details_full_service);
        create_service_entry!(base_rule_details_full_2022_service, build_base_rule_details_full_2022_service);
        create_service_entry!(base_rule_file_details_service, build_base_rule_file_details_service);
        create_service_entry!(base_rule_file_details_2022_service, build_base_rule_file_details_2022_service);
        create_service_entry!(base_speciality_de_fee_relation_service, build_base_speciality_de_fee_relation_service);
        create_service_entry!(base_speciality_de_fee_relation_2022_service, build_base_speciality_de_fee_relation_2022_service);
        create_service_entry!(base_specialty_measures_service, build_base_specialty_measures_service);
        create_service_entry!(base_tax_reform_documents_service, build_base_tax_reform_documents_service);
        create_service_entry!(base_tax_reform_documents_2022_service, build_base_tax_reform_documents_2022_service);
        create_service_entry!(base_tax_reform_file_service, build_base_tax_reform_file_service);
        create_service_entry!(base_tax_reform_file_2022_service, build_base_tax_reform_file_2022_service);
        create_service_entry!(base_unit_project_type_service, build_base_unit_project_type_service);
        create_service_entry!(base_unit_project_type_2022_service, build_base_unit_project_type_2022_service);
        create_service_entry!(conversion_info_service, build_conversion_info_service);
        create_service_entry!(sys_dictionary_service, build_sys_dictionary_service);
        create_service_entry!(sys_user_service, build_sys_user_service);
        create_service_entry!(upc_template_service, build_upc_template_service);

        Ok(Self {
            anwen_rate_service,
            anwen_rate_2022_service,
            anzhuang_rate_service,
            anzhuang_rate_2022_service,
            bzhs_clpb_service,
            bzhs_clpb_2022_service,
            clpb_service,
            cost_code_service,
            cost_code_2022_service,
            cost_summary_service,
            cost_summary_2022_service,
            cslb_service,
            cslb_2022_service,
            de_service,
            de_2022_service,
            de_2022_bs_service,
            de_2022_bs_standard_service,
            de_awf_relation_service,
            de_awf_relation_2022_service,
            de_az_cost_service,
            de_az_cost_2022_service,
            de_bs_service,
            de_bs_standard_service,
            de_chapter_service,
            de_chapter_2022_service,
            de_chapter_description_service,
            de_chapter_description_2022_service,
            de_cslb_relation_service,
            de_cslb_relation_2022_service,
            de_fwxs_cg_relation_service,
            de_fwxs_cg_relation_2022_service,
            de_gtf_major_relation_service,
            de_gtf_major_relation_2022_service,
            de_job_content_service,
            de_job_content_2022_service,
            de_library_service,
            de_library_2022_service,
            de_rcj_2022_bs_service,
            de_rcj_bs_service,
            de_rcj_relation_service,
            de_rcj_relation_2022_service,
            de_relation_service,
            de_relation_2022_service,
            de_relation_variable_coefficient_service,
            de_relation_variable_coefficient_2022_service,
            de_relation_variable_describe_service,
            de_relation_variable_describe_2022_service,
            de_rule_relation_service,
            de_zs_cg_relation_service,
            de_zs_cg_relation_2022_service,
            fee_file_service,
            fee_file_2022_service,
            fee_file_project_service,
            fee_file_project_2022_service,
            fee_file_relation_service,
            fee_file_relation_2022_service,
            gsj_rate_service,
            gsj_rate_2022_service,
            jxpb_service,
            base_list_service,
            base_list_2022_service,
            base_list_calc_rule_service,
            base_list_calc_rule_2022_service,
            base_list_de_standard_service,
            base_list_de_standard_2022_service,
            base_list_feature_service,
            base_list_feature_2022_service,
            base_list_feature_down_pull_menu_order_service,
            base_list_feature_down_pull_menu_order_2022_service,
            base_list_job_content_service,
            base_list_job_content_2022_service,
            base_list_job_content_quota_service,
            base_list_job_content_quota_2022_service,
            base_list_library_service,
            base_list_library_2022_service,
            base_manage_rate_service,
            base_manage_rate_2022_service,
            base_policy_document_service,
            base_policy_document_2022_service,
            base_rate_service,
            base_rate_2022_service,
            base_rcj_service,
            base_rcj_2022_service,
            base_rcj_class_level_service,
            base_rule_details_full_service,
            base_rule_details_full_2022_service,
            base_rule_file_details_service,
            base_rule_file_details_2022_service,
            base_speciality_de_fee_relation_service,
            base_speciality_de_fee_relation_2022_service,
            base_specialty_measures_service,
            base_tax_reform_documents_service,
            base_tax_reform_documents_2022_service,
            base_tax_reform_file_service,
            base_tax_reform_file_2022_service,
            base_unit_project_type_service,
            base_unit_project_type_2022_service,
            conversion_info_service,
            sys_dictionary_service,
            sys_user_service,
            upc_template_service,
        })
    }
}
