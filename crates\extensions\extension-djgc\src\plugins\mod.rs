use std::sync::Arc;

use async_trait::async_trait;
use mf_state::{
    error::StateResult,
    plugin::{Plugin, PluginMetadata, PluginSpec, PluginTrait},
    state::State,
    transaction::Transaction,
};

#[derive(Debug)]
pub struct PluginSjgc;
#[async_trait]
impl PluginTrait for PluginSjgc {
    fn metadata(&self) -> PluginMetadata {
        PluginMetadata {
            name: "djgc".to_string(),
            version: "1.0.0".to_string(),
            description: "单价构成插件".to_string(),
            author: "moduforge".to_string(),
            dependencies: vec![],
            conflicts: vec![],
            state_fields: vec![],
            tags: vec![],
        }
    }
    async fn append_transaction(
        &self,
        _trs: &[Transaction],
        _old_state: &State,
        _new_state: &State,
    ) -> StateResult<Option<Transaction>> {
        todo!()
    }
    async fn filter_transaction(
        &self,
        _tr: &Transaction,
        _state: &State,
    ) -> bool {
        todo!()
    }
}
