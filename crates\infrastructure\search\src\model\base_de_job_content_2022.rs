/*
CREATE TABLE "base_de_job_content_2022" (
  "id" text NOT NULL,
  "library_code" text(50),
  "de_id" text(20),
  "de_code" text(255),
  "de_job_content" text(1024),
  "de_name" text(255),
  "rec_user_code" text(32),
  "rec_status" text(4),
  "rec_date" text(20),
  "extend1" text(64),
  "extend2" text(64),
  "extend3" text(64),
  "description" text(255),
  "agency_code" text(64),
  "product_code" text(64),
  "remark" text(255)
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct BaseDeJobContent2022 {
    pub id: String,
    pub library_code: Option<String>,
    pub de_id: Option<String>,
    pub de_code: Option<String>,
    pub de_job_content: Option<String>,
    pub de_name: Option<String>,
    pub rec_user_code: Option<String>,
    pub rec_status: Option<String>,
    pub rec_date: Option<String>,
    pub extend1: Option<String>,
    pub extend2: Option<String>,
    pub extend3: Option<String>,
    pub description: Option<String>,
    pub agency_code: Option<String>,
    pub product_code: Option<String>,
    pub remark: Option<String>,
}
//crud!(BaseDeJobContent2022 {}, "base_de_job_content_2022");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("id".to_string()),
        searchable_attributes: Some(vec![
            "library_code".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_job_content".to_string(),
            "de_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "remark".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "library_code".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_job_content".to_string(),
            "de_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "remark".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "library_code".to_string(),
            "de_id".to_string(),
            "de_code".to_string(),
            "de_job_content".to_string(),
            "de_name".to_string(),
            "rec_user_code".to_string(),
            "rec_status".to_string(),
            "rec_date".to_string(),
            "extend1".to_string(),
            "extend2".to_string(),
            "extend3".to_string(),
            "description".to_string(),
            "agency_code".to_string(),
            "product_code".to_string(),
            "remark".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };

    // 创建索引
    let _ = client.create_index::<BaseDeJobContent2022>("base_de_job_content_2022", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<BaseDeJobContent2022>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("base_de_job_content_2022").await?;
    let base_de_job_content_2022_index = IndexClient::<BaseDeJobContent2022> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_de_job_content_2022_index);
    Ok(search_service)
}
