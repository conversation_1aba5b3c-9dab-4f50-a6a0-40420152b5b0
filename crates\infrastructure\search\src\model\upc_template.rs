/*
CREATE TABLE "upc_template" (
  "sequence_nbr" TEXT NOT NULL,
  "qf_code" TEXT,
  "standard" TEXT,
  "sort" TEXT,
  "type" TEXT,
  "code" TEXT,
  "name" TEXT,
  "caculate_base" TEXT,
  "desc" TEXT,
  "rate" TEXT DEFAULT '',
  "type_code" TEXT,
  "remark" TEXT DEFAULT '',
  PRIMARY KEY ("sequence_nbr")
);
*/

use crate::meili::{
    MEILI_SEARCH_CLIENT,
    client::{BasicSearchService, IndexClient},
    model::IndexSettings,
};
//use rbatis::crud;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct UpcTemplate {
    pub sequence_nbr: String,
    pub qf_code: Option<String>,
    pub standard: Option<String>,
    pub sort: String,
    pub r#type: Option<String>,
    pub code: Option<String>,
    pub name: Option<String>,
    pub caculate_base: Option<String>,
    pub desc: Option<String>,
    pub rate: Option<String>,
    pub type_code: Option<String>,
    pub remark: Option<String>,
}
//crud!(UpcTemplate {}, "upc_template");
pub async fn create_index() -> anyhow::Result<()> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 创建索引设置
    let settings = IndexSettings {
        primary_key: Some("sequence_nbr".to_string()),
        searchable_attributes: Some(vec![
            "qf_code".to_string(),
            "standard".to_string(),
            "sort".to_string(),
            "type".to_string(),
            "code".to_string(),
            "name".to_string(),
            "caculate_base".to_string(),
            "desc".to_string(),
            "rate".to_string(),
            "type_code".to_string(),
            "remark".to_string(),
        ]),
        filterable_attributes: Some(vec![
            "qf_code".to_string(),
            "standard".to_string(),
            "sort".to_string(),
            "type".to_string(),
            "code".to_string(),
            "name".to_string(),
            "caculate_base".to_string(),
            "desc".to_string(),
            "rate".to_string(),
            "type_code".to_string(),
            "remark".to_string(),
        ]),
        sortable_attributes: Some(vec![
            "qf_code".to_string(),
            "standard".to_string(),
            "sort".to_string(),
            "type".to_string(),
            "code".to_string(),
            "name".to_string(),
            "caculate_base".to_string(),
            "desc".to_string(),
            "rate".to_string(),
            "type_code".to_string(),
            "remark".to_string(),
        ]),
        displayed_attributes: None, // 显示所有字段
    };
    // 创建索引
    let _ = client.create_index::<UpcTemplate>("upc_template", Some(settings)).await?;
    Ok(())
}

pub async fn build_service() -> anyhow::Result<BasicSearchService<UpcTemplate>> {
    let client = MEILI_SEARCH_CLIENT.get();
    // 获取索引
    let index = client.get_index("upc_template").await?;
    let base_list_index = IndexClient::<UpcTemplate> { index, _phantom: std::marker::PhantomData };
    // 创建搜索服务
    let search_service = BasicSearchService::new(base_list_index);
    Ok(search_service)
}
