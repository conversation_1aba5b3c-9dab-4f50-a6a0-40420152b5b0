/// {{ description }}字段定义
/// 定义{{ description }}相关的字段类型和验证规则

use serde::{Deserialize, Serialize};
use std::fmt;

/// 字段类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum {{ node_prefix }}FieldType {
    /// 文本字段
    Text,
    /// 数字字段
    Number,
    /// 百分比字段
    Percentage,
    /// 货币字段
    Currency,
    /// 日期字段
    Date,
    /// 枚举字段
    Enum(Vec<String>),
}

/// 字段定义结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct {{ node_prefix }}Field {
    /// 字段名称
    pub name: String,
    /// 字段标签
    pub label: String,
    /// 字段类型
    pub field_type: {{ node_prefix }}FieldType,
    /// 是否必填
    pub required: bool,
    /// 默认值
    pub default_value: Option<String>,
    /// 字段描述
    pub description: Option<String>,
}

impl {{ node_prefix }}Field {
    /// 创建新的字段定义
    pub fn new(
        name: String,
        label: String,
        field_type: {{ node_prefix }}FieldType,
        required: bool,
    ) -> Self {
        Self {
            name,
            label,
            field_type,
            required,
            default_value: None,
            description: None,
        }
    }
    
    /// 设置默认值
    pub fn with_default(mut self, default_value: String) -> Self {
        self.default_value = Some(default_value);
        self
    }
    
    /// 设置描述
    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }
    
    /// 验证字段值
    pub fn validate(&self, value: &str) -> Result<(), String> {
        if self.required && value.is_empty() {
            return Err(format!("字段 {} 是必填项", self.label));
        }
        
        match &self.field_type {
            {{ node_prefix }}FieldType::Number => {
                value.parse::<f64>()
                    .map_err(|_| format!("字段 {} 必须是有效数字", self.label))?;
            }
            {{ node_prefix }}FieldType::Percentage => {
                let num: f64 = value.parse()
                    .map_err(|_| format!("字段 {} 必须是有效百分比", self.label))?;
                if num < 0.0 || num > 100.0 {
                    return Err(format!("字段 {} 百分比必须在0-100之间", self.label));
                }
            }
            {{ node_prefix }}FieldType::Currency => {
                let num: f64 = value.parse()
                    .map_err(|_| format!("字段 {} 必须是有效金额", self.label))?;
                if num < 0.0 {
                    return Err(format!("字段 {} 金额不能为负数", self.label));
                }
            }
            {{ node_prefix }}FieldType::Enum(options) => {
                if !options.contains(&value.to_string()) {
                    return Err(format!(
                        "字段 {} 的值必须是以下选项之一: {:?}",
                        self.label, options
                    ));
                }
            }
            _ => {} // 其他类型暂不验证
        }
        
        Ok(())
    }
}

impl fmt::Display for {{ node_prefix }}FieldType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            {{ node_prefix }}FieldType::Text => write!(f, "文本"),
            {{ node_prefix }}FieldType::Number => write!(f, "数字"),
            {{ node_prefix }}FieldType::Percentage => write!(f, "百分比"),
            {{ node_prefix }}FieldType::Currency => write!(f, "货币"),
            {{ node_prefix }}FieldType::Date => write!(f, "日期"),
            {{ node_prefix }}FieldType::Enum(options) => {
                write!(f, "枚举({})", options.join(", "))
            }
        }
    }
}

/// 预定义的字段集合
pub struct {{ node_prefix }}Fields;

impl {{ node_prefix }}Fields {
    /// 获取标准字段定义
    pub fn standard_fields() -> Vec<{{ node_prefix }}Field> {
        vec![
            {{ node_prefix }}Field::new(
                "code".to_string(),
                "编码".to_string(),
                {{ node_prefix }}FieldType::Text,
                true,
            ).with_description("项目编码，用于唯一标识".to_string()),
            
            {{ node_prefix }}Field::new(
                "name".to_string(),
                "名称".to_string(),
                {{ node_prefix }}FieldType::Text,
                true,
            ).with_description("项目名称".to_string()),
            
            {{ node_prefix }}Field::new(
                "quantity".to_string(),
                "数量".to_string(),
                {{ node_prefix }}FieldType::Number,
                false,
            ).with_default("1.0".to_string()),
            
            {{ node_prefix }}Field::new(
                "unit_price".to_string(),
                "单价".to_string(),
                {{ node_prefix }}FieldType::Currency,
                false,
            ).with_default("0.0".to_string()),
        ]
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_field_validation() {
        let field = {{ node_prefix }}Field::new(
            "test".to_string(),
            "测试字段".to_string(),
            {{ node_prefix }}FieldType::Number,
            true,
        );
        
        // 测试必填验证
        assert!(field.validate("").is_err());
        
        // 测试数字验证
        assert!(field.validate("abc").is_err());
        assert!(field.validate("123.45").is_ok());
    }
    
    #[test]
    fn test_percentage_validation() {
        let field = {{ node_prefix }}Field::new(
            "rate".to_string(),
            "费率".to_string(),
            {{ node_prefix }}FieldType::Percentage,
            false,
        );
        
        assert!(field.validate("50.5").is_ok());
        assert!(field.validate("101").is_err());
        assert!(field.validate("-5").is_err());
    }
}